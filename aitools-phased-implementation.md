# AItools Directory - Phased Implementation Guide

This guide breaks down the AItools directory project into manageable phases with specific prompts for each tab/session of <PERSON> or <PERSON><PERSON>GP<PERSON>. The goal is to generate complete, production-ready code that integrates seamlessly.

## Overview of Phases and Tabs

1. **Project Setup & Core Frontend (Tabs 0-2)**
   - Tab 0: Project Structure & Configuration
   - Tab 1: Core UI Components
   - Tab 2: Homepage & Navigation

2. **Backend & API Development (Tabs 3-5)**
   - Tab 3: Backend Structure & Authentication
   - Tab 4: Database Models & API Routes
   - Tab 5: Data Collection Services

3. **Advanced Frontend Features (Tabs 6-8)**
   - Tab 6: Search & Filter Implementation
   - Tab 7: Tool Detail Pages
   - Tab 8: User Dashboard & Profiles

4. **Content & Monetization (Tabs 9-10)**
   - Tab 9: Blog System Implementation
   - Tab 10: Monetization Features (AdSense & Affiliate)

5. **Mobile Apps & Integration (Tabs 11-13)**
   - Tab 11: React Native Base Setup
   - Tab 12: Mobile App Core Features
   - Tab 13: Cross-platform Syncing

## Detailed Prompts for Each Tab

---

### Tab 0: Project Structure & Configuration

**Prompt:**
```
I need complete production-ready code for a Next.js project structure for an AI tools directory named "AItools". This should be a comprehensive setup with all necessary configurations. Requirements:

1. Set up a Next.js 14+ project with TypeScript, Tailwind CSS, and best practices for folder structure
2. Include all necessary configuration files (package.json, tsconfig.json, next.config.js, tailwind.config.js, etc.)
3. Set up environment variables structure with a sample .env.example file
4. Configure ESLint and Prettier for code quality
5. Set up basic folder structure including:
   - /app - for pages using Next.js App Router
   - /components - for UI components
   - /lib - for utility functions
   - /types - for TypeScript types
   - /services - for API and external service connections
   - /styles - for global CSS and Tailwind customizations
   - /public - for static assets
6. Include proper README.md with setup instructions and project overview
7. Set up authentication state management using NextAuth.js
8. Configure basic SEO setup with next-seo
9. Set up a responsive layout component with header and footer
10. Configure dark/light mode support

Provide ALL the complete code for each file with proper comments. Do not use placeholder comments like "// Add more here" - include actual, complete implementation. The code should be ready to be copied directly into files and run without additional coding required.
```

**What to collect for Tab 1:**
- Project structure details
- Configuration files (package.json with exact dependencies, etc.)
- Environment variable structure

---

### Tab 1: Core UI Components

**Prompt:**
```
Build all core UI components for AItools directory using React, Next.js 14+, and Tailwind CSS. I need production-ready, fully implemented components with no placeholders or "to be implemented" comments. Reference the following project structure already created:

[PASTE RELEVANT PROJECT STRUCTURE FROM TAB 0]

Create the following components with full implementation:
1. Button component with multiple variants (primary, secondary, outline, ghost)
2. Card components for displaying AI tools (include image, title, description, categories, pricing badges)
3. Navigation components (header with responsive mobile menu, footer with proper site sections)
4. Modal component for dialogs and popups
5. Form components (input, select, checkbox, radio, textarea with proper validation)
6. Alert/notification component for system messages
7. Dropdown and multi-select components for filtering
8. Pagination component for search results
9. Tabs component for organizing content
10. Loading states and skeleton loaders for UI elements
11. Rating/review display component
12. A homepage hero section with search bar
13. Category browsing section with visual indicators for AI tool types

All components should:
- Be fully responsive
- Have proper TypeScript typing
- Include accessibility features (ARIA attributes, keyboard navigation)
- Support dark/light mode as configured in the base project
- Use Tailwind CSS for styling with consistent design tokens
- Include proper animations/transitions using Framer Motion
- Have comprehensive prop validation

Provide complete code for all components, organized in logical files/folders following the project structure.
```

**What to collect for Tab 2:**
- Components folder structure
- Key components that will be used in the homepage

---

### Tab 2: Homepage & Navigation

**Prompt:**
```
Using the project structure and components created previously, I need complete code for the homepage and navigation system for the AItools directory website. This should be fully implemented with no placeholders.

[PASTE RELEVANT PROJECT STRUCTURE AND COMPONENT IMPORTS FROM PREVIOUS TABS]

Create the following pages and features:
1. Complete homepage with:
   - Hero section with main search functionality
   - Featured/trending AI tools section
   - Category browsing with visual indicators
   - Latest blog posts preview
   - Newsletter signup component
   - "How it works" section

2. Complete navigation system:
   - Responsive header with logo, navigation links, search icon, and authentication buttons
   - Dropdown menus for categories
   - Mobile navigation drawer with hamburger menu
   - User menu dropdown when logged in (profile, saved tools, settings, logout)
   - Complete footer with site sections, social links, and legal pages

3. Basic page layouts for:
   - Category pages structure
   - Search results page structure
   - Basic tool listing page structure

Implement all client-side functionality including:
- Navigation state management
- Responsive behavior for all screen sizes
- Search input with suggested searches
- Dark/light mode toggle
- Mobile menu interactions

The code should be completely production-ready with all necessary imports, type definitions, and proper Next.js 14+ App Router implementation. Include proper metadata for SEO.
```

**What to collect for Tab 3:**
- Homepage layout and structure
- Navigation elements and structure
- Page routing approach

---

### Tab 3: Backend Structure & Authentication

**Prompt:**
```
I need production-ready backend code for the AItools directory, using Next.js API routes or App Router server components with a focus on authentication and user management. No placeholders or incomplete code.

[PASTE RELEVANT PROJECT STRUCTURE FROM PREVIOUS TABS]

Implement the following:

1. Complete user authentication system using NextAuth.js:
   - Email/password authentication
   - Social login (Google, GitHub)
   - JWT session handling
   - Protected routes/API endpoints
   - User registration flow
   - Password reset functionality
   - Remember me functionality

2. User management:
   - User profiles with editable fields
   - User preferences storage
   - Email verification process
   - Role-based access control (admin, moderator, user)

3. Backend structure:
   - Database connection configuration (MongoDB with Mongoose)
   - API route organization
   - Rate limiting
   - Error handling middleware
   - Logging system
   - API response standardization

4. Security implementation:
   - CSRF protection
   - Input validation
   - Password hashing
   - Security headers
   - API route protection

Provide all necessary files, configs, and implementations as complete code that can be directly used in production. Include proper error handling, validation, and security best practices. All TypeScript types should be properly defined.
```

**What to collect for Tab 4:**
- Authentication implementation details
- API route structure
- Database connection configuration

---

### Tab 4: Database Models & API Routes

**Prompt:**
```
I need complete, production-ready code for database models and API routes for the AItools directory. This should work seamlessly with the authentication system already implemented.

[PASTE RELEVANT PROJECT STRUCTURE AND AUTHENTICATION DETAILS FROM PREVIOUS TABS]

Create the following database models (using Mongoose schemas for MongoDB):

1. User model:
   - Basic details (name, email, password, profile picture)
   - Saved/favorite tools
   - User preferences
   - Activity history
   - Authentication-related fields

2. AI Tool model:
   - Complete tool details (name, description, features, pricing, etc.)
   - Categories and tags
   - Ratings and review statistics
   - Developer/company information
   - Timestamps and update tracking
   - SEO-related fields

3. Category model:
   - Name, description, icon/image
   - Parent/child relationships for subcategories
   - Associated tools count

4. Review model:
   - Rating values
   - Review text
   - User reference
   - Tool reference
   - Helpfulness votes
   - Timestamps

5. Blog Post model:
   - Title, content, author
   - Featured image
   - Categories and tags
   - SEO fields
   - Publication status and dates

Implement complete API routes for:
1. User operations (CRUD)
2. Tools operations (CRUD, search, filtering)
3. Categories operations (CRUD)
4. Reviews operations (CRUD)
5. Blog operations (CRUD)
6. Admin operations for managing content

Include proper:
- Request validation
- Response formatting
- Error handling
- Pagination support
- Sorting and filtering options
- Full-text search capabilities
- Related data population
- Permission checks

Provide complete TypeScript interfaces for all models and response types. Code should be ready to use without additional implementation.
```

**What to collect for Tab 5:**
- Database models structure
- API routes implementation
- Data relationships

---

### Tab 5: Data Collection Services

**Prompt:**
```
I need complete, production-ready code for data collection services to gather AI tool information from external sources for the AItools directory. These services should work with the database models and API routes already created.

[PASTE RELEVANT DATABASE MODELS AND API ROUTES FROM PREVIOUS TABS]

Create the following services with full implementation:

1. Web scraping service:
   - Configure Puppeteer or Cheerio for ethical web scraping
   - Implement scrapers for major AI tool directories and websites
   - Include proper rate limiting and robots.txt respect
   - Set up data cleaning and normalization
   - Implement duplicate detection
   - Create scheduled job system for regular updates

2. API integration services:
   - Connect to Product Hunt API for new AI tools
   - GitHub API integration for open-source AI projects
   - Hugging Face API integration for ML models
   - Other relevant API integrations

3. Manual data entry system:
   - Admin interface for adding/editing tools
   - Bulk import functionality
   - Data validation rules

4. Data processing pipeline:
   - Normalization of data from different sources
   - Category assignment based on descriptions
   - Feature extraction and tagging
   - Image processing and optimization
   - Data quality scoring

5. Update tracking system:
   - Version history for tool data
   - Change detection mechanisms
   - Update scheduling based on data freshness

Include all necessary utility functions, type definitions, configuration settings, and example usage. Code should handle errors gracefully, include logging, and follow best practices for asynchronous operations. Provide complete implementation details without placeholders.
```

**What to collect for Tab 6:**
- Data collection methods
- Integration points with database
- Scheduling approach

---

### Tab 6: Search & Filter Implementation

**Prompt:**
```
I need complete, production-ready code for an advanced search and filter system for the AItools directory. This should integrate with the previously created components, database models, and API routes.

[PASTE RELEVANT COMPONENTS AND API ROUTES FROM PREVIOUS TABS]

Implement the following search and filter features:

1. Full-text search system:
   - Client-side search component with typeahead/autocomplete
   - Server-side search implementation with MongoDB text indexes
   - Relevance scoring and result ranking
   - Search history storage for logged-in users
   - Search analytics tracking

2. Advanced filtering system:
   - Multiple filter types (categories, pricing models, features, etc.)
   - Combination of filters with AND/OR logic
   - Filter state management and URL parameters
   - Filter persistence between sessions
   - Mobile-friendly filter UI

3. Search results page:
   - Responsive grid/list views with toggle
   - Pagination implementation
   - Sort options (relevance, popularity, newest)
   - Results count and summary
   - No-results state with suggestions

4. Performance optimizations:
   - Debounced search inputs
   - Cached results for common searches
   - Progressive loading of results
   - Search index optimization

5. Recommendation features:
   - "Similar tools" suggestions
   - "People also searched for" section
   - Recently viewed tools tracking

Include all necessary components, API routes, state management, and CSS. The implementation should be responsive, accessible, and performance-optimized. Provide complete code without placeholders or "to be implemented" comments.
```

**What to collect for Tab 7:**
- Search implementation approach
- Filter components
- State management patterns

---

### Tab 7: Tool Detail Pages

**Prompt:**
```
I need complete, production-ready code for the AI tool detail pages for the AItools directory. This should integrate with all previously implemented components, models, and services.

[PASTE RELEVANT COMPONENTS, MODELS, AND API ROUTES FROM PREVIOUS TABS]

Implement the following for the tool detail pages:

1. Complete tool detail page:
   - Hero section with tool name, logo, screenshots
   - Quick facts and badges (pricing, categories, ratings)
   - Detailed description with proper formatting
   - Feature list with icons
   - Pricing tables/information
   - Integration options
   - User ratings and reviews section
   - Similar/alternative tools section

2. Interactive elements:
   - Save/favorite button
   - Share functionality (social media, copy link)
   - "Try it" button linking to the tool
   - Report incorrect information feature
   - Review submission form for authenticated users

3. Media handling:
   - Image gallery with lightbox
   - Video embed support
   - Screenshot carousel

4. Additional information tabs:
   - Features tab with detailed breakdown
   - Pricing tab with plan comparison
   - Reviews tab with filtering options
   - Alternatives tab with comparisons
   - Updates tab showing version history

5. SEO optimization:
   - Structured data (JSON-LD)
   - Dynamic meta tags
   - Open Graph and Twitter card data
   - Canonical URLs

Include all necessary components, styling, client-side functionality, and server-side data fetching. The implementation should be responsive, accessible, and performance-optimized. Provide complete code without placeholders.
```

**What to collect for Tab 8:**
- Tool detail page structure
- Dynamic routing implementation
- Data fetching patterns

---

### Tab 8: User Dashboard & Profiles

**Prompt:**
```
I need complete, production-ready code for user dashboards and profiles for the AItools directory. This should seamlessly integrate with the authentication system and other components already implemented.

[PASTE RELEVANT AUTHENTICATION AND COMPONENT DETAILS FROM PREVIOUS TABS]

Implement the following user dashboard features:

1. User dashboard main page:
   - Overview of saved tools
   - Recent activity feed
   - Personalized tool recommendations
   - Quick actions (save new tool, write review, etc.)
   - Usage statistics and insights

2. Saved/favorite tools management:
   - Grid/list view of saved tools
   - Collection/folder organization
   - Filtering and sorting options
   - Bulk actions (remove, categorize)
   - Export functionality

3. User profile management:
   - Personal information editing
   - Profile picture upload and cropping
   - Password changing
   - Email preferences
   - Notification settings
   - Account deletion process

4. Reviews management:
   - List of submitted reviews
   - Edit/delete functionality
   - Review metrics (helpfulness votes, etc.)
   - Draft reviews system

5. Public profile pages:
   - User reviews showcase
   - Contribution statistics
   - Optional public collections
   - Privacy controls

Include all necessary components, forms with validation, state management, API integration, and responsive styling. Implement proper loading states, error handling, and success notifications. The code should be complete and production-ready without placeholders.
```

**What to collect for Tab 9:**
- Dashboard structure
- Profile management features
- State management patterns

---

### Tab 9: Blog System Implementation

**Prompt:**
```
I need complete, production-ready code for a blog system for the AItools directory. This should integrate with the existing project structure and authentication system.

[PASTE RELEVANT PROJECT STRUCTURE AND MODELS FROM PREVIOUS TABS]

Implement the following blog features:

1. Blog listing page:
   - Featured posts section with large cards
   - Category filtering
   - Tag cloud
   - Pagination
   - Search functionality
   - Author filters
   - Sort options (newest, popular)

2. Blog post detail page:
   - Rich article formatting
   - Author information
   - Published date and read time
   - Category and tag display
   - Related posts section
   - Social sharing buttons
   - "Subscribe to newsletter" component

3. Blog comment system:
   - Comment submission for authenticated users
   - Threaded replies
   - Comment moderation tools
   - Upvoting/liking comments
   - Markdown support in comments

4. Admin content management:
   - Blog post editor with rich text capabilities
   - Draft saving and preview
   - SEO fields (meta title, description)
   - Featured image management
   - Publishing scheduling
   - Revision history

5. SEO optimizations:
   - Sitemap generation
   - Structured data markup
   - Open Graph and Twitter card data
   - Canonical URL handling
   - Category and tag archives with proper metadata

Include all necessary components, database models, API routes, and styling. Implement proper state management, form handling, and data fetching. The code should be completely ready for production without placeholders.
```

**What to collect for Tab 10:**
- Blog system architecture
- Content management approach
- SEO implementation details

---

### Tab 10: Monetization Features

**Prompt:**
```
I need complete, production-ready code for implementing monetization features for the AItools directory, specifically Google AdSense integration and affiliate marketing functionality.

[PASTE RELEVANT PROJECT STRUCTURE AND COMPONENTS FROM PREVIOUS TABS]

Implement the following monetization features:

1. Google AdSense integration:
   - Ad component with multiple size formats
   - Responsive ad units
   - Ad placement strategy (in-content, sidebar, etc.)
   - Ad blockers detection (optional message)
   - Performance tracking and reporting
   - Lazy loading for better performance
   - A/B testing capabilities for ad placements

2. Affiliate marketing system:
   - Affiliate link generation for tools
   - Click tracking implementation
   - Conversion attribution
   - Performance dashboard for admins
   - Custom parameters for different campaigns
   - UTM parameter handling

3. Sponsored listings:
   - Featured tool placement system
   - Sponsored badge/indicator
   - Admin interface for managing sponsored tools
   - Scheduling system for campaigns
   - Performance metrics for sponsors

4. Premium features (subscription model):
   - Subscription tiers definition
   - Payment processing with Stripe
   - User subscription management
   - Access control for premium features
   - Subscription status indicators
   - Upgrade/downgrade flows

5. Analytics dashboard:
   - Revenue tracking by source
   - User engagement metrics
   - Conversion funnel visualization
   - Export functionality for reports

Include all necessary components, API routes, database models, external service integrations, and styling. The implementation should follow best practices for user experience and transparency. Provide complete code without placeholders.
```

**What to collect for Tab 11:**
- AdSense implementation details
- Affiliate system architecture
- Payment processing approach

---

### Tab 11: React Native Base Setup

**Prompt:**
```
I need complete, production-ready code for setting up a React Native mobile app project for the AItools directory that will work on both iOS and Android. This should align with the web version's functionality and design.

[PASTE RELEVANT PROJECT STRUCTURE AND DESIGN DETAILS FROM PREVIOUS TABS]

Implement the following React Native project setup:

1. Project initialization and configuration:
   - React Native setup with TypeScript
   - Navigation system using React Navigation
   - State management setup (Redux or Context API)
   - Styling approach (styled-components or React Native Paper)
   - Icon and splash screen setup
   - Environment configuration for dev/prod

2. Authentication implementation:
   - Login/registration screens
   - Social authentication integration
   - Secure token storage
   - Biometric authentication option
   - Session management

3. Core components matching web version:
   - Button component with variants
   - Card components for tools
   - Form elements (inputs, selectors, etc.)
   - Navigation components (tab bar, drawer)
   - Modal and dialog components
   - Loading states and indicators

4. Responsive layouts:
   - Screen dimension handling
   - Orientation change support
   - Different device size adaptation
   - Safe area handling

5. Native functionality:
   - Push notification setup
   - Deep linking configuration
   - Offline mode preparation
   - File/image handling
   - Permissions management

Include all necessary configuration files, component definitions, navigation setup, and styling. The code should follow React Native best practices for performance and UX. Provide complete implementation without placeholders.
```

**What to collect for Tab 12:**
- React Native project structure
- Authentication implementation
- Navigation patterns

---

### Tab 12: Mobile App Core Features

**Prompt:**
```
I need complete, production-ready code for the core features of the AItools directory mobile app using React Native. This should build upon the base setup already implemented.

[PASTE RELEVANT REACT NATIVE PROJECT STRUCTURE FROM PREVIOUS TAB]

Implement the following core mobile app features:

1. Home screen implementation:
   - Featured tools carousel
   - Category browsing with icons
   - Recent searches
   - Trending tools section
   - Pull-to-refresh functionality
   - Bottom tab navigation

2. Search and discovery:
   - Search screen with filters
   - Voice search capability
   - Recent searches history
   - Search suggestions
   - Filter persistence
   - Results with infinite scrolling

3. Tool detail screens:
   - Tool information display
   - Screenshot gallery with pinch-to-zoom
   - Save/favorite functionality
   - Share options native to each platform
   - Rating display and submission
   - "Open website" with in-app browser

4. User profile and saved tools:
   - Profile screen with user information
   - Saved tools management
   - Settings screen
   - Notification preferences
   - Dark/light mode toggle
   - Language selection

5. Offline capabilities:
   - Offline mode for saved tools
   - Caching strategy
   - Sync mechanism when back online
   - Offline indicator

Include all necessary screens, components, navigation, API integration, and state management. Implement proper loading states, error handling, and platform-specific adaptations where needed. The code should be complete and ready for production without placeholders.
```

**What to collect for Tab 13:**
- Mobile screen implementations
- API integration approach
- Offline capabilities

---

### Tab 13: Cross-platform Syncing

**Prompt:**
```
I need complete, production-ready code for implementing cross-platform data synchronization between the web platform and mobile apps for the AItools directory.

[PASTE RELEVANT WEB AND MOBILE CODE STRUCTURES FROM PREVIOUS TABS]

Implement the following cross-platform synchronization features:

1. Authentication sync:
   - Single sign-on across platforms
   - Auth token management
   - Session expiration handling
   - Cross-device logout capability

2. User data synchronization:
   - Saved/favorite tools syncing
   - User preferences syncing
   - Profile information syncing
   - Activity history across devices

3. Real-time synchronization:
   - WebSocket implementation for real-time updates
   - Conflict resolution strategy
   - Offline changes queuing
   - Sync status indicators

4. Push notification system:
   - Notification preferences syncing
   - Cross-platform notification delivery
   - Notification actions consistency
   - Notification read status syncing

5. Analytics and tracking:
   - Unified user journey tracking
   - Cross-platform event logging
   - Device information collection
   - Usage patterns analysis

Include all necessary backend services, API endpoints, mobile implementations, and web components. Implement proper error handling, retry mechanisms, and conflict resolution. The code should be complete and production-ready without placeholders.
```

---

## Integration Process

### Step 1: Repository Setup
1. Create a new GitHub repository (or your preferred Git hosting)
2. Initialize with the project structure from Tab 0
3. Create an organized folder structure matching the specifications

### Step 2: Incremental Integration Process
Follow this process for each tab:

1. Create a new branch for each tab/feature set
2. Implement the code generated from each tab's prompt
3. Test the implementation in isolation
4. Create a pull request for review
5. Merge to the main branch once validated

### Integration Order and Dependencies

| Tab | Depends On | Files to Share |
|-----|------------|----------------|
| Tab 0 | None | Project structure, configuration files |
| Tab 1 | Tab 0 | Component structure, basic utilities |
| Tab 2 | Tab 1 | Page components, routing structure |
| Tab 3 | Tab 0 | Authentication configuration, API structure |
| Tab 4 | Tab 3 | Database models, API endpoints |
| Tab 5 | Tab 4 | Data collection services |
| Tab 6 | Tab 2, Tab 4 | Search components, API integration |
| Tab 7 | Tab 6 | Detail page structure, data fetching |
| Tab 8 | Tab 3, Tab 7 | User dashboard components |
| Tab 9 | Tab 4 | Blog components and models |
| Tab 10 | Tab 2, Tab 7 | Monetization components |
| Tab 11 | Tab 0 | Mobile project structure |
| Tab 12 | Tab 11 | Mobile screens and components |
| Tab 13 | Tab 4, Tab 12 | Synchronization services |

## Best Practices for Seamless Integration

### Consistent Naming Conventions
Ensure consistent naming across all tabs:
- Components: PascalCase (e.g., `ToolCard.tsx`)
- Files/utilities: camelCase (e.g., `fetchTools.ts`)
- Types/interfaces: PascalCase with prefix I (e.g., `IToolData`)
- Constants: UPPER_SNAKE_CASE (e.g., `MAX_ITEMS_PER_PAGE`)

### Type Sharing
1. Create a central `/types` directory with shared types
2. Export all interfaces and types from index files
3. Use consistent imports across components

### API Path Consistency
1. Define API routes in a central location (e.g., `lib/api.ts`)
2. Use consistent path patterns (e.g., `/api/tools/:id`)
3. Share API utility functions across components

### Component Consistency
1. Maintain consistent prop patterns
2. Use shared styling tokens
3. Document components with consistent JSDoc comments

### Testing Between Integration Points
After each integration:
1. Run linting to ensure code style consistency
2. Check for type errors with TypeScript
3. Test the integrated feature end-to-end
4. Verify mobile/web consistency for shared features

## Specific Integration Checkpoints

### Frontend Integration (Tabs 0-2 + 6-7)
Key files to ensure compatibility:
- Component props and interfaces
- Style tokens and Tailwind classes
- Layout components structure
- Navigation state management

### Backend Integration (Tabs 3-5)
Key files to ensure compatibility:
- Database model schemas
- API route structures
- Authentication flows
- Data collection services

### Web-Mobile Integration (Tabs 11-13)
Key files to ensure compatibility:
- Shared API clients
- Authentication tokens
- Data models and types
- Environment configurations

## Final Integration Checklist

Before finalizing the project:
1. Verify all environment variables are documented
2. Ensure consistent error handling throughout
3. Check for duplicate code that should be shared
4. Verify all API endpoints are properly secured
5. Test cross-platform functionality
6. Verify responsive design on multiple screen sizes
7. Run performance audits for web and mobile
8. Check accessibility compliance
