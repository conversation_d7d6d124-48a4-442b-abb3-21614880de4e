import React from 'react'
import Link from 'next/link'
import { FaArrowLeft } from 'react-icons/fa'
import { But<PERSON> } from '@/components/ui/Button'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">Tool Not Found</h1>
        <p className="text-gray-600 mb-8">
          Sorry, we couldn't find the AI tool you're looking for.
        </p>
        <Button asChild>
          <Link href="/" className="inline-flex items-center">
            <FaArrowLeft className="mr-2" />
            Back to Tools Directory
          </Link>
        </Button>
      </div>
    </div>
  )
} 