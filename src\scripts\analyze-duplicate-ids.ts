import dbConnect from '../lib/db';
import Tool from '../models/Tool';

async function findDuplicateIds() {
  try {
    console.log('🔍 Connecting to database...');
    await dbConnect();

    console.log('📊 Analyzing tools for duplicate IDs...');
    
    // Find all tools with their IDs
    const allTools = await Tool.find({}).select('_id name url').lean();
    console.log(`📈 Total tools found: ${allTools.length}`);

    // Track IDs and their occurrences
    const idCounts = new Map<string, number>();
    const duplicateIds: string[] = [];

    for (const tool of allTools) {
      const idString = (tool._id as any).toString();
      const currentCount = idCounts.get(idString) || 0;
      idCounts.set(idString, currentCount + 1);
      
      if (currentCount === 1) {
        // This is the second occurrence, mark as duplicate
        duplicateIds.push(idString);
      }
    }

    console.log(`🔍 Found ${duplicateIds.length} duplicate IDs`);

    if (duplicateIds.length > 0) {
      console.log('\n📋 Sample duplicate IDs:');
      duplicateIds.slice(0, 10).forEach((id, index) => {
        console.log(`  ${index + 1}. ${id} (appears ${idCounts.get(id)} times)`);
      });
    }

    // Get tools with duplicate IDs and their details
    if (duplicateIds.length > 0) {
      console.log('\n🔍 Analyzing duplicate ID tools:');
      
      for (const duplicateId of duplicateIds.slice(0, 5)) {
        const toolsWithSameId = await Tool.find({ _id: duplicateId }).lean();
        console.log(`\n ID ${duplicateId} appears ${toolsWithSameId.length} times:`);
        toolsWithSameId.forEach((tool, index) => {
          console.log(`   ${index + 1}. Name: ${tool.name}, URL: ${tool.url || 'N/A'}`);
        });
      }
    }

    // Alternative approach: use aggregation to find duplicates
    console.log('\n🔍 Using aggregation to verify...');
    
    const aggregationResult = await Tool.aggregate([
      {
        $group: {
          _id: '$_id',
          count: { $sum: 1 },
          docs: { $push: { name: '$name', url: '$url' } }
        }
      },
      {
        $match: { count: { $gt: 1 } }
      },
      {
        $limit: 10
      }
    ]);

    console.log(`📊 Aggregation found ${aggregationResult.length} duplicate ID groups`);
    
    if (aggregationResult.length > 0) {
      console.log('\n📋 Aggregation results:');
      aggregationResult.forEach((result, index) => {
        console.log(`  ${index + 1}. ID: ${result._id} (${result.count} times)`);
        result.docs.forEach((doc: any, docIndex: number) => {
          console.log(`     ${docIndex + 1}. ${doc.name} - ${doc.url || 'No URL'}`);
        });
      });
    }

  } catch (error) {
    console.error('❌ Error during analysis:', error);
    process.exit(1);
  }
}

// Run the analysis
findDuplicateIds()
  .then(() => {
    console.log('\n🎉 Analysis completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Analysis failed:', error);
    process.exit(1);
  });
