import { runToolsScraper } from '../services/toolsScraper';
import { runAPICollector } from '../services/apiToolCollector';

async function main() {
  console.log('🚀 AI Tools Comprehensive Data Collection');
  console.log('='.repeat(60));
  console.log('This script will collect AI tools from multiple sources:');
  console.log('• Web scraping from AI tool directories');
  console.log('• GitHub API for open-source AI projects');
  console.log('• Product Hunt API for new AI tools');
  console.log('• Hugging Face API for AI models');
  console.log('='.repeat(60));

  const startTime = Date.now();
  let totalErrors = 0;

  try {
    // Phase 1: API Collection (faster, more reliable)
    console.log('\n📡 PHASE 1: API-Based Collection');
    console.log('-'.repeat(40));
    try {
      await runAPICollector();
      console.log('✅ API collection completed');
    } catch (error) {
      console.error('❌ API collection failed:', error);
      totalErrors++;
    }

    // Phase 2: Web Scraping (slower, needs to respect robots.txt)
    console.log('\n🕷️ PHASE 2: Web Scraping');
    console.log('-'.repeat(40));
    try {
      await runToolsScraper();
      console.log('✅ Web scraping completed');
    } catch (error) {
      console.error('❌ Web scraping failed:', error);
      totalErrors++;
    }

  } catch (error) {
    console.error('❌ Collection process failed:', error);
    totalErrors++;
  }

  const duration = Math.round((Date.now() - startTime) / 1000);
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 FINAL SUMMARY');
  console.log('='.repeat(60));
  console.log(`⏱️ Total duration: ${duration} seconds`);
  console.log(`❌ Errors encountered: ${totalErrors}`);
  
  if (totalErrors === 0) {
    console.log('🎉 All collection phases completed successfully!');
    console.log('💡 Tip: Run this script periodically to keep your database updated');
  } else {
    console.log('⚠️ Some collection phases failed. Check logs above for details.');
  }
  
  console.log('='.repeat(60));
  
  process.exit(totalErrors > 0 ? 1 : 0);
}

main();
