import dbConnect from '../lib/db';
import Category from '../models/Category';
import Tool from '../models/Tool';

async function showAllTools() {
  try {
    await dbConnect();
    console.log('🔍 AI Tools Database Summary\n');

    // Get all categories with their tools count
    const categories = await Category.find({}).sort({ order: 1 });
    
    let totalTools = 0;
    
    for (const category of categories) {
      const tools = await Tool.find({ category: category._id }).sort({ name: 1 });
      const toolNames = tools.map(tool => tool.name);
      
      console.log(`📁 ${category.name} (${tools.length} tools)`);
      console.log(`   Slug: ${category.slug}`);
      if (tools.length > 0) {
        console.log(`   Tools: ${toolNames.join(', ')}`);
      }
      console.log('');
      
      totalTools += tools.length;
    }
    
    console.log(`📊 Total Tools: ${totalTools}`);
    console.log(`📁 Total Categories: ${categories.length}`);
    
    // Show top 10 tools by name for verification
    const allTools = await Tool.find({}).sort({ name: 1 }).limit(10);
    console.log('\n🔤 First 10 tools alphabetically:');
    allTools.forEach(tool => {
      console.log(`   • ${tool.name}`);
    });

    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

showAllTools();
