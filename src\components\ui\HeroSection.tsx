import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from './Button';

const HeroSection: React.FC = () => {
  return (
    <div className="relative bg-gradient-to-r from-primary to-primary/80 text-primary-foreground py-16 md:py-24 overflow-hidden">
      <div className="absolute inset-0 bg-grid-white/10 bg-[size:20px_20px]"></div>
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-3xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            Discover the Best AI Tools
          </h1>
          <p className="text-lg md:text-xl mb-8 text-primary-foreground/90">
            Explore our curated collection of AI tools to enhance your productivity, creativity, and workflow. More AI Tools coming soon....
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/tools">
              <Button size="lg" className="bg-white text-primary hover:bg-white/90">
                Browse Tools
              </Button>
            </Link>
            <Link href="/pricing">
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                Start Free Trial
              </Button>
            </Link>
          </div>
          <p className="mt-4 text-sm text-primary-foreground/70">
            7-day free trial • £5/month • Cancel anytime
          </p>
        </div>
      </div>
      
      <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-background to-transparent"></div>
    </div>
  );
};

export default HeroSection; 