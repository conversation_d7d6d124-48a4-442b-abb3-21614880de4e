import type { Tool } from '@/types/tool'
import { apiCall } from '@/hooks/useClientToken'

interface FetchToolsParams {
  query?: string
  category?: string
  sortBy?: 'rating' | 'name'
  page?: number
  limit?: number
}

interface ToolsResponse {
  tools: Tool[]
  total: number
  page: number
  totalPages: number
}

export async function fetchTools(params: FetchToolsParams = {}): Promise<ToolsResponse> {
  const searchParams = new URLSearchParams()
  
  if (params.query) searchParams.set('query', params.query)
  if (params.category) searchParams.set('category', params.category)
  if (params.sortBy) searchParams.set('sortBy', params.sortBy)
  if (params.page) searchParams.set('page', params.page.toString())
  if (params.limit) searchParams.set('limit', params.limit.toString())

  const response = await apiCall(`/api/tools?${searchParams.toString()}`)
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}))
    throw new Error(errorData.error || 'Failed to fetch tools')
  }

  return response.json()
}

export async function createTool(tool: Omit<Tool, 'id' | 'rating'>): Promise<Tool> {
  const response = await apiCall('/api/tools', {
    method: 'POST',
    body: JSON.stringify(tool),
  })

  if (!response.ok) {
    const error = await response.json().catch(() => ({}))
    throw new Error(error.message || 'Failed to create tool')
  }

  return response.json()
}

export async function getToolById(id: string): Promise<Tool> {
  const response = await apiCall(`/api/tools/${id}`)
  
  if (!response.ok) {
    if (response.status === 404) {
      throw new Error('Tool not found')
    }
    throw new Error('Failed to fetch tool')
  }

  return response.json()
}

export async function updateTool(id: string, toolData: Partial<Tool>): Promise<Tool> {
  const response = await apiCall(`/api/tools/${id}`, {
    method: 'PUT',
    body: JSON.stringify(toolData),
  })

  if (!response.ok) {
    if (response.status === 404) {
      throw new Error('Tool not found')
    }
    const error = await response.json().catch(() => ({}))
    throw new Error(error.message || 'Failed to update tool')
  }

  return response.json()
}

export async function deleteTool(id: string): Promise<void> {
  const response = await apiCall(`/api/tools/${id}`, {
    method: 'DELETE',
  })

  if (!response.ok) {
    if (response.status === 404) {
      throw new Error('Tool not found')
    }
    throw new Error('Failed to delete tool')
  }
} 