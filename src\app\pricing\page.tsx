import React from 'react';
import Link from 'next/link';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata = {
  title: 'Pricing - AI Tools Directory',
  description: 'Choose the perfect plan for your AI tool discovery needs. Premium access to thousands of AI tools and exclusive features.',
};

const PricingPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
            One Price, Everything Included
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            £5/month gets you unlimited access to view all AI tools AND submit your own tools. No hidden fees, no tiers, just everything you need.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="flex justify-center mb-16">
          <div className="max-w-md w-full">
            
            {/* Single All-Inclusive Plan */}
            <Card className="p-8 border-2 border-primary relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-primary text-primary-foreground px-4 py-1 rounded-full text-sm font-medium">
                  Everything Included
                </span>
              </div>
              
              <div className="text-center mb-8">
                <h3 className="text-3xl font-bold text-foreground mb-2">All Access</h3>
                <div className="text-5xl font-bold text-foreground mb-2">£5</div>
                <p className="text-muted-foreground">per month - unlimited everything</p>
              </div>
              
              <ul className="space-y-4 mb-8">
                <li className="flex items-start">
                  <svg className="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                  </svg>
                  <span className="text-foreground font-medium">Unlimited access to 3000+ AI tools</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                  </svg>
                  <span className="text-foreground font-medium">Submit unlimited AI tools</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                  </svg>
                  <span className="text-foreground font-medium">Advanced search & filtering</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                  </svg>
                  <span className="text-foreground font-medium">Priority tool review & approval</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                  </svg>
                  <span className="text-foreground font-medium">Detailed analytics & insights</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                  </svg>
                  <span className="text-foreground font-medium">Priority customer support</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                  </svg>
                  <span className="text-foreground font-medium">Early access to new features</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                  </svg>
                  <span className="text-foreground font-medium">Ad-free browsing experience</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                  </svg>
                  <span className="text-foreground font-medium">Export tool lists & comparisons</span>
                </li>
              </ul>
              
              <Link href="/login?callbackUrl=/dashboard?subscribed=true">
                <Button className="w-full mb-4 text-lg py-3">
                  Start 7-Day Free Trial
                </Button>
              </Link>
              <p className="text-center text-sm text-muted-foreground">
                Cancel anytime. No commitment. All features included.
              </p>
            </Card>
          </div>
        </div>

        {/* What's Included */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-foreground mb-12">
            What's Included in Your £5/Month Subscription
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            
            {/* Viewing Benefits */}
            <Card className="p-8">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-blue-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-foreground mb-2">View All Tools</h3>
              </div>
              
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Access to 3000+ AI tools
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Advanced search & filtering
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Detailed tool analytics
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Export lists & comparisons
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Ad-free experience
                </li>
              </ul>
            </Card>

            {/* Posting Benefits */}
            <Card className="p-8">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-foreground mb-2">Submit Your Tools</h3>
              </div>
              
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Submit unlimited AI tools
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Priority review & approval
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Professional listing creation
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Performance analytics
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Direct user engagement
                </li>
              </ul>
            </Card>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-foreground mb-12">
            Frequently Asked Questions
          </h2>
          
          <div className="max-w-4xl mx-auto space-y-8">
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-foreground mb-3">
                What do I get for £5 per month?
              </h3>
              <p className="text-muted-foreground">
                Everything! You get unlimited access to view all 3000+ AI tools in our directory, 
                plus the ability to submit unlimited AI tools of your own. There are no additional 
                fees or hidden costs - it's all included in your monthly subscription.
              </p>
            </Card>
            
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-foreground mb-3">
                Can I cancel my subscription anytime?
              </h3>
              <p className="text-muted-foreground">
                Yes! You can cancel your subscription at any time through your account settings. 
                Your access will remain active until the end of your current billing period, 
                and no future charges will be made.
              </p>
            </Card>
            
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-foreground mb-3">
                How does the 7-day free trial work?
              </h3>
              <p className="text-muted-foreground">
                When you sign up, you get full access to everything for 7 days completely free - 
                view all tools AND submit your own tools. If you don't cancel before the trial ends, 
                you'll be charged £5/month. No commitment required.
              </p>
            </Card>
            
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-foreground mb-3">
                Is there a limit to how many tools I can submit?
              </h3>
              <p className="text-muted-foreground">
                No limits! With your £5/month subscription, you can submit as many AI tools as you want. 
                Each submission goes through our quality review process to ensure high standards 
                for all users in our directory.
              </p>
            </Card>
            
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-foreground mb-3">
                Do you offer discounts for students or startups?
              </h3>
              <p className="text-muted-foreground">
                Yes! We offer 50% discounts for verified students and early-stage startups. 
                Contact our support team with your educational institution email or startup 
                documentation to apply for a discount (just £2.50/month).
              </p>
            </Card>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <Card className="p-8 bg-gradient-to-r from-primary/5 to-secondary/5">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Ready to Access Everything for £5/Month?
            </h2>
            <p className="text-lg text-muted-foreground mb-6">
              One simple price gets you unlimited access to view all AI tools AND submit your own tools. 
              Join thousands of AI enthusiasts, creators, and businesses.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/login?callbackUrl=/dashboard?subscribed=true">
                <Button size="lg" className="px-8">
                  Start 7-Day Free Trial
                </Button>
              </Link>
              <Link href="/">
                <Button variant="outline" size="lg" className="px-8">
                  Preview Tools
                </Button>
              </Link>
            </div>
            <p className="text-sm text-muted-foreground mt-4">
              Cancel anytime. No commitment. Everything included.
            </p>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PricingPage;
