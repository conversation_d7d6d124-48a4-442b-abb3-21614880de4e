import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { authMiddleware } from '@/middleware/auth';

export async function GET(request: NextRequest) {
  try {
    // Apply auth middleware
    const authResponse = await authMiddleware(request);
    if (authResponse instanceof NextResponse) {
      return authResponse;
    }
    
    await dbConnect();
    
    // Get user ID from headers set by middleware
    const userId = request.headers.get('x-user-id');
    
    // Find user by ID
    const user = await User.findById(userId)
      .select('-password')
      .populate('savedTools');
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(user);
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user profile' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Apply auth middleware
    const authResponse = await authMiddleware(request);
    if (authResponse instanceof NextResponse) {
      return authResponse;
    }
    
    await dbConnect();
    
    // Get user ID from headers set by middleware
    const userId = request.headers.get('x-user-id');
    
    // Get update data from request body
    const { name, email, password, image } = await request.json();
    
    // Find user by ID
    const user = await User.findById(userId);
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Update user fields
    if (name) user.name = name;
    if (email) user.email = email;
    if (password) user.password = password;
    if (image) user.image = image;
    
    // Save updated user
    const updatedUser = await user.save();
    
    // Return updated user without password
    return NextResponse.json({
      _id: updatedUser._id,
      name: updatedUser.name,
      email: updatedUser.email,
      image: updatedUser.image,
      role: updatedUser.role,
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    return NextResponse.json(
      { error: 'Failed to update user profile' },
      { status: 500 }
    );
  }
} 