import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Category from '@/models/Category';
import Tool from '@/models/Tool';
import { authMiddleware } from '@/middleware/auth';
import AntiScrapingService from '@/lib/antiScraping';

export async function GET(request: NextRequest) {
  try {
    // Anti-scraping checks (only if enabled)
    const isDevelopment = process.env.NODE_ENV === 'development'
    const antiScrapingEnabled = process.env.ANTI_SCRAPING_ENABLED === 'true'
    
    if (antiScrapingEnabled) {
      const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
      const antiScraping = AntiScrapingService.getInstance()
      
      // Check rate limiting
      const rateLimitCheck = antiScraping.checkRateLimit(ip)
      if (!rateLimitCheck.allowed) {
        return NextResponse.json(
          { error: 'Too many requests' }, 
          { 
            status: 429,
            headers: {
              'Retry-After': rateLimitCheck.retryAfter?.toString() || '60'
            }
          }
        )
      }
      
      // Check for suspicious behavior
      const suspiciousCheck = antiScraping.isSuspiciousRequest(request, ip)
      if (suspiciousCheck.isSuspicious) {
        console.log(`Blocked suspicious request from ${ip}: ${suspiciousCheck.reason}`)
        return NextResponse.json(
          { error: 'Access denied' }, 
          { status: 403 }
        )
      }
      
      // Validate client token for API requests
      const clientToken = request.headers.get('x-client-token')
      if (!isDevelopment && (!clientToken || !antiScraping.validateClientToken(clientToken))) {
        return NextResponse.json(
          { error: 'Invalid or missing client token' }, 
          { status: 401 }
        )
      }
    }

    // Check if database URI is configured
    if (!process.env.MONGODB_URI) {
      console.error('MONGODB_URI environment variable is not configured')
      return NextResponse.json(
        { 
          error: 'Database not configured',
          categories: []
        },
        { status: 503 }
      )
    }

    // Try to connect to database
    try {
      await dbConnect()
    } catch (dbError) {
      console.error('Database connection failed:', dbError)
      return NextResponse.json(
        { 
          error: 'Database connection failed',
          categories: []
        },
        { status: 503 }
      )
    }
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const parent = searchParams.get('parent');
    
    // Build query
    const query: any = {};
    
    if (parent === 'null') {
      query.parent = null;
    } else if (parent) {
      query.parent = parent;
    }
    
    // Find categories
    const categories = await Category.find(query)
      .populate('parent', 'name')
      .sort({ order: 1, name: 1 });
    
    // Get tool counts for each category
    const categoriesWithToolCount = await Promise.all(
      categories.map(async (category) => {
        const toolCount = await Tool.countDocuments({ category: category._id });
        return {
          ...category.toObject(),
          toolCount
        };
      })
    );
    
    return NextResponse.json(categoriesWithToolCount);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Apply auth middleware
    const authResponse = await authMiddleware(request);
    if (authResponse instanceof NextResponse) {
      return authResponse;
    }
    
    // Check if user is admin
    const userRole = request.headers.get('x-user-role');
    
    if (userRole !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized as admin' },
        { status: 403 }
      );
    }
    
    await dbConnect();
    
    // Get category data from request body
    const { name, description, icon, parent, order } = await request.json();
    
    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }
    
    // Generate slug from name
    const slug = name
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-');
    
    // Create new category
    const category = await Category.create({
      name,
      description,
      icon,
      parent,
      order,
      slug,
    });
    
    return NextResponse.json(category, { status: 201 });
  } catch (error) {
    console.error('Error creating category:', error);
    return NextResponse.json(
      { error: 'Failed to create category' },
      { status: 500 }
    );
  }
} 