'use client'

import { useEffect, useState } from 'react'

interface ObfuscatedContentProps {
  children: React.ReactNode
  delay?: number
  placeholder?: React.ReactNode
}

export default function ObfuscatedContent({ 
  children, 
  delay = 1000,
  placeholder 
}: ObfuscatedContentProps) {
  const [showContent, setShowContent] = useState(false)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    
    // Add random delay to make scraping harder
    const randomDelay = delay + Math.random() * 500
    
    const timer = setTimeout(() => {
      // Additional checks before showing content
      if (typeof window !== 'undefined' && 
          window.document && 
          !navigator.webdriver) {
        setShowContent(true)
      }
    }, randomDelay)

    return () => clearTimeout(timer)
  }, [delay])

  if (!isClient || !showContent) {
    return (
      <div className="animate-pulse">
        {placeholder || (
          <div className="space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        )}
      </div>
    )
  }

  return <>{children}</>
}

// Higher order component for tools
export function withObfuscation<T extends object>(
  Component: React.ComponentType<T>,
  options: { delay?: number } = {}
) {
  return function ObfuscatedComponent(props: T) {
    return (
      <ObfuscatedContent delay={options.delay}>
        <Component {...props} />
      </ObfuscatedContent>
    )
  }
}
