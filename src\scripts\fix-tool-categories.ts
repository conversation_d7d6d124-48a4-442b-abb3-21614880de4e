import dbConnect from '../lib/db';
import Tool from '../models/Tool';
import Category from '../models/Category';

// Mapping of category names to tool names
const categoryToolMapping: { [key: string]: string[] } = {
  'Language Models': ['ChatGPT', 'Claude', 'Anthropic', 'Cohere'],
  'Image Generation': ['DALL-E', 'Midjourney', 'Stable Diffusion'],
  'Content Creation': ['Jasper', 'Copy.ai'],
  'Writing': ['Grammarly'],
  'Video Editing': ['Descript'],
  'Video Generation': ['Synthesia', 'Pictory', 'RunwayML'],
  'Other': ['Hugging Face', 'Replicate']
};

async function fixToolCategories() {
  try {
    await dbConnect();
    console.log('Connected to database');

    // Get all categories
    const categories = await Category.find({});
    console.log('Found categories:', categories.map(c => ({ name: c.name, id: c._id })));

    // Create category name to ID mapping
    const categoryMap: { [key: string]: string } = {};
    categories.forEach(category => {
      categoryMap[category.name] = category._id.toString();
    });

    // Update tools with correct category IDs
    for (const [categoryName, toolNames] of Object.entries(categoryToolMapping)) {
      const categoryId = categoryMap[categoryName];
      if (categoryId) {
        console.log(`Updating tools for category: ${categoryName} (ID: ${categoryId})`);
        
        for (const toolName of toolNames) {
          const result = await Tool.updateMany(
            { name: toolName },
            { category: categoryId }
          );
          console.log(`Updated ${result.modifiedCount} tool(s) named "${toolName}"`);
        }
      } else {
        console.log(`Category "${categoryName}" not found in database`);
      }
    }

    console.log('Tool categories updated successfully');
  } catch (error) {
    console.error('Error fixing tool categories:', error);
  }
}

fixToolCategories();
