import fs from 'fs';
import path from 'path';
import Category from '../models/Category';
import Tool from '../models/Tool';
import dbConnect from '../lib/db';

// Comprehensive tool categories mapping
const toolCategories: { [key: string]: string } = {
  // Language Models
  'ChatGPT': 'Language Models',
  'Claude': 'Language Models',
  'Bard': 'Language Models',
  'Bing Chat': 'Language Models',
  'Anthropic': 'Language Models',
  'Cohere': 'Language Models',
  'Perplexity AI': 'Language Models',
  'You.com': 'Language Models',

  // Image Generation
  'Midjourney': 'Image Generation',
  'DALL-E': 'Image Generation',
  'Stable Diffusion': 'Image Generation',

  // Content Creation
  'Jasper': 'Content Creation',
  'Copy.ai': 'Content Creation',
  'Writesonic': 'Content Creation',
  'Rytr': 'Content Creation',
  'Tome': 'Content Creation',
  'Canva': 'Content Creation',
  'Beautiful.ai': 'Content Creation',
  'Gamma': 'Content Creation',
  'Pitch': 'Content Creation',
  'Slidebean': 'Content Creation',

  // Writing
  'Grammarly': 'Writing',
  'Wordtune': 'Writing',
  'Quillbot': 'Writing',
  'Heming<PERSON>': 'Writing',
  'ProWritingAid': 'Writing',
  'DeepL': 'Writing',
  'Linguix': 'Writing',
  'Scribens': 'Writing',
  'Antidote': 'Writing',
  'Reverso': 'Writing',

  // Productivity
  'Notion AI': 'Productivity',

  // Transcription
  'Otter.ai': 'Transcription',
  'AssemblyAI': 'Transcription',
  'Whisper': 'Transcription',

  // Video Editing
  'Descript': 'Video Editing',
  'Kapwing': 'Video Editing',

  // Video Generation
  'Synthesia': 'Video Generation',
  'Pictory': 'Video Generation',
  'RunwayML': 'Video Generation',
  'Lumen5': 'Video Generation',

  // Code Generation
  'GitHub Copilot': 'Code Generation',
  'Amazon CodeWhisperer': 'Code Generation',
  'Codeium': 'Code Generation',
  'Tabnine': 'Code Generation',
  'Phind': 'Code Generation',

  // Education
  'Duolingo': 'Education',
  'Babbel': 'Education',
  'Rosetta Stone': 'Education',
  'Busuu': 'Education',
  'Memrise': 'Education',
  'Anki': 'Education',
  'Quizlet': 'Education',
  'Kahoot': 'Education',
  'Coursera': 'Education',
  'Udemy': 'Education',
  'edX': 'Education',
  'Skillshare': 'Education',
  'Brilliant': 'Education',
  'Khan Academy': 'Education',
  'DataCamp': 'Education',
  'Codecademy': 'Education',
  'freeCodeCamp': 'Education',
  'Pluralsight': 'Education',
  'Udacity': 'Education',
  'Treehouse': 'Education',
  'LinkedIn Learning': 'Education',
  'Masterclass': 'Education',
  'Skillsoft': 'Education',

  // Audio AI
  'Eleven Labs': 'Audio AI',
  'Murf.ai': 'Audio AI',
  'Play.ht': 'Audio AI',
  'Resemble.ai': 'Audio AI',

  // Other
  'Hugging Face': 'Other',
  'Replicate': 'Other',
  'default': 'Other'
};

// Comprehensive tool descriptions
const toolDescriptions: { [key: string]: string } = {
  // Language Models
  'ChatGPT': 'Advanced AI language model by OpenAI for conversations, content creation, and task assistance',
  'Claude': 'AI assistant by Anthropic known for helpful, honest, and harmless conversations',
  'Bard': 'Google\'s AI chatbot powered by LaMDA for creative and informative conversations',
  'Bing Chat': 'Microsoft\'s AI-powered search assistant integrated with Bing search',
  'Anthropic': 'AI safety-focused language model platform for various text generation tasks',
  'Cohere': 'Enterprise-focused AI platform for natural language processing and generation',
  'Perplexity AI': 'AI-powered search engine that provides detailed answers with citations',
  'You.com': 'AI search engine that combines web search with AI-generated responses',

  // Image Generation
  'Midjourney': 'AI art generator creating stunning and artistic images from text descriptions',
  'DALL-E': 'OpenAI\'s image generation model for creating unique images from text prompts',
  'Stable Diffusion': 'Open-source AI model for generating high-quality images from text',

  // Content Creation
  'Jasper': 'AI writing assistant for marketing copy, blog posts, and creative content',
  'Copy.ai': 'AI copywriting tool for marketing materials, social media, and business content',
  'Writesonic': 'AI writing platform for creating articles, ads, and marketing content',
  'Rytr': 'AI writing assistant for creating content across multiple formats and tones',
  'Tome': 'AI-powered presentation and storytelling platform',
  'Canva': 'Design platform with AI features for creating graphics, presentations, and marketing materials',
  'Beautiful.ai': 'AI-powered presentation software with smart design assistance',
  'Gamma': 'AI presentation maker that creates slides from simple text prompts',
  'Pitch': 'Collaborative presentation software with AI-powered design suggestions',
  'Slidebean': 'AI presentation platform that automatically designs slides',

  // Writing
  'Grammarly': 'AI-powered writing assistant for grammar, spelling, and style improvements',
  'Wordtune': 'AI writing companion that helps rewrite and improve text clarity',
  'Quillbot': 'AI paraphrasing tool for rewriting and improving text',
  'Hemingway': 'Writing app that highlights complex sentences and common errors',
  'ProWritingAid': 'Comprehensive writing assistant with grammar, style, and readability analysis',
  'DeepL': 'AI-powered translation service known for high-quality translations',
  'Linguix': 'AI writing assistant for grammar checking and writing enhancement',
  'Scribens': 'Free grammar checker and text corrector',
  'Antidote': 'Advanced grammar checker and writing assistant for multiple languages',
  'Reverso': 'Translation and language learning platform with context examples',

  // Productivity
  'Notion AI': 'AI features integrated into Notion for writing, summarizing, and organizing content',

  // Transcription
  'Otter.ai': 'AI-powered transcription service for meetings, interviews, and lectures',
  'AssemblyAI': 'Speech-to-text API with advanced AI features for developers',
  'Whisper': 'OpenAI\'s open-source speech recognition system',

  // Video Editing
  'Descript': 'AI-powered video and podcast editing with text-based editing interface',
  'Kapwing': 'Online video editor with AI features for content creators',

  // Video Generation
  'Synthesia': 'AI video platform for creating videos with AI avatars',
  'Pictory': 'AI video creation platform that turns text into engaging videos',
  'RunwayML': 'AI-powered creative suite for video editing and generation',
  'Lumen5': 'AI video creation platform for transforming content into videos',

  // Code Generation
  'GitHub Copilot': 'AI pair programmer that suggests code completions and functions',
  'Amazon CodeWhisperer': 'AI code generator by AWS for multiple programming languages',
  'Codeium': 'Free AI-powered code completion and search tool',
  'Tabnine': 'AI code assistant that provides intelligent code completions',
  'Phind': 'AI search engine specifically designed for developers and programmers',

  // Education
  'Duolingo': 'Language learning platform with AI-powered personalized lessons',
  'Babbel': 'Language learning app with conversation-focused AI tutoring',
  'Rosetta Stone': 'Language learning software with speech recognition technology',
  'Busuu': 'Language learning platform with AI-powered study plans',
  'Memrise': 'Language learning app using spaced repetition and AI',
  'Anki': 'Flashcard app with spaced repetition algorithm for efficient learning',
  'Quizlet': 'Study platform with AI-powered study modes and flashcards',
  'Kahoot': 'Interactive learning platform with AI-generated quizzes',
  'Coursera': 'Online learning platform offering courses from top universities',
  'Udemy': 'Global marketplace for online learning with diverse course offerings',
  'edX': 'Non-profit online learning platform founded by Harvard and MIT',
  'Skillshare': 'Creative learning platform with project-based courses',
  'Brilliant': 'Interactive learning platform for STEM subjects',
  'Khan Academy': 'Free educational platform with personalized learning dashboard',
  'DataCamp': 'Platform for learning data science and analytics skills',
  'Codecademy': 'Interactive platform for learning programming and web development',
  'freeCodeCamp': 'Non-profit organization offering free coding bootcamp curriculum',
  'Pluralsight': 'Technology learning platform for IT professionals',
  'Udacity': 'Online education platform focusing on tech skills and nanodegrees',
  'Treehouse': 'Online technology education platform with interactive courses',
  'LinkedIn Learning': 'Professional development platform with business and tech courses',
  'Masterclass': 'Premium online learning platform with celebrity instructors',
  'Skillsoft': 'Corporate learning platform for professional skill development',

  // Audio AI
  'Eleven Labs': 'AI voice synthesis platform for creating realistic speech',
  'Murf.ai': 'AI voice generator for creating professional voiceovers',
  'Play.ht': 'AI voice generator with ultra-realistic text-to-speech',
  'Resemble.ai': 'AI voice cloning and generation platform',

  // Other
  'Hugging Face': 'Open-source platform for machine learning models and datasets',
  'Replicate': 'Platform for running machine learning models in the cloud',

  'default': 'An AI-powered tool designed to enhance productivity and creativity'
};

// Tool features mapping
const toolFeatures: { [key: string]: string[] } = {
  'ChatGPT': ['Natural language processing', 'Content generation', 'Code assistance', 'Translation', 'Question answering'],
  'Claude': ['Natural language understanding', 'Content creation', 'Analysis and research', 'Code generation', 'Task assistance'],
  'Bard': ['Creative writing', 'Information synthesis', 'Code generation', 'Language translation', 'Question answering'],
  'Midjourney': ['Artistic image generation', 'Style customization', 'High-resolution output', 'Community features'],
  'DALL-E': ['Image generation from text', 'Style variations', 'Image editing', 'High-quality output'],
  'Grammarly': ['Grammar checking', 'Style suggestions', 'Plagiarism detection', 'Tone analysis', 'Browser integration'],
  'GitHub Copilot': ['Code completion', 'Function generation', 'Multiple language support', 'Context awareness'],
  'Notion AI': ['Writing assistance', 'Content summarization', 'Idea generation', 'Task automation'],
  'Otter.ai': ['Real-time transcription', 'Speaker identification', 'Meeting summaries', 'Keyword search'],
  'default': ['AI-powered functionality', 'User-friendly interface', 'Integration capabilities']
};

// Tool URLs mapping
const toolUrls: { [key: string]: string } = {
  'ChatGPT': 'https://chat.openai.com',
  'Claude': 'https://claude.ai',
  'Bard': 'https://bard.google.com',
  'Bing Chat': 'https://www.bing.com/chat',
  'Midjourney': 'https://www.midjourney.com',
  'DALL-E': 'https://labs.openai.com',
  'Stable Diffusion': 'https://stability.ai',
  'Grammarly': 'https://www.grammarly.com',
  'Jasper': 'https://www.jasper.ai',
  'Copy.ai': 'https://www.copy.ai',
  'Notion AI': 'https://www.notion.so/product/ai',
  'GitHub Copilot': 'https://github.com/features/copilot',
  'Otter.ai': 'https://otter.ai',
  'Descript': 'https://www.descript.com',
  'Synthesia': 'https://www.synthesia.io',
  'RunwayML': 'https://runwayml.com',
  'Hugging Face': 'https://huggingface.co',
  'Duolingo': 'https://www.duolingo.com',
  'Coursera': 'https://www.coursera.org',
  'Udemy': 'https://www.udemy.com',
  'default': ''
};

// Tool pricing mapping
const toolPricing: { [key: string]: string } = {
  'ChatGPT': 'Free / $20/month for Plus',
  'Claude': 'Free / Premium plans available',
  'Bard': 'Free',
  'Bing Chat': 'Free',
  'Midjourney': 'Starting at $10/month',
  'DALL-E': 'Pay-per-use credits',
  'Grammarly': 'Free / Premium from $12/month',
  'GitHub Copilot': '$10/month for individuals',
  'Notion AI': '$10/month per user',
  'Otter.ai': 'Free / Pro from $10/month',
  'Duolingo': 'Free / Plus from $6.99/month',
  'Coursera': 'Free courses / Specializations from $39/month',
  'Udemy': 'Pay-per-course, frequent sales',
  'default': 'Varies'
};

async function importAllTools() {
  try {
    // Connect to database
    await dbConnect();
    console.log('Connected to database');

    // Read tools from tools_list.txt
    const toolsListPath = path.join(process.cwd(), 'tools_list.txt');
    const toolsFileContent = fs.readFileSync(toolsListPath, 'utf-8');
    const tools = toolsFileContent
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      // Remove duplicates
      .filter((tool, index, array) => array.indexOf(tool) === index);

    console.log(`Found ${tools.length} unique tools to import`);

    // Get existing categories
    const categories = await Category.find({});
    const categoryMap = new Map<string, string>();
    
    categories.forEach(category => {
      categoryMap.set(category.name, category._id.toString());
    });

    console.log('Available categories:', Array.from(categoryMap.keys()));

    // Import tools
    let createdCount = 0;
    let existingCount = 0;
    let errorCount = 0;

    for (const toolName of tools) {
      try {
        const existingTool = await Tool.findOne({ name: toolName });
        
        if (!existingTool) {
          const categoryName = toolCategories[toolName] || toolCategories.default;
          const categoryId = categoryMap.get(categoryName);
          
          if (!categoryId) {
            console.log(`❌ Category '${categoryName}' not found for tool: ${toolName}`);
            errorCount++;
            continue;
          }

          const slug = toolName.toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim();
          
          const newTool = await Tool.create({
            name: toolName,
            slug,
            description: toolDescriptions[toolName] || toolDescriptions.default,
            features: toolFeatures[toolName] || toolFeatures.default,
            pricing: toolPricing[toolName] || toolPricing.default,
            url: toolUrls[toolName] || toolUrls.default,
            category: categoryId,
            rating: 0,
            ratingCount: 0,
            status: 'active',
            imageUrl: `https://api.dicebear.com/7.x/shapes/svg?seed=${encodeURIComponent(toolName)}&backgroundColor=b6e3f4,c0aede,d1f4d9,ffdfbf,ffd5dc`,
            apiAvailable: false
          });

          console.log(`✅ Created tool: ${toolName} (Category: ${categoryName})`);
          createdCount++;
        } else {
          console.log(`⚪ Tool already exists: ${toolName}`);
          existingCount++;
        }
      } catch (error) {
        console.log(`❌ Error creating tool ${toolName}:`, error);
        errorCount++;
      }
    }

    console.log('\n📊 Import Summary:');
    console.log(`✅ Created: ${createdCount} tools`);
    console.log(`⚪ Already existed: ${existingCount} tools`);
    console.log(`❌ Errors: ${errorCount} tools`);
    console.log(`📝 Total processed: ${createdCount + existingCount + errorCount} tools`);

    process.exit(0);
  } catch (error) {
    console.error('❌ Error importing tools:', error);
    process.exit(1);
  }
}

importAllTools();
