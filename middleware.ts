import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Common bot user agents to block (excluding legitimate search engines)
const BOT_USER_AGENTS = [
  'scrapy', 'python-requests', 'curl', 'wget', 'postman', 'insomnia', 'httpie', 
  'apache-httpclient', 'okhttp', 'node-fetch', 'axios', 'requests', 'urllib', 
  'beautifulsoup', 'selenium', 'playwright', 'puppeteer', 'chrome-headless', 
  'phantomjs', 'headless'
]

// Allowed search engine bots
const ALLOWED_SEARCH_BOTS = [
  'googlebot', 'bingbot', 'slurp', 'duckduckbot', 'baiduspider', 'yandexbot',
  'facebookexternalhit', 'twitterbot', 'linkedinbot', 'whatsapp', 'applebot',
  'ia_archiver', 'archive.org_bot'
]

// Rate limiting map (in production, use Redis or similar)
const requestCounts = new Map<string, { count: number; resetTime: number }>()

// Suspicious patterns in URLs
const SUSPICIOUS_PATTERNS = [
  '/api/tools?limit=1000',
  '/api/categories?limit=1000',
  'page=',
  'limit=100',
  'limit=200',
  'limit=500',
  'limit=1000'
]

export function middleware(request: NextRequest) {
  const userAgent = request.headers.get('user-agent')?.toLowerCase() || ''
  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
  const url = request.nextUrl.pathname + request.nextUrl.search
  
  // Skip most anti-scraping checks in development mode
  const isDevelopment = process.env.NODE_ENV === 'development'
  
  // Only enable anti-scraping if explicitly enabled
  const antiScrapingEnabled = process.env.ANTI_SCRAPING_ENABLED === 'true'
  
  if (!isDevelopment && antiScrapingEnabled) {
    // Check for bot user agents (but allow search engines)
    const isSearchBot = ALLOWED_SEARCH_BOTS.some(bot => userAgent.includes(bot))
    const isMaliciousBot = BOT_USER_AGENTS.some(bot => userAgent.includes(bot))
    
    if (isMaliciousBot && !isSearchBot) {
      console.log(`Blocked malicious bot request from ${ip}: ${userAgent}`)
      return new NextResponse('Forbidden', { status: 403 })
    }
    
    // Check for suspicious patterns
    if (SUSPICIOUS_PATTERNS.some(pattern => url.includes(pattern))) {
      console.log(`Blocked suspicious request from ${ip}: ${url}`)
      return new NextResponse('Forbidden', { status: 403 })
    }
  }
  
  // Rate limiting (more lenient in development)
  const now = Date.now()
  const windowMs = 60 * 1000 // 1 minute
  const maxRequests = isDevelopment ? 200 : 30 // Much higher limit in development
  
  const currentData = requestCounts.get(ip)
  
  if (!currentData || now > currentData.resetTime) {
    requestCounts.set(ip, { count: 1, resetTime: now + windowMs })
  } else {
    currentData.count++
    if (currentData.count > maxRequests) {
      console.log(`Rate limited ${ip}: ${currentData.count} requests`)
      return new NextResponse('Too Many Requests', { status: 429 })
    }
  }
  
  // Clean up old entries (simple cleanup)
  for (const [key, data] of requestCounts.entries()) {
    if (now > data.resetTime) {
      requestCounts.delete(key)
    }
  }
  
  // Add security headers
  const response = NextResponse.next()
  
  // Prevent indexing by search engines for sensitive pages
  if (request.nextUrl.pathname.startsWith('/api/')) {
    response.headers.set('X-Robots-Tag', 'noindex, nofollow, noarchive, nosnippet')
  }
  
  // Security headers
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  
  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}
