'use client'

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export default function DebugDeploymentPage() {
  const [diagnostics, setDiagnostics] = useState<any>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const runDiagnostics = async () => {
      const results: any = {
        environment: {
          hostname: window.location.hostname,
          protocol: window.location.protocol,
          isDevelopment: window.location.hostname === 'localhost',
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        },
        api: {},
        errors: []
      };

      // Test API endpoints
      try {
        console.log('Testing /api/tools...');
        const toolsResponse = await fetch('/api/tools');
        results.api.tools = {
          status: toolsResponse.status,
          statusText: toolsResponse.statusText,
          headers: Object.fromEntries(toolsResponse.headers.entries())
        };
        
        if (toolsResponse.ok) {
          const toolsData = await toolsResponse.json();
          results.api.tools.dataLength = Array.isArray(toolsData) ? toolsData.length : 'Not an array';
          results.api.tools.sample = Array.isArray(toolsData) ? toolsData.slice(0, 2) : toolsData;
        } else {
          const errorText = await toolsResponse.text();
          results.api.tools.error = errorText;
        }
      } catch (error) {
        results.api.tools = { error: error.message };
        results.errors.push(`Tools API error: ${error.message}`);
      }

      // Test categories API
      try {
        console.log('Testing /api/categories...');
        const categoriesResponse = await fetch('/api/categories');
        results.api.categories = {
          status: categoriesResponse.status,
          statusText: categoriesResponse.statusText
        };
        
        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json();
          results.api.categories.dataLength = Array.isArray(categoriesData) ? categoriesData.length : 'Not an array';
        } else {
          const errorText = await categoriesResponse.text();
          results.api.categories.error = errorText;
        }
      } catch (error) {
        results.api.categories = { error: error.message };
        results.errors.push(`Categories API error: ${error.message}`);
      }

      // Test NextAuth
      try {
        console.log('Testing NextAuth...');
        const authResponse = await fetch('/api/auth/session');
        results.api.auth = {
          status: authResponse.status,
          statusText: authResponse.statusText
        };
        
        if (authResponse.ok) {
          const authData = await authResponse.json();
          results.api.auth.hasSession = !!authData.user;
        }
      } catch (error) {
        results.api.auth = { error: error.message };
        results.errors.push(`Auth API error: ${error.message}`);
      }

      // Check client token
      try {
        const clientToken = localStorage.getItem('client-token') || window.__CLIENT_TOKEN__;
        results.clientToken = {
          hasToken: !!clientToken,
          tokenLength: clientToken ? clientToken.length : 0
        };
      } catch (error) {
        results.clientToken = { error: error.message };
      }

      setDiagnostics(results);
      setLoading(false);
    };

    runDiagnostics();
  }, []);

  const testApiWithToken = async () => {
    try {
      const response = await fetch('/api/tools', {
        headers: {
          'X-Client-Token': localStorage.getItem('client-token') || '',
          'X-Requested-With': 'XMLHttpRequest'
        }
      });
      
      const result = {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      };
      
      if (response.ok) {
        const data = await response.json();
        result.dataLength = Array.isArray(data) ? data.length : 'Not an array';
      } else {
        result.error = await response.text();
      }
      
      alert(JSON.stringify(result, null, 2));
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Running diagnostics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-foreground mb-8">Deployment Diagnostics</h1>
          
          <div className="space-y-6">
            {/* Environment Info */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">Environment</h2>
              <pre className="bg-muted p-4 rounded text-sm overflow-auto">
                {JSON.stringify(diagnostics.environment, null, 2)}
              </pre>
            </Card>

            {/* API Tests */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">API Tests</h2>
              <pre className="bg-muted p-4 rounded text-sm overflow-auto">
                {JSON.stringify(diagnostics.api, null, 2)}
              </pre>
              <div className="mt-4">
                <Button onClick={testApiWithToken}>
                  Test API with Client Token
                </Button>
              </div>
            </Card>

            {/* Client Token */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">Client Token</h2>
              <pre className="bg-muted p-4 rounded text-sm overflow-auto">
                {JSON.stringify(diagnostics.clientToken, null, 2)}
              </pre>
            </Card>

            {/* Errors */}
            {diagnostics.errors && diagnostics.errors.length > 0 && (
              <Card className="p-6 border-red-200">
                <h2 className="text-xl font-semibold mb-4 text-red-600">Errors</h2>
                <ul className="space-y-2">
                  {diagnostics.errors.map((error, index) => (
                    <li key={index} className="text-red-600 bg-red-50 p-2 rounded">
                      {error}
                    </li>
                  ))}
                </ul>
              </Card>
            )}

            {/* Raw Diagnostics */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">Full Diagnostics</h2>
              <pre className="bg-muted p-4 rounded text-xs overflow-auto max-h-96">
                {JSON.stringify(diagnostics, null, 2)}
              </pre>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
