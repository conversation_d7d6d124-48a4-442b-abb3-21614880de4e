import dbConnect from '../lib/db';
import Tool from '../models/Tool';
import Category from '../models/Category';
import { Types } from 'mongoose';

interface ToolData {
  name: string;
  description: string;
  url?: string;
  pricing?: string;
  features?: string[];
  imageUrl?: string;
  company?: string;
  category?: string;
  source?: string;
  apiAvailable?: boolean;
  [key: string]: any;
}

interface DetailScore {
  score: number;
  details: {
    hasUrl: boolean;
    hasFeatures: boolean;
    hasCompany: boolean;
    hasPricing: boolean;
    hasImage: boolean;
    descriptionLength: number;
    featuresCount: number;
  };
}

export class SmartDuplicateHandler {
  private categoryMap: Map<string, string> = new Map();

  async initialize() {
    await dbConnect();
    await this.loadCategoryMap();
  }

  private async loadCategoryMap() {
    const categories = await Category.find({});
    categories.forEach(category => {
      this.categoryMap.set(category.slug, category._id.toString());
      this.categoryMap.set(category.name.toLowerCase(), category._id.toString());
    });
  }
  
  /**
   * Calculate a detail score for a tool based on completeness of information
   */
  private calculateDetailScore(tool: ToolData): DetailScore {
    const details = {
      hasUrl: Boolean(tool.url && tool.url.trim() !== ''),
      hasFeatures: Boolean(tool.features && tool.features.length > 0),
      hasCompany: Boolean(tool.company && tool.company.trim() !== ''),
      hasPricing: Boolean(tool.pricing && tool.pricing.trim() !== ''),
      hasImage: Boolean(tool.imageUrl && tool.imageUrl.trim() !== '' && !tool.imageUrl.includes('dicebear')),
      descriptionLength: tool.description ? tool.description.length : 0,
      featuresCount: tool.features ? tool.features.length : 0
    };

    let score = 0;
    
    // Base scoring
    score += details.hasUrl ? 20 : 0;
    score += details.hasFeatures ? 15 : 0;
    score += details.hasCompany ? 10 : 0;
    score += details.hasPricing ? 10 : 0;
    score += details.hasImage ? 15 : 0;
    
    // Description quality (longer descriptions get more points, up to 20)
    score += Math.min(details.descriptionLength / 10, 20);
    
    // Features count (more features = better, up to 10)
    score += Math.min(details.featuresCount * 2, 10);

    return { score, details };
  }

  /**
   * Check if two tool names are similar enough to be considered duplicates
   */
  private areNamesSimilar(name1: string, name2: string): boolean {
    const normalize = (str: string) => str.toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .trim();

    const n1 = normalize(name1);
    const n2 = normalize(name2);

    // Exact match
    if (n1 === n2) return true;

    // One contains the other
    if (n1.includes(n2) || n2.includes(n1)) return true;

    // Check for common variations
    const variations: Array<[RegExp, string]> = [
      [/ai$/, ''],
      [/\.ai$/, ''],
      [/\sai$/, ''],
      [/^ai\s/, ''],
      [/\s+/g, ''],
      [/[.-]/g, '']
    ];

    let mod1 = n1;
    let mod2 = n2;

    for (const [pattern, replacement] of variations) {
      mod1 = mod1.replace(pattern, replacement);
      mod2 = mod2.replace(pattern, replacement);
    }

    return mod1 === mod2;
  }

  /**
   * Find potential duplicates of a tool in the database
   */
  async findPotentialDuplicates(toolData: ToolData): Promise<any[]> {
    await dbConnect();

    const potentialDuplicates = await Tool.find({
      $or: [
        { name: { $regex: new RegExp(toolData.name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i') } },
        { url: toolData.url },
        { slug: toolData.name.toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-') }
      ]
    });

    return potentialDuplicates.filter(existing => 
      this.areNamesSimilar(existing.name, toolData.name) ||
      (toolData.url && existing.url === toolData.url)
    );
  }

  /**
   * Merge tool data, keeping the best information from both
   */
  private mergeToolData(existing: any, newTool: ToolData): any {
    const existingScore = this.calculateDetailScore(existing);
    const newScore = this.calculateDetailScore(newTool);

    console.log(`📊 Comparing tools:
    Existing: "${existing.name}" (Score: ${existingScore.score})
    New: "${newTool.name}" (Score: ${newScore.score})`);

    // Start with the tool that has the higher score as base
    const base = newScore.score > existingScore.score ? newTool : existing.toObject ? existing.toObject() : existing;
    const supplement = newScore.score > existingScore.score ? (existing.toObject ? existing.toObject() : existing) : newTool;

    // Merge fields, preferring non-empty values, but don't include _id in updates
    const merged = {
      name: base.name || supplement.name,
      description: base.description && base.description.length > supplement.description?.length 
        ? base.description 
        : supplement.description || base.description,
      url: base.url || supplement.url,
      pricing: base.pricing || supplement.pricing,
      company: base.company || supplement.company,
      imageUrl: (base.imageUrl && !base.imageUrl.includes('dicebear')) 
        ? base.imageUrl 
        : (supplement.imageUrl && !supplement.imageUrl.includes('dicebear'))
        ? supplement.imageUrl 
        : base.imageUrl,
      features: this.mergeFeatures(base.features || [], supplement.features || []),
      source: `${base.source || 'unknown'}, ${supplement.source || 'unknown'}`,
      updatedAt: new Date()
    };

    return merged;
  }

  /**
   * Merge features arrays, removing duplicates
   */
  private mergeFeatures(features1: string[], features2: string[]): string[] {
    const combined = [...(features1 || []), ...(features2 || [])];
    const unique = [...new Set(combined.map(f => f.toLowerCase()))];
    return unique.map(f => 
      combined.find(original => original.toLowerCase() === f) || f
    );
  }

  /**
   * Process a new tool, handling duplicates intelligently
   */
  async processNewTool(toolData: ToolData): Promise<{ action: 'created' | 'updated' | 'skipped', toolId?: string, details?: string }> {
    try {
      // Resolve category ID from slug or name
      let categoryId = this.categoryMap.get('other');
      if (toolData.category) {
        categoryId = this.categoryMap.get(toolData.category.toLowerCase()) || 
                    this.categoryMap.get(toolData.category) || 
                    categoryId;
      }

      // Create slug
      const slug = toolData.name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();

      // Generate placeholder image if not provided
      const imageUrl = toolData.imageUrl || 
        `https://api.dicebear.com/7.x/shapes/svg?seed=${encodeURIComponent(toolData.name)}&backgroundColor=b6e3f4,c0aede,d1f4d9,ffdfbf,ffd5dc`;

      // Prepare complete tool data
      const completeToolData = {
        name: toolData.name,
        slug,
        description: toolData.description,
        category: categoryId,
        url: toolData.url || '',
        pricing: toolData.pricing || 'Contact for pricing',
        imageUrl,
        features: toolData.features || ['AI-powered functionality'],
        company: toolData.company || '',
        rating: 0,
        ratingCount: 0,
        status: 'active',
        apiAvailable: toolData.apiAvailable || false,
        source: toolData.source || 'unknown'
      };

      const duplicates = await this.findPotentialDuplicates(toolData);

      if (duplicates.length === 0) {
        // No duplicates found, create new tool
        const newTool = await Tool.create(completeToolData);
        return { 
          action: 'created', 
          toolId: newTool._id.toString(),
          details: `New tool created: ${toolData.name}`
        };
      }

      // Handle duplicates
      const bestDuplicate = duplicates.reduce((best, current) => {
        const bestScore = this.calculateDetailScore(best);
        const currentScore = this.calculateDetailScore(current);
        return currentScore.score > bestScore.score ? current : best;
      });

      const mergedData = this.mergeToolData(bestDuplicate, completeToolData);
      
      // Update the existing tool with merged data
      await Tool.findByIdAndUpdate(bestDuplicate._id, mergedData);

      return {
        action: 'updated',
        toolId: bestDuplicate._id.toString(),
        details: `Updated existing tool with better data: ${mergedData.name}`
      };

    } catch (error) {
      console.error('Error processing tool:', error);
      return {
        action: 'skipped',
        details: `Error processing ${toolData.name}: ${error}`
      };
    }
  }

  /**
   * Batch process multiple tools
   */
  async batchProcessTools(tools: ToolData[]): Promise<{
    created: number;
    updated: number;
    skipped: number;
    details: string[];
  }> {
    // Initialize if not already done
    if (this.categoryMap.size === 0) {
      await this.initialize();
    }

    const results = {
      created: 0,
      updated: 0,
      skipped: 0,
      details: [] as string[]
    };

    for (const tool of tools) {
      const result = await this.processNewTool(tool);
      
      if (result.action === 'created') results.created++;
      else if (result.action === 'updated') results.updated++;
      else results.skipped++;

      if (result.details) {
        results.details.push(result.details);
      }

      // Add small delay to prevent overwhelming the database
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    return results;
  }

  /**
   * Clean up all duplicates in the database
   */
  async cleanupAllDuplicates(): Promise<{ merged: number, removed: number }> {
    await dbConnect();
    
    const allTools = await Tool.find({});
    const processed = new Set<string>();
    let merged = 0;
    let removed = 0;

    for (const tool of allTools) {
      if (processed.has(tool._id.toString())) continue;

      const duplicates = await this.findPotentialDuplicates(tool);
      const realDuplicates = duplicates.filter(d => 
        d._id.toString() !== tool._id.toString() && 
        !processed.has(d._id.toString())
      );

      if (realDuplicates.length > 0) {
        // Find the best version among all duplicates
        const allVersions = [tool, ...realDuplicates];
        const bestVersion = allVersions.reduce((best, current) => {
          const bestScore = this.calculateDetailScore(best);
          const currentScore = this.calculateDetailScore(current);
          return currentScore.score > bestScore.score ? current : best;
        });

        // Merge all data into the best version
        let mergedData = bestVersion.toObject();
        for (const duplicate of allVersions) {
          if (duplicate._id.toString() !== bestVersion._id.toString()) {
            mergedData = this.mergeToolData({ toObject: () => mergedData }, duplicate);
          }
        }

        // Update the best version
        await Tool.findByIdAndUpdate(bestVersion._id, mergedData);

        // Remove other duplicates
        for (const duplicate of allVersions) {
          if (duplicate._id.toString() !== bestVersion._id.toString()) {
            await Tool.findByIdAndDelete(duplicate._id);
            processed.add(duplicate._id.toString());
            removed++;
          }
        }

        processed.add(bestVersion._id.toString());
        merged++;
      } else {
        processed.add(tool._id.toString());
      }
    }

    return { merged, removed };
  }
}

export default SmartDuplicateHandler;
