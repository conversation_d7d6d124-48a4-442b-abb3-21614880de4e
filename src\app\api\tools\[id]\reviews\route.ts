import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Review from '@/models/Review';
import Tool from '@/models/Tool';
import { authMiddleware } from '@/middleware/auth';

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    await dbConnect();
    
    const { id } = params;
    
    // Find reviews for the tool
    const reviews = await Review.find({ tool: id })
      .populate('user', 'name image')
      .sort({ createdAt: -1 });
    
    return NextResponse.json(reviews);
  } catch (error) {
    console.error('Error fetching reviews:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reviews' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Apply auth middleware
    const authResponse = await authMiddleware(request);
    if (authResponse instanceof NextResponse) {
      return authResponse;
    }
    
    await dbConnect();
    
    const { id } = params;
    
    // Get user ID from headers set by middleware
    const userId = request.headers.get('x-user-id');
    
    // Get review data from request body
    const { rating, title, comment } = await request.json();
    
    // Validate required fields
    if (!rating || !title || !comment) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Check if tool exists
    const tool = await Tool.findById(id);
    
    if (!tool) {
      return NextResponse.json(
        { error: 'Tool not found' },
        { status: 404 }
      );
    }
    
    // Check if user has already reviewed this tool
    const existingReview = await Review.findOne({
      user: userId,
      tool: id,
    });
    
    if (existingReview) {
      return NextResponse.json(
        { error: 'You have already reviewed this tool' },
        { status: 400 }
      );
    }
    
    // Create new review
    const review = await Review.create({
      user: userId,
      tool: id,
      rating,
      title,
      comment,
    });
    
    // Update tool rating
    const allReviews = await Review.find({ tool: id });
    const totalRating = allReviews.reduce((acc, review) => acc + review.rating, 0);
    const averageRating = totalRating / allReviews.length;
    
    tool.rating = averageRating;
    tool.ratingCount = allReviews.length;
    
    await tool.save();
    
    return NextResponse.json(review, { status: 201 });
  } catch (error) {
    console.error('Error creating review:', error);
    return NextResponse.json(
      { error: 'Failed to create review' },
      { status: 500 }
    );
  }
} 