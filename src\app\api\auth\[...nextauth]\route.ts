import NextAuth from 'next-auth'
import Cred<PERSON><PERSON><PERSON>rovider from 'next-auth/providers/credentials'
import GoogleProvider from 'next-auth/providers/google'
import GitHubProvider from 'next-auth/providers/github'
import dbConnect from '@/lib/db'
import User from '@/models/User'
import bcrypt from 'bcryptjs'

const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        try {
          await dbConnect()
          
          // Find user by email and include password field
          const user = await User.findOne({ email: credentials.email }).select('+password')
          
          if (!user) {
            return null
          }
          
          // Check if password matches
          const isMatch = await bcrypt.compare(credentials.password, user.password)
          
          if (!isMatch) {
            return null
          }
          
          return {
            id: user._id.toString(),
            email: user.email,
            name: user.name,
            role: user.role,
            isPaid: user.isPaid,
            subscriptionStatus: user.subscriptionStatus,
            image: user.image
          }
        } catch (error) {
          console.error('Auth error:', error)
          return null
        }
      }
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
    }),
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID || '',
      clientSecret: process.env.GITHUB_CLIENT_SECRET || '',
    })
  ],
  callbacks: {
    async jwt({ token, user, account }) {
      // Persist the OAuth access_token and or the user id to the token right after signin
      if (account && user) {
        token.accessToken = account.access_token
        token.id = user.id
        token.role = user.role
        token.isPaid = user.isPaid
        token.subscriptionStatus = user.subscriptionStatus
      }
      
      // Refresh user data from database on each request
      if (token.id) {
        try {
          await dbConnect()
          const dbUser = await User.findById(token.id)
          if (dbUser) {
            token.role = dbUser.role
            token.isPaid = dbUser.isPaid
            token.subscriptionStatus = dbUser.subscriptionStatus
          }
        } catch (error) {
          console.error('Error refreshing user data:', error)
        }
      }
      
      return token
    },
    async session({ session, token }) {
      // Send properties to the client
      if (token) {
        session.user.id = token.id
        session.user.role = token.role
        session.user.isPaid = token.isPaid
        session.user.subscriptionStatus = token.subscriptionStatus
      }
      return session
    },
    async signIn({ user, account, profile }) {
      if (account?.provider === 'google' || account?.provider === 'github') {
        try {
          await dbConnect()
          
          // Check if user exists
          let existingUser = await User.findOne({ email: user.email })
          
          if (!existingUser) {
            // Create new user for OAuth
            existingUser = await User.create({
              name: user.name,
              email: user.email,
              image: user.image,
              password: 'oauth-user', // Placeholder password for OAuth users
              isPaid: false,
              subscriptionStatus: 'inactive'
            })
          }
          
          // Update user object with database info
          user.id = existingUser._id.toString()
          user.role = existingUser.role
          user.isPaid = existingUser.isPaid
          user.subscriptionStatus = existingUser.subscriptionStatus
          
          return true
        } catch (error) {
          console.error('OAuth sign in error:', error)
          return false
        }
      }
      
      return true
    }
  },
  pages: {
    signIn: '/login',
    signUp: '/register',
  },
  session: {
    strategy: 'jwt',
  },
  secret: process.env.NEXTAUTH_SECRET,
})

export { handler as GET, handler as POST }
