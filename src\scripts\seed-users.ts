import dbConnect from '../lib/db';
import User from '../models/User';

// Define initial users
const users = [
  {
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    image: ''
  },
  {
    name: 'Regular User',
    email: '<EMAIL>',
    password: 'user123',
    role: 'user',
    image: ''
  }
];

async function seedUsers() {
  try {
    // Connect to database
    await dbConnect();
    console.log('Connected to database');

    // Create new users
    for (const userData of users) {
      const existingUser = await User.findOne({ email: userData.email });
      
      if (!existingUser) {
        await User.create(userData);
        console.log(`Created user: ${userData.name}`);
      } else {
        console.log(`User already exists: ${userData.name}`);
      }
    }

    console.log('Users seeded successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding users:', error);
    process.exit(1);
  }
}

seedUsers(); 