'use client'

import React, { useState, useEffect } from 'react';
import { fetchTools } from '@/services/toolsService';
import { apiCall } from '@/hooks/useClientToken';

export default function TestApiPage() {
  const [tools, setTools] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const testApis = async () => {
      try {
        setLoading(true);
        
        // Test tools API
        console.log('Testing tools API...');
        const toolsResponse = await fetchTools({ limit: 5 });
        console.log('Tools response:', toolsResponse);
        setTools(toolsResponse.tools || []);
        
        // Test categories API
        console.log('Testing categories API...');
        const categoriesResponse = await apiCall('/api/categories');
        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json();
          console.log('Categories response:', categoriesData);
          setCategories(categoriesData);
        } else {
          throw new Error('Failed to fetch categories');
        }
        
      } catch (err) {
        console.error('API test error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    testApis();
  }, []);

  if (loading) {
    return <div className="p-8">Loading API tests...</div>;
  }

  if (error) {
    return <div className="p-8 text-red-500">Error: {error}</div>;
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">API Test Results</h1>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Tools API ({tools.length} tools)</h2>
        <div className="grid gap-4">
          {tools.slice(0, 3).map((tool) => (
            <div key={tool._id} className="border p-4 rounded">
              <h3 className="font-medium">{tool.name}</h3>
              <p className="text-sm text-gray-600">{tool.description}</p>
              <p className="text-xs mt-2">Category: {tool.category?.name || 'N/A'}</p>
            </div>
          ))}
        </div>
      </div>
      
      <div>
        <h2 className="text-xl font-semibold mb-4">Categories API ({categories.length} categories)</h2>
        <div className="grid gap-2">
          {categories.slice(0, 5).map((category) => (
            <div key={category._id} className="border p-2 rounded">
              <strong>{category.name}</strong> - {category.toolCount || 0} tools
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
