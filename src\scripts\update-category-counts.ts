import dbConnect from '../lib/db';
import Category from '../models/Category';
import Tool from '../models/Tool';

async function updateCategoryCounts() {
  console.log('🔄 Updating category counts...');
  
  try {
    await dbConnect();
    
    // Get all categories
    const categories = await Category.find();
    console.log(`📁 Found ${categories.length} categories`);
    
    // Update tool count for each category
    for (const category of categories) {
      const toolCount = await Tool.countDocuments({ category: category._id });
      
      if (category.toolCount !== toolCount) {
        await Category.findByIdAndUpdate(category._id, { toolCount });
        console.log(`✅ Updated ${category.name}: ${category.toolCount} → ${toolCount} tools`);
      } else {
        console.log(`✔️  ${category.name}: ${toolCount} tools (unchanged)`);
      }
    }
    
    console.log('🎉 Category counts updated successfully!');
    
  } catch (error) {
    console.error('❌ Error updating category counts:', error);
    throw error;
  }
}

// Run the update if this script is executed directly
if (require.main === module) {
  updateCategoryCounts()
    .then(() => {
      console.log('\n✅ Category count update completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Update failed:', error);
      process.exit(1);
    });
}

export { updateCategoryCounts };
