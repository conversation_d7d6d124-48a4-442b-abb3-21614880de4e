import React from 'react'
import { render, screen, fireEvent, act } from '@testing-library/react'
import SearchInput from './SearchInput'

// Mock setTimeout and clearTimeout
jest.useFakeTimers()

describe('SearchInput Component', () => {
  const mockOnSearch = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.runOnlyPendingTimers()
  })

  it('renders with default props', () => {
    render(<SearchInput onSearch={mockOnSearch} />)
    expect(screen.getByPlaceholderText('Search AI tools...')).toBeInTheDocument()
    expect(screen.getByRole('searchbox')).toBeInTheDocument()
  })

  it('renders with custom placeholder', () => {
    render(<SearchInput onSearch={mockOnSearch} placeholder="Custom placeholder" />)
    expect(screen.getByPlaceholderText('Custom placeholder')).toBeInTheDocument()
  })

  it('renders with initial value', () => {
    render(<SearchInput onSearch={mockOnSearch} initialValue="Initial search" />)
    expect(screen.getByRole('searchbox')).toHaveValue('Initial search')
  })

  it('calls onSearch after debounce when typing', () => {
    render(<SearchInput onSearch={mockOnSearch} debounceMs={500} />)
    
    const input = screen.getByRole('searchbox')
    fireEvent.change(input, { target: { value: 'test query' } })
    
    // onSearch should not be called immediately
    expect(mockOnSearch).not.toHaveBeenCalled()
    
    // Fast-forward timers
    act(() => {
      jest.advanceTimersByTime(500)
    })
    
    // Now onSearch should be called
    expect(mockOnSearch).toHaveBeenCalledWith('test query')
  })

  it('clears input and calls onSearch when clear button is clicked', () => {
    render(<SearchInput onSearch={mockOnSearch} initialValue="test query" />)
    
    const clearButton = screen.getByRole('button', { name: /clear search/i })
    fireEvent.click(clearButton)
    
    expect(screen.getByRole('searchbox')).toHaveValue('')
    expect(mockOnSearch).toHaveBeenCalledWith('')
  })

  it('calls onSearch immediately when form is submitted', () => {
    render(<SearchInput onSearch={mockOnSearch} initialValue="test query" />)
    
    const form = screen.getByRole('form')
    fireEvent.submit(form)
    
    expect(mockOnSearch).toHaveBeenCalledWith('test query')
  })

  it('does not show clear button when input is empty', () => {
    render(<SearchInput onSearch={mockOnSearch} />)
    expect(screen.queryByRole('button', { name: /clear search/i })).not.toBeInTheDocument()
  })

  it('shows clear button when input has value', () => {
    render(<SearchInput onSearch={mockOnSearch} initialValue="test query" />)
    expect(screen.getByRole('button', { name: /clear search/i })).toBeInTheDocument()
  })
}) 