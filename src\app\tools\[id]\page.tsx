import { Tool } from '@/types/tool';
import { But<PERSON> } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import Breadcrumbs from '@/components/ui/Breadcrumbs';
import { ArrowLeft, ExternalLink, Star, Calendar, Globe, DollarSign, Zap, Users, Code, BookOpen } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import dbConnect from '@/lib/db';
import ToolModel from '@/models/Tool';
import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import StructuredData from '@/components/seo/StructuredData';
import { generateToolPageMetadata, generateToolJsonLd, generateBreadcrumbJsonLd, seoConfig } from '@/lib/seo';

interface PageProps {
  params: { id: string }
}

async function getTool(id: string): Promise<Tool | null> {
  try {
    await dbConnect();
    const tool = await ToolModel.findById(id).populate('category', 'name slug').lean();
    
    if (!tool) {
      return null;
    }

    // Convert MongoDB document to plain object
    return {
      ...tool,
      _id: (tool as any)._id.toString(),
      category: (tool as any).category ? {
        ...(tool as any).category,
        _id: (tool as any).category._id.toString()
      } : null,
      createdAt: (tool as any).createdAt?.toISOString(),
      updatedAt: (tool as any).updatedAt?.toISOString()
    } as unknown as Tool;
  } catch (error) {
    console.error('Error fetching tool:', error);
    return null;
  }
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const tool = await getTool(params.id);
  
  if (!tool) {
    return {
      title: 'Tool Not Found',
      description: 'The requested AI tool could not be found.'
    };
  }

  return generateToolPageMetadata(tool);
}

export default async function ToolPage({ params }: PageProps) {
  const tool = await getTool(params.id);

  if (!tool) {
    notFound();
  }

  const categoryName = typeof tool.category === 'string' ? tool.category : (tool.category?.name || 'AI Tools')
  
  // Breadcrumb data for structured data
  const breadcrumbs = [
    { name: "Home", url: seoConfig.siteUrl },
    { name: "Tools", url: `${seoConfig.siteUrl}/tools` },
    { name: categoryName, url: `${seoConfig.siteUrl}/categories/${typeof tool.category === 'string' ? tool.category : (tool.category?.slug || 'other')}` },
    { name: tool.name, url: `${seoConfig.siteUrl}/tools/${tool._id}` }
  ]

  return (
    <>
      {/* Structured Data for Tool Page */}
      <StructuredData data={[
        generateToolJsonLd(tool),
        generateBreadcrumbJsonLd(breadcrumbs)
      ]} />
      
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        {/* Breadcrumbs */}
        <div className="container mx-auto px-4 pt-8">
          <Breadcrumbs 
            items={[
              { name: "Tools", url: "/tools" },
              { name: categoryName, url: `/categories/${typeof tool.category === 'string' ? tool.category : (tool.category?.slug || 'other')}` },
              { name: tool.name, url: `/tools/${tool._id}`, current: true }
            ]}
          />
        </div>
        
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white mt-4">
        <div className="container mx-auto px-4 py-8">
          <Link href="/tools">
            <Button variant="ghost" className="mb-6 text-white hover:bg-white/10">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Tools
            </Button>
          </Link>
          
          <div className="flex flex-col md:flex-row items-start gap-8">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-4">
                {tool.imageUrl && (
                  <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-white/10 backdrop-blur">
                    <Image
                      src={tool.imageUrl}
                      alt={tool.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                )}
                <div>
                  <h1 className="text-4xl font-bold mb-2">{tool.name}</h1>
                  {tool.company && (
                    <p className="text-blue-100">by {tool.company}</p>
                  )}
                </div>
              </div>
              
              <p className="text-xl text-blue-100 mb-6 leading-relaxed">
                {tool.description}
              </p>

              <div className="flex flex-wrap gap-3">
                {tool.url && (
                  <Button 
                    asChild 
                    variant="secondary" 
                    size="lg"
                    className="bg-white text-blue-600 hover:bg-gray-100"
                  >
                    <a href={tool.url} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="mr-2 h-5 w-5" />
                      Visit Website
                    </a>
                  </Button>
                )}
                
                <Badge variant="outline" className="border-white/30 text-white text-lg px-4 py-2">
                  {typeof tool.category === 'string' 
                    ? tool.category 
                    : tool.category && tool.category.name 
                      ? tool.category.name 
                      : 'Uncategorized'
                  }
                </Badge>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="bg-white/10 backdrop-blur rounded-lg p-6 min-w-64">
              <h3 className="text-lg font-semibold mb-4">Quick Info</h3>
              <div className="space-y-3">
                {tool.pricing && (
                  <div className="flex items-center">
                    <DollarSign className="w-5 h-5 mr-3" />
                    <span>{tool.pricing}</span>
                  </div>
                )}
                {tool.rating > 0 && (
                  <div className="flex items-center">
                    <Star className="w-5 h-5 mr-3 fill-current" />
                    <span>{tool.rating}/5 ({tool.ratingCount || 0} reviews)</span>
                  </div>
                )}
                {tool.apiAvailable && (
                  <div className="flex items-center">
                    <Code className="w-5 h-5 mr-3" />
                    <span>API Available</span>
                  </div>
                )}
                <div className="flex items-center">
                  <Calendar className="w-5 h-5 mr-3" />
                  <span>Added {new Date(tool.createdAt || Date.now()).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content Column */}
          <div className="lg:col-span-2 space-y-8">
            
            {/* Features Section */}
            {tool.features && tool.features.length > 0 && (
              <Card className="shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Zap className="w-6 h-6 mr-2 text-yellow-500" />
                    Key Features
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {tool.features.map((feature, index) => (
                      <div key={index} className="flex items-start">
                        <div className="w-2 h-2 rounded-full bg-blue-500 mt-2 mr-3 flex-shrink-0"></div>
                        <span className="text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Detailed Information */}
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BookOpen className="w-6 h-6 mr-2 text-blue-500" />
                  About {tool.name}
                </CardTitle>
              </CardHeader>
              <CardContent className="prose max-w-none">
                <div className="text-gray-700 leading-relaxed">
                  <p className="mb-4">{tool.description}</p>
                  
                  {tool.company && (
                    <div className="bg-gray-50 p-4 rounded-lg mb-4">
                      <h4 className="font-semibold text-gray-800 mb-2">About the Company</h4>
                      <p>Developed by <strong>{tool.company}</strong>, this tool represents cutting-edge AI technology designed to enhance productivity and innovation.</p>
                    </div>
                  )}

                  <div className="border-l-4 border-blue-500 pl-4 mb-4">
                    <p className="text-gray-600 italic">
                      This AI tool is part of our curated collection of {typeof tool.category === 'string' ? tool.category : tool.category.name} solutions, 
                      carefully selected for their quality, innovation, and user value.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Usage Guidelines */}
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="w-6 h-6 mr-2 text-green-500" />
                  Who Should Use This Tool?
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-2">Perfect For:</h4>
                    <ul className="space-y-1 text-gray-600">
                      <li>• Professionals seeking AI automation</li>
                      <li>• Developers and technical teams</li>
                      <li>• Content creators and marketers</li>
                      <li>• Students and researchers</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-2">Use Cases:</h4>
                    <ul className="space-y-1 text-gray-600">
                      <li>• Streamline workflows</li>
                      <li>• Enhance productivity</li>
                      <li>• Automate repetitive tasks</li>
                      <li>• Generate insights</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Pricing Details */}
            {tool.pricing && (
              <Card className="shadow-lg">
                <CardHeader>
                  <CardTitle className="text-lg">Pricing</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-2">
                      {tool.pricing}
                    </div>
                    <p className="text-gray-600 text-sm">
                      {tool.pricing.toLowerCase().includes('free') 
                        ? 'No cost to get started' 
                        : 'Competitive pricing for AI capabilities'
                      }
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Technical Details */}
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="text-lg">Technical Info</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <Badge variant={tool.status === 'active' ? 'default' : 'secondary'}>
                      {tool.status || 'Active'}
                    </Badge>
                  </div>
                  {tool.apiAvailable && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">API:</span>
                      <Badge variant="outline" className="text-green-600">Available</Badge>
                    </div>
                  )}
                  {tool.source && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Source:</span>
                      <span className="text-xs text-gray-500 capitalize">{tool.source}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Related Tools */}
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="text-lg">Explore More</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Link href={`/categories/${typeof tool.category === 'string' ? tool.category : tool.category.slug}`}>
                    <Button variant="outline" className="w-full justify-start">
                      <Globe className="mr-2 h-4 w-4" />
                      More {typeof tool.category === 'string' ? tool.category : tool.category.name} Tools
                    </Button>
                  </Link>
                  <Link href="/tools">
                    <Button variant="outline" className="w-full justify-start">
                      <BookOpen className="mr-2 h-4 w-4" />
                      Browse All Tools
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
    </>
  );
} 