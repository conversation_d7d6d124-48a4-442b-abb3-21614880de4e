{"name": "aitools", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "compile-scripts": "tsc -p tsconfig.scripts.json", "import-tools": "npm run compile-scripts && node dist/scripts/import-tools.js", "seed-categories": "npm run compile-scripts && node dist/scripts/seed-categories.js", "seed-users": "npm run compile-scripts && node dist/scripts/seed-users.js", "seed-reviews": "npm run compile-scripts && node dist/scripts/seed-reviews.js", "seed-all": "npm run seed-categories && npm run seed-users && npm run import-tools && npm run seed-reviews", "clean-duplicates": "npm run compile-scripts && node dist/scripts/clean-duplicate-tools.js", "analyze-duplicates": "npm run compile-scripts && node dist/scripts/analyze-duplicate-ids.js", "test": "jest", "test:watch": "jest --watch", "test:tool-pages": "jest src/tests/tool-pages-integration.test.ts --detect<PERSON><PERSON>Handles", "test:all-tools": "node src/tests/tool-pages-test.js"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@types/cheerio": "^0.22.35", "@types/jsonwebtoken": "^9.0.9", "@types/node-cron": "^3.0.11", "bcryptjs": "^2.4.3", "cheerio": "^1.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.0.3", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.263.1", "mongodb": "^6.3.0", "mongoose": "^8.1.0", "next": "14.1.0", "next-auth": "^4.24.5", "node-cron": "^4.2.1", "node-fetch": "^2.7.0", "nodemailer": "^6.9.8", "puppeteer": "^24.12.1", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.50.0", "react-icons": "^5.0.1", "rss-parser": "^3.13.0", "tailwind-merge": "^3.2.0", "web-vitals": "^5.0.3", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.11", "@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5"}}