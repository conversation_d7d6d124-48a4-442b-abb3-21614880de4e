'use client'

import React, { useState } from 'react';
import Link from 'next/link';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

const MyToolsPage: React.FC = () => {
  const [filter, setFilter] = useState('all');

  const tools = [
    {
      id: 1,
      name: 'AI Content Generator Pro',
      category: 'Content Creation',
      status: 'approved',
      submittedDate: '2024-01-10',
      views: 847,
      clicks: 23
    },
    {
      id: 2,
      name: 'Smart Image Enhancer',
      category: 'Image Processing',
      status: 'approved',
      submittedDate: '2024-01-05',
      views: 234,
      clicks: 15
    },
    {
      id: 3,
      name: 'Voice Recognition API',
      category: 'Audio Processing',
      status: 'pending',
      submittedDate: '2024-01-03',
      views: 0,
      clicks: 0
    },
    {
      id: 4,
      name: 'Smart Code Assistant',
      category: 'Development Tools',
      status: 'rejected',
      submittedDate: '2023-12-28',
      views: 0,
      clicks: 0,
      rejectionReason: 'Insufficient documentation provided'
    }
  ];

  const filteredTools = tools.filter(tool => {
    if (filter === 'all') return true;
    return tool.status === filter;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">Approved</span>;
      case 'pending':
        return <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">Under Review</span>;
      case 'rejected':
        return <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-sm">Rejected</span>;
      default:
        return <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-sm">Unknown</span>;
    }
  };

  const handleEditTool = (toolId: number) => {
    alert(`Edit functionality for tool ${toolId} coming soon!`);
  };

  const handleDeleteTool = (toolId: number) => {
    if (confirm('Are you sure you want to delete this tool submission?')) {
      alert(`Delete functionality for tool ${toolId} coming soon!`);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-12">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-2">
            My Tool Submissions
          </h1>
          <p className="text-lg text-muted-foreground">
            Manage and track your submitted AI tools
          </p>
        </div>

        {/* Filter Buttons */}
        <Card className="p-4 mb-8">
          <div className="flex flex-wrap gap-2">
            <Button
              variant={filter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('all')}
            >
              All ({tools.length})
            </Button>
            <Button
              variant={filter === 'approved' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('approved')}
            >
              Approved ({tools.filter(t => t.status === 'approved').length})
            </Button>
            <Button
              variant={filter === 'pending' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('pending')}
            >
              Pending ({tools.filter(t => t.status === 'pending').length})
            </Button>
            <Button
              variant={filter === 'rejected' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('rejected')}
            >
              Rejected ({tools.filter(t => t.status === 'rejected').length})
            </Button>
          </div>
        </Card>

        {/* Tools List */}
        <div className="space-y-6">
          {filteredTools.length === 0 ? (
            <Card className="p-8 text-center">
              <p className="text-muted-foreground mb-4">No tools found for the selected filter.</p>
              <Link href="/submit-tool">
                <Button>Submit Your First Tool</Button>
              </Link>
            </Card>
          ) : (
            filteredTools.map(tool => (
              <Card key={tool.id} className="p-6">
                <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="text-xl font-semibold text-foreground">{tool.name}</h3>
                      {getStatusBadge(tool.status)}
                    </div>
                    
                    <p className="text-muted-foreground mb-2">
                      Category: {tool.category} • Submitted: {new Date(tool.submittedDate).toLocaleDateString()}
                    </p>
                    
                    {tool.status === 'approved' && (
                      <div className="flex gap-4 text-sm text-muted-foreground mb-4">
                        <span>Views: <strong className="text-foreground">{tool.views}</strong></span>
                        <span>Clicks: <strong className="text-foreground">{tool.clicks}</strong></span>
                        <span>CTR: <strong className="text-foreground">{tool.views > 0 ? ((tool.clicks / tool.views) * 100).toFixed(1) : 0}%</strong></span>
                      </div>
                    )}
                    
                    {tool.status === 'rejected' && tool.rejectionReason && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-md mb-4">
                        <p className="text-red-600 text-sm">
                          <strong>Rejection reason:</strong> {tool.rejectionReason}
                        </p>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex gap-2">
                    {tool.status === 'approved' && (
                      <Link href={`/tools/${tool.id}`}>
                        <Button variant="outline" size="sm">View Live</Button>
                      </Link>
                    )}
                    
                    {(tool.status === 'pending' || tool.status === 'rejected') && (
                      <Button variant="outline" size="sm" onClick={() => handleEditTool(tool.id)}>
                        Edit
                      </Button>
                    )}
                    
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => handleDeleteTool(tool.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>

        {/* Action Buttons */}
        <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/submit-tool">
            <Button>Submit New Tool</Button>
          </Link>
          <Link href="/analytics">
            <Button variant="outline">View Analytics</Button>
          </Link>
          <Link href="/dashboard">
            <Button variant="outline">Back to Dashboard</Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default MyToolsPage;
