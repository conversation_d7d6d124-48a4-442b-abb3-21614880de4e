import dbConnect from '../lib/db';
import Category from '../models/Category';
import Tool from '../models/Tool';

// Tool to category mapping - this should match the import-all-tools.ts mapping
const toolCategories: { [key: string]: string } = {
  // Language Models
  'ChatGPT': 'Language Models',
  'Claude': 'Language Models',
  'Bard': 'Language Models',
  'Bing Chat': 'Language Models',
  'Anthropic': 'Language Models',
  'Cohere': 'Language Models',
  'Perplexity AI': 'Language Models',
  'You.com': 'Language Models',

  // Image Generation
  'Midjourney': 'Image Generation',
  'DALL-E': 'Image Generation',
  'Stable Diffusion': 'Image Generation',

  // Content Creation
  'Jasper': 'Content Creation',
  'Copy.ai': 'Content Creation',
  'Writesonic': 'Content Creation',
  'Rytr': 'Content Creation',
  'Tome': 'Content Creation',
  'Canva': 'Content Creation',
  'Beautiful.ai': 'Content Creation',
  'Gamma': 'Content Creation',
  'Pitch': 'Content Creation',
  'Slidebean': 'Content Creation',

  // Writing
  'Grammarly': 'Writing',
  'Wordtune': 'Writing',
  'Quillbot': 'Writing',
  'Hemingway': 'Writing',
  'ProWritingAid': 'Writing',
  'DeepL': 'Writing',
  'Linguix': 'Writing',
  'Scribens': 'Writing',
  'Antidote': 'Writing',
  'Reverso': 'Writing',

  // Productivity
  'Notion AI': 'Productivity',

  // Transcription
  'Otter.ai': 'Transcription',
  'AssemblyAI': 'Transcription',
  'Whisper': 'Transcription',

  // Video Editing
  'Descript': 'Video Editing',
  'Kapwing': 'Video Editing',

  // Video Generation
  'Synthesia': 'Video Generation',
  'Pictory': 'Video Generation',
  'RunwayML': 'Video Generation',
  'Lumen5': 'Video Generation',

  // Code Generation
  'GitHub Copilot': 'Code Generation',
  'Amazon CodeWhisperer': 'Code Generation',
  'Codeium': 'Code Generation',
  'Tabnine': 'Code Generation',
  'Phind': 'Code Generation',

  // Education
  'Duolingo': 'Education',
  'Babbel': 'Education',
  'Rosetta Stone': 'Education',
  'Busuu': 'Education',
  'Memrise': 'Education',
  'Anki': 'Education',
  'Quizlet': 'Education',
  'Kahoot': 'Education',
  'Coursera': 'Education',
  'Udemy': 'Education',
  'edX': 'Education',
  'Skillshare': 'Education',
  'Brilliant': 'Education',
  'Khan Academy': 'Education',
  'DataCamp': 'Education',
  'Codecademy': 'Education',
  'freeCodeCamp': 'Education',
  'Pluralsight': 'Education',
  'Udacity': 'Education',
  'Treehouse': 'Education',
  'LinkedIn Learning': 'Education',
  'Masterclass': 'Education',
  'Skillsoft': 'Education',

  // Audio AI
  'Eleven Labs': 'Audio AI',
  'Murf.ai': 'Audio AI',
  'Play.ht': 'Audio AI',
  'Resemble.ai': 'Audio AI',

  // Other
  'Hugging Face': 'Other',
  'Replicate': 'Other'
};

async function fixAllToolCategories() {
  try {
    await dbConnect();
    console.log('🔧 Fixing tool categories...\n');

    // Get all categories
    const categories = await Category.find({});
    const categoryMap = new Map<string, string>();
    
    categories.forEach(category => {
      categoryMap.set(category.name, category._id.toString());
    });

    // Get all tools
    const tools = await Tool.find({});
    console.log(`Found ${tools.length} tools to process`);
    
    let updatedCount = 0;
    let errorCount = 0;

    for (const tool of tools) {
      try {
        const expectedCategory = toolCategories[tool.name] || 'Other';
        const expectedCategoryId = categoryMap.get(expectedCategory);
        
        if (!expectedCategoryId) {
          console.log(`❌ Category '${expectedCategory}' not found for tool: ${tool.name}`);
          errorCount++;
          continue;
        }

        // Check if tool needs updating
        if (tool.category.toString() !== expectedCategoryId) {
          await Tool.findByIdAndUpdate(tool._id, { 
            category: expectedCategoryId 
          });
          console.log(`✅ Updated ${tool.name}: ${expectedCategory}`);
          updatedCount++;
        } else {
          console.log(`⚪ ${tool.name}: Already in correct category (${expectedCategory})`);
        }
      } catch (error) {
        console.log(`❌ Error updating tool ${tool.name}:`, error);
        errorCount++;
      }
    }

    console.log('\n📊 Fix Summary:');
    console.log(`✅ Updated: ${updatedCount} tools`);
    console.log(`❌ Errors: ${errorCount} tools`);
    console.log(`📝 Total processed: ${tools.length} tools`);

    // Show final category distribution
    console.log('\n📁 Final Category Distribution:');
    for (const category of categories.sort((a, b) => a.order - b.order)) {
      const toolCount = await Tool.countDocuments({ category: category._id });
      console.log(`   ${category.name}: ${toolCount} tools`);
    }

    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

fixAllToolCategories();
