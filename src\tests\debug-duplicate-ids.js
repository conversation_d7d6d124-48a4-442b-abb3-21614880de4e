/**
 * Debug script to identify duplicate IDs in API responses
 */

// @ts-ignore
global.fetch = require('node-fetch')

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3001'

async function debugDuplicateIds() {
  console.log('🔍 Debugging duplicate IDs in API responses...\n')
  
  try {
    // Fetch all tools via API (same as the test)
    const firstResponse = await fetch(`${BASE_URL}/api/tools?limit=1`)
    const firstData = await firstResponse.json()
    const totalToolCount = firstData.total
    
    console.log(`📊 Total tools in database: ${totalToolCount}`)
    
    const allTools: any[] = []
    const batchSize = 100
    const totalPages = Math.ceil(totalToolCount / batchSize)
    
    console.log(`📥 Fetching in ${totalPages} batches of ${batchSize}...`)
    
    for (let page = 1; page <= totalPages; page++) {
      const response = await fetch(`${BASE_URL}/api/tools?page=${page}&limit=${batchSize}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch tools page ${page}: ${response.statusText}`)
      }
      
      const data = await response.json()
      console.log(`📄 Page ${page}: ${data.tools.length} tools, total so far: ${allTools.length + data.tools.length}`)
      
      allTools.push(...data.tools)
    }
    
    console.log(`\n📊 Fetched ${allTools.length} tools total`)
    
    // Check for duplicates
    const seenIds = new Set<string>()
    const duplicateIds = new Set<string>()
    const duplicateDetails: any[] = []
    
    allTools.forEach((tool, index) => {
      if (seenIds.has(tool._id)) {
        duplicateIds.add(tool._id)
        
        // Find the original occurrence
        const originalIndex = allTools.findIndex(t => t._id === tool._id)
        duplicateDetails.push({
          id: tool._id,
          name: tool.name,
          originalIndex,
          duplicateIndex: index,
          originalName: allTools[originalIndex].name
        })
      } else {
        seenIds.add(tool._id)
      }
    })
    
    console.log(`\n🔍 Found ${duplicateIds.size} unique duplicate IDs`)
    console.log(`📊 Total duplicate occurrences: ${duplicateDetails.length}`)
    
    if (duplicateDetails.length > 0) {
      console.log('\n📋 First 10 duplicates:')
      duplicateDetails.slice(0, 10).forEach((dup, index) => {
        console.log(`  ${index + 1}. ID: ${dup.id}`)
        console.log(`     Original at index ${dup.originalIndex}: "${dup.originalName}"`)
        console.log(`     Duplicate at index ${dup.duplicateIndex}: "${dup.name}"`)
        console.log('')
      })
      
      // Check if duplicates appear in specific page ranges
      console.log('📊 Analyzing duplicate distribution by page:')
      const duplicatesByPage = new Map<number, number>()
      
      duplicateDetails.forEach(dup => {
        const page = Math.ceil((dup.duplicateIndex + 1) / batchSize)
        duplicatesByPage.set(page, (duplicatesByPage.get(page) || 0) + 1)
      })
      
      Array.from(duplicatesByPage.entries())
        .sort(([a], [b]) => a - b)
        .forEach(([page, count]) => {
          console.log(`  Page ${page}: ${count} duplicates`)
        })
    }
    
  } catch (error) {
    console.error('❌ Error:', error)
  }
}

debugDuplicateIds()
  .then(() => console.log('\n🎉 Debug completed!'))
  .catch(error => console.error('💥 Debug failed:', error))
