import mongoose from 'mongoose';

// Define the schema for the Category model
const CategorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please provide a name for the category'],
    trim: true,
    unique: true,
    maxlength: [50, 'Name cannot be more than 50 characters'],
  },
  description: {
    type: String,
    required: [true, 'Please provide a description for the category'],
    trim: true,
    maxlength: [500, 'Description cannot be more than 500 characters'],
  },
  icon: {
    type: String,
    default: '',
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
  },
  parent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    default: null,
  },
  order: {
    type: Number,
    default: 0,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  toolCount: {
    type: Number,
    default: 0,
  },
});

// Update the updatedAt field before saving
CategorySchema.pre('save', function(this: any, next: () => void) {
  this.updatedAt = new Date();
  next();
});

// Create text indexes for search functionality
CategorySchema.index({ name: 'text', description: 'text' });

// Check if the model already exists to prevent overwriting
export default mongoose.models.Category || mongoose.model('Category', CategorySchema); 