#!/usr/bin/env node

/**
 * Tool Pages Integration Test Runner
 * This script tests all tool pages to ensure they are functional
 */

const http = require('http');
const https = require('https');

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';

// Simple fetch implementation for Node.js
function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          statusText: res.statusMessage,
          text: () => Promise.resolve(data),
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });

    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

class ToolPagesTest {
  constructor() {
    this.allTools = [];
    this.totalToolCount = 0;
    this.testResults = {
      passed: 0,
      failed: 0,
      errors: []
    };
  }

  log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      'info': '🔍',
      'success': '✅',
      'error': '❌',
      'warning': '⚠️'
    }[level] || 'ℹ️';
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  async fetchAllTools() {
    this.log('Fetching all tools from the database...');
    
    try {
      // Get total count first
      const firstResponse = await fetch(`${BASE_URL}/api/tools?limit=1`);
      if (!firstResponse.ok) {
        throw new Error(`Failed to fetch tools: ${firstResponse.statusText}`);
      }
      
      const firstData = await firstResponse.json();
      this.totalToolCount = firstData.total;
      
      this.log(`Found ${this.totalToolCount} tools in database`);
      
      // Fetch all tools in batches
      const batchSize = 100;
      const totalPages = Math.ceil(this.totalToolCount / batchSize);
      
      for (let page = 1; page <= totalPages; page++) {
        const response = await fetch(`${BASE_URL}/api/tools?page=${page}&limit=${batchSize}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch tools page ${page}: ${response.statusText}`);
        }
        
        const data = await response.json();
        this.allTools.push(...data.tools);
        
        if (page % 10 === 0 || page === totalPages) {
          this.log(`Fetched ${this.allTools.length}/${this.totalToolCount} tools (${Math.round((this.allTools.length / this.totalToolCount) * 100)}%)`);
        }
      }
      
      this.log(`Successfully fetched all ${this.allTools.length} tools`, 'success');
      
    } catch (error) {
      this.log(`Failed to fetch tools: ${error.message}`, 'error');
      throw error;
    }
  }

  async testDataIntegrity() {
    this.log('Testing data integrity...');
    
    const issues = [];
    
    // Check for required fields
    this.allTools.forEach((tool, index) => {
      const toolIssues = [];
      
      if (!tool._id) toolIssues.push('missing _id');
      if (!tool.name || tool.name.trim() === '') toolIssues.push('missing or empty name');
      if (!tool.description || tool.description.trim() === '') toolIssues.push('missing or empty description');
      if (!tool.category) toolIssues.push('missing category');
      if (typeof tool.rating !== 'number' || tool.rating < 0 || tool.rating > 5) {
        toolIssues.push('invalid rating');
      }
      
      if (toolIssues.length > 0) {
        issues.push(`Tool ${index + 1} (${tool._id}): ${toolIssues.join(', ')}`);
      }
    });
    
    // Check for duplicates
    const seenIds = new Set();
    const duplicates = [];
    
    this.allTools.forEach(tool => {
      if (seenIds.has(tool._id)) {
        duplicates.push(tool._id);
      } else {
        seenIds.add(tool._id);
      }
    });
    
    if (duplicates.length > 0) {
      issues.push(`Duplicate IDs found: ${duplicates.join(', ')}`);
    }
    
    // Check ID format
    const invalidIds = this.allTools.filter(tool => !/^[a-f\d]{24}$/i.test(tool._id));
    if (invalidIds.length > 0) {
      issues.push(`Invalid ID format: ${invalidIds.length} tools`);
    }
    
    if (issues.length > 0) {
      this.log(`Data integrity issues found: ${issues.length}`, 'error');
      issues.slice(0, 10).forEach(issue => this.log(`  ${issue}`, 'error'));
      if (issues.length > 10) {
        this.log(`  ... and ${issues.length - 10} more issues`, 'error');
      }
      this.testResults.failed++;
      this.testResults.errors.push(...issues);
    } else {
      this.log(`Data integrity check passed for all ${this.allTools.length} tools`, 'success');
      this.testResults.passed++;
    }
  }

  async testApiEndpoints() {
    this.log('Testing API endpoints...');
    
    const sampleSize = Math.min(20, this.allTools.length);
    const sampleTools = [];
    
    // Get evenly distributed sample
    for (let i = 0; i < sampleSize; i++) {
      const index = Math.floor((i / sampleSize) * this.allTools.length);
      sampleTools.push(this.allTools[index]);
    }
    
    const failedEndpoints = [];
    
    for (const tool of sampleTools) {
      try {
        const response = await fetch(`${BASE_URL}/api/tools/${tool._id}`);
        
        if (!response.ok) {
          failedEndpoints.push(`${tool._id} (${tool.name}): ${response.status} ${response.statusText}`);
          continue;
        }
        
        const toolData = await response.json();
        
        // Verify the returned data matches
        if (toolData._id !== tool._id) {
          failedEndpoints.push(`${tool._id}: ID mismatch in API response`);
        }
        if (toolData.name !== tool.name) {
          failedEndpoints.push(`${tool._id}: Name mismatch in API response`);
        }
        
      } catch (error) {
        failedEndpoints.push(`${tool._id} (${tool.name}): ${error.message}`);
      }
    }
    
    if (failedEndpoints.length > 0) {
      this.log(`API endpoint failures: ${failedEndpoints.length}/${sampleSize}`, 'error');
      failedEndpoints.forEach(error => this.log(`  ${error}`, 'error'));
      this.testResults.failed++;
      this.testResults.errors.push(...failedEndpoints);
    } else {
      this.log(`All ${sampleSize} sampled API endpoints working correctly`, 'success');
      this.testResults.passed++;
    }
  }

  async testPageAccessibility() {
    this.log('Testing page accessibility...');
    
    const sampleSize = Math.min(15, this.allTools.length);
    const sampleTools = [];
    
    // Get evenly distributed sample
    for (let i = 0; i < sampleSize; i++) {
      const index = Math.floor((i / sampleSize) * this.allTools.length);
      sampleTools.push(this.allTools[index]);
    }
    
    const failedPages = [];
    
    for (const tool of sampleTools) {
      try {
        const response = await fetch(`${BASE_URL}/tools/${tool._id}`);
        
        if (!response.ok) {
          failedPages.push(`${tool._id} (${tool.name}): ${response.status} ${response.statusText}`);
        }
        
      } catch (error) {
        failedPages.push(`${tool._id} (${tool.name}): ${error.message}`);
      }
    }
    
    if (failedPages.length > 0) {
      this.log(`Page accessibility failures: ${failedPages.length}/${sampleSize}`, 'error');
      failedPages.forEach(error => this.log(`  ${error}`, 'error'));
      this.testResults.failed++;
      this.testResults.errors.push(...failedPages);
    } else {
      this.log(`All ${sampleSize} sampled tool pages accessible`, 'success');
      this.testResults.passed++;
    }
  }

  async testPageContent() {
    this.log('Testing page content...');
    
    const sampleSize = Math.min(5, this.allTools.length);
    const sampleTools = [];
    
    for (let i = 0; i < sampleSize; i++) {
      const index = Math.floor((i / sampleSize) * this.allTools.length);
      sampleTools.push(this.allTools[index]);
    }
    
    const contentIssues = [];
    
    for (const tool of sampleTools) {
      try {
        const response = await fetch(`${BASE_URL}/tools/${tool._id}`);
        
        if (!response.ok) {
          contentIssues.push(`${tool._id}: Failed to load page (${response.status})`);
          continue;
        }
        
        const pageContent = await response.text();
        
        // Check for essential content
        const checks = [
          { name: 'tool name', condition: pageContent.includes(tool.name) },
          { name: 'Next.js app', condition: pageContent.includes('__NEXT_DATA__') },
          { name: 'React hydration', condition: pageContent.includes('self.__next') || pageContent.includes('__NEXT_DATA__') }
        ];
        
        const failedChecks = checks.filter(check => !check.condition);
        
        if (failedChecks.length > 0) {
          const issues = failedChecks.map(check => check.name).join(', ');
          contentIssues.push(`${tool._id} (${tool.name}): Missing ${issues}`);
        }
        
      } catch (error) {
        contentIssues.push(`${tool._id}: ${error.message}`);
      }
    }
    
    if (contentIssues.length > 0) {
      this.log(`Page content issues: ${contentIssues.length}/${sampleSize}`, 'error');
      contentIssues.forEach(issue => this.log(`  ${issue}`, 'error'));
      this.testResults.failed++;
      this.testResults.errors.push(...contentIssues);
    } else {
      this.log(`All ${sampleSize} sampled tool pages contain essential content`, 'success');
      this.testResults.passed++;
    }
  }

  async testCategoryIntegration() {
    this.log('Testing category integration...');
    
    const categoryStats = new Map();
    const issues = [];
    
    this.allTools.forEach(tool => {
      const categoryName = typeof tool.category === 'string' 
        ? tool.category 
        : tool.category?.name;
      
      if (!categoryName) {
        issues.push(`${tool._id} (${tool.name}): No category name`);
        return;
      }
      
      categoryStats.set(categoryName, (categoryStats.get(categoryName) || 0) + 1);
    });
    
    if (issues.length > 0) {
      this.log(`Category integration issues: ${issues.length}`, 'error');
      issues.slice(0, 5).forEach(issue => this.log(`  ${issue}`, 'error'));
      if (issues.length > 5) {
        this.log(`  ... and ${issues.length - 5} more`, 'error');
      }
      this.testResults.failed++;
      this.testResults.errors.push(...issues);
    } else {
      this.log(`Category integration verified for all tools`, 'success');
      this.log(`Found ${categoryStats.size} unique categories`);
      
      // Show top categories
      const topCategories = Array.from(categoryStats.entries())
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5);
      
      this.log('Top 5 categories:');
      topCategories.forEach(([category, count]) => {
        this.log(`  ${category}: ${count} tools`);
      });
      
      this.testResults.passed++;
    }
  }

  printSummary() {
    this.log('\n='.repeat(60));
    this.log('TOOL PAGES INTEGRATION TEST SUMMARY', 'info');
    this.log('='.repeat(60));
    this.log(`📊 Total tools tested: ${this.allTools.length}`);
    this.log(`✅ Tests passed: ${this.testResults.passed}`);
    this.log(`❌ Tests failed: ${this.testResults.failed}`);
    
    if (this.testResults.failed === 0) {
      this.log('🎉 ALL TESTS PASSED! All tool pages are fully functional!', 'success');
    } else {
      this.log(`⚠️ ${this.testResults.failed} test(s) failed. See errors above.`, 'warning');
    }
    
    this.log('='.repeat(60));
  }

  async run() {
    try {
      this.log('Starting comprehensive tool pages integration test...');
      
      await this.fetchAllTools();
      await this.testDataIntegrity();
      await this.testApiEndpoints();
      await this.testPageAccessibility();
      await this.testPageContent();
      await this.testCategoryIntegration();
      
      this.printSummary();
      
      // Exit with appropriate code
      process.exit(this.testResults.failed === 0 ? 0 : 1);
      
    } catch (error) {
      this.log(`Test runner failed: ${error.message}`, 'error');
      process.exit(1);
    }
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  const tester = new ToolPagesTest();
  tester.run();
}

module.exports = ToolPagesTest;
