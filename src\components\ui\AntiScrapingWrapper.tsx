'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

// Extend Window interface for our custom properties
declare global {
  interface Window {
    __CLIENT_TOKEN__?: string
    __FETCH_PATCHED__?: boolean
  }
}

interface AntiScrapingWrapperProps {
  children: React.ReactNode
}

export default function AntiScrapingWrapper({ children }: AntiScrapingWrapperProps) {
  const [isValidated, setIsValidated] = useState(false)
  const [clientToken, setClientToken] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') return

    // Check if running in development mode
    const isDevelopment = typeof window !== 'undefined' && window.location.hostname === 'localhost';

    // Skip anti-scraping in development mode or if disabled
    const antiScrapingEnabled = process.env.NEXT_PUBLIC_ANTI_SCRAPING_ENABLED === 'true'

    // For now, disable anti-scraping to fix deployment issues
    if (isDevelopment || !antiScrapingEnabled || true) {
      setIsValidated(true)
      // Generate a dummy token for development
      const devToken = 'dev-token-' + Math.random().toString(36).substr(2, 9)
      setClientToken(devToken)
      window.__CLIENT_TOKEN__ = devToken
      localStorage.setItem('client-token', devToken)
      return
    }

    // Multiple layers of detection
    const runValidation = async () => {
      // Check 1: Ensure JavaScript is enabled and DOM is available
      if (!document || !window) {
        return false
      }

      // Check 2: Detect headless browsers
      if (detectHeadlessBrowser()) {
        console.log('Headless browser detected')
        return false
      }

      // Check 3: Detect automation tools
      if (detectAutomationTools()) {
        console.log('Automation tools detected')
        return false
      }

      // Check 4: Verify human-like behavior
      if (!await verifyHumanBehavior()) {
        console.log('Non-human behavior detected')
        return false
      }

      // Generate client token
      try {
        const response = await fetch('/api/auth/client-token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
          }
        })
        
        if (response.ok) {
          const data = await response.json()
          setClientToken(data.token)
          localStorage.setItem('client-token', data.token)
        }
      } catch (error) {
        console.log('Failed to get client token')
      }

      return true
    }

    const validateAndProceed = async () => {
      const isValid = await runValidation()
      if (isValid) {
        setIsValidated(true)
      } else {
        // For now, just proceed with validation anyway to avoid blocking legitimate users
        console.log('Validation failed, but proceeding anyway')
        setIsValidated(true)
        // Could implement fallback token here
        const fallbackToken = 'fallback-token-' + Math.random().toString(36).substr(2, 9)
        setClientToken(fallbackToken)
        window.__CLIENT_TOKEN__ = fallbackToken
        localStorage.setItem('client-token', fallbackToken)
      }
    }

    // Add a small delay to make it harder for bots to bypass
    const timer = setTimeout(validateAndProceed, 500)
    
    return () => clearTimeout(timer)
  }, [router])

  // Detect headless browsers
  const detectHeadlessBrowser = (): boolean => {
    // Check for common headless browser indicators
    const checks = [
      () => window.navigator.webdriver,
      () => window.navigator.plugins.length === 0,
      () => window.navigator.languages.length === 0,
      () => /HeadlessChrome/.test(window.navigator.userAgent),
      () => window.outerHeight === 0,
      () => window.outerWidth === 0,
      () => !(window as any).chrome?.runtime,
    ]

    return checks.some(check => {
      try {
        return check()
      } catch {
        return false
      }
    })
  }

  // Detect automation tools
  const detectAutomationTools = (): boolean => {
    // Check for Selenium, Puppeteer, Playwright, etc.
    const automationIndicators = [
      'webdriver',
      '_phantom',
      'phantom',
      'selenium',
      'puppeteer',
      'playwright',
      '__webdriver_script_fn',
      'domAutomation',
      'domAutomationController'
    ]

    return automationIndicators.some(indicator => {
      try {
        return window.hasOwnProperty(indicator) || 
               document.hasOwnProperty(indicator) ||
               navigator.hasOwnProperty(indicator)
      } catch {
        return false
      }
    })
  }

  // Verify human-like behavior
  const verifyHumanBehavior = (): Promise<boolean> => {
    return new Promise((resolve) => {
      let interactionScore = 0
      let timeScore = 0
      
      // Check for mouse movement
      const handleMouseMove = () => {
        interactionScore += 1
        if (interactionScore >= 3) {
          resolve(true)
        }
      }

      // Check for keyboard interaction
      const handleKeyPress = () => {
        interactionScore += 2
        if (interactionScore >= 3) {
          resolve(true)
        }
      }

      // Check for scroll behavior
      const handleScroll = () => {
        interactionScore += 1
        if (interactionScore >= 3) {
          resolve(true)
        }
      }

      // Time-based validation (humans take time to read)
      setTimeout(() => {
        timeScore += 1
        if (timeScore >= 1 && interactionScore >= 1) {
          resolve(true)
        } else if (timeScore >= 2) {
          // If user stays on page for 3+ seconds, likely human
          resolve(true)
        }
      }, 3000)

      // Add event listeners
      document.addEventListener('mousemove', handleMouseMove, { once: true })
      document.addEventListener('keypress', handleKeyPress, { once: true })
      document.addEventListener('scroll', handleScroll, { once: true })

      // Fallback - resolve after 5 seconds anyway
      setTimeout(() => resolve(true), 5000)
    })
  }

  // Set up API interceptor to include client token
  useEffect(() => {
    if (clientToken && typeof window !== 'undefined') {
      // Store token globally for use in API calls
      window.__CLIENT_TOKEN__ = clientToken
      
      // Monkey patch fetch to include the client token
      if (!window.__FETCH_PATCHED__) {
        const originalFetch = window.fetch
        window.fetch = function(...args: Parameters<typeof fetch>) {
          const [resource, init = {}] = args
          
          // Add client token to API requests
          if (typeof resource === 'string' && resource.startsWith('/api/')) {
            const token = window.__CLIENT_TOKEN__ || localStorage.getItem('client-token')
            if (token) {
              init.headers = {
                ...init.headers,
                'X-Client-Token': token,
                'X-Requested-With': 'XMLHttpRequest'
              }
            }
          }
          
          return originalFetch(resource, init)
        }
        window.__FETCH_PATCHED__ = true
      }
    }
  }, [clientToken])

  // Also trigger a custom event when token is ready
  useEffect(() => {
    if (clientToken && typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('clientTokenReady', { detail: { token: clientToken } }))
    }
  }, [clientToken])

  if (!isValidated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return <>{children}</>
}
