import Script from 'next/script'

export default function WebVitals() {
  return (
    <Script
      id="web-vitals"
      strategy="afterInteractive"
      dangerouslySetInnerHTML={{
        __html: `
          function sendToAnalytics(metric) {
            if (typeof gtag !== 'undefined') {
              gtag('event', metric.name, {
                custom_parameter_1: metric.value,
                custom_parameter_2: metric.id,
                custom_parameter_3: metric.name,
                custom_parameter_4: metric.navigationType,
              });
            }
          }

          // Core Web Vitals
          if ('web-vital' in window) {
            import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
              getCLS(sendToAnalytics);
              getFID(sendToAnalytics);
              getFCP(sendToAnalytics);
              getLCP(sendToAnalytics);
              getTTFB(sendToAnalytics);
            });
          }

          // Page performance tracking
          window.addEventListener('load', function() {
            const perfData = performance.getEntriesByType('navigation')[0];
            if (perfData && typeof gtag !== 'undefined') {
              gtag('event', 'page_timing', {
                custom_parameter_1: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                custom_parameter_2: perfData.loadEventEnd - perfData.loadEventStart,
                custom_parameter_3: perfData.domInteractive - perfData.domLoading,
              });
            }
          });
        `,
      }}
    />
  )
}
