import mongoose from 'mongoose';

// Define the schema for the Review model
const ReviewSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'User',
  },
  tool: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Tool',
  },
  rating: {
    type: Number,
    required: [true, 'Please provide a rating'],
    min: 1,
    max: 5,
  },
  title: {
    type: String,
    required: [true, 'Please provide a title for your review'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters'],
  },
  comment: {
    type: String,
    required: [true, 'Please provide a comment for your review'],
    trim: true,
    maxlength: [1000, 'Comment cannot be more than 1000 characters'],
  },
  helpful: {
    type: Number,
    default: 0,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Update the updatedAt field before saving
ReviewSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Create compound index to ensure a user can only review a tool once
ReviewSchema.index({ user: 1, tool: 1 }, { unique: true });

// Check if the model already exists to prevent overwriting
export default mongoose.models.Review || mongoose.model('Review', ReviewSchema); 