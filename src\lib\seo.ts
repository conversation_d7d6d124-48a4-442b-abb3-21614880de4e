import { Metadata } from 'next'
import { Tool } from '@/types/tool'

export interface SEOConfig {
  siteName: string
  siteUrl: string
  defaultTitle: string
  defaultDescription: string
  defaultImage: string
  twitterHandle: string
  facebookAppId?: string
  googleSiteVerification?: string
  bingSiteVerification?: string
}

export const seoConfig: SEOConfig = {
  siteName: 'AI Tools Directory',
  siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://aitools.directory',
  defaultTitle: 'AI Tools Directory - Discover the Best AI Tools for Your Needs',
  defaultDescription: 'Explore thousands of AI tools across various categories. Find the perfect AI solution for productivity, creativity, business, and more. Updated daily with the latest AI innovations.',
  defaultImage: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://aitools.directory'}/api/og`,
  twitterHandle: process.env.NEXT_PUBLIC_TWITTER_HANDLE || '@aitoolsdirectory',
  facebookAppId: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID,
  googleSiteVerification: process.env.GOOGLE_SITE_VERIFICATION,
  bingSiteVerification: process.env.BING_SITE_VERIFICATION,
}

export function generateHomePageMetadata(): Metadata {
  const { siteName, siteUrl, defaultTitle, defaultDescription, defaultImage, twitterHandle } = seoConfig

  return {
    metadataBase: new URL(siteUrl),
    title: {
      default: defaultTitle,
      template: `%s | ${siteName}`
    },
    description: defaultDescription,
    keywords: [
      'AI tools',
      'artificial intelligence',
      'AI directory',
      'machine learning tools',
      'AI software',
      'productivity tools',
      'AI chatbots',
      'image generation',
      'code assistants',
      'content creation',
      'AI automation',
      'business AI tools',
      'creative AI',
      'AI analysis',
      'text generation',
      'voice AI',
      'video AI',
      'AI research tools'
    ],
    authors: [{ name: siteName }],
    creator: siteName,
    publisher: siteName,
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      type: 'website',
      locale: 'en_US',
      url: siteUrl,
      title: defaultTitle,
      description: defaultDescription,
      siteName,
      images: [
        {
          url: defaultImage,
          width: 1200,
          height: 630,
          alt: `${siteName} - Discover the Best AI Tools`,
        }
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: defaultTitle,
      description: defaultDescription,
      site: twitterHandle,
      creator: twitterHandle,
      images: [defaultImage],
    },
    alternates: {
      canonical: siteUrl,
    },
    other: {
      ...(seoConfig.googleSiteVerification && { 'google-site-verification': seoConfig.googleSiteVerification }),
      ...(seoConfig.bingSiteVerification && { 'msvalidate.01': seoConfig.bingSiteVerification }),
    },
  }
}

export function generateToolPageMetadata(tool: Tool): Metadata {
  const { siteName, siteUrl, twitterHandle } = seoConfig
  
  const categoryName = typeof tool.category === 'string' ? tool.category : tool.category?.name || 'AI Tools'
  const toolUrl = `${siteUrl}/tools/${tool._id}`
  const toolImage = tool.imageUrl || `${siteUrl}/api/og?title=${encodeURIComponent(tool.name)}&subtitle=${encodeURIComponent(categoryName)}&category=${encodeURIComponent(categoryName)}`
  
  const title = `${tool.name} - ${categoryName} | ${siteName}`
  const description = tool.description || `Discover ${tool.name}, a powerful AI tool in the ${categoryName} category. ${tool.features?.slice(0, 3).join(', ') || 'Enhance your workflow with AI technology.'}`
  
  // Generate rich keywords based on tool data
  const keywords = [
    tool.name.toLowerCase(),
    categoryName.toLowerCase(),
    'AI tool',
    'artificial intelligence',
    ...(tool.tags || []),
    ...(tool.features?.slice(0, 5) || []),
    tool.pricing?.toLowerCase() || '',
    tool.company?.toLowerCase() || '',
  ].filter(Boolean)

  return {
    metadataBase: new URL(siteUrl),
    title,
    description,
    keywords,
    authors: [{ name: tool.company || siteName }],
    creator: siteName,
    publisher: siteName,
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      type: 'article',
      locale: 'en_US',
      url: toolUrl,
      title,
      description,
      siteName,
      images: [
        {
          url: toolImage,
          width: 1200,
          height: 630,
          alt: `${tool.name} - ${categoryName} AI Tool`,
        }
      ],
      publishedTime: tool.createdAt,
      modifiedTime: tool.updatedAt,
      section: categoryName,
      tags: tool.tags,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      site: twitterHandle,
      creator: twitterHandle,
      images: [toolImage],
    },
    alternates: {
      canonical: toolUrl,
    },
  }
}

export function generateToolJsonLd(tool: Tool) {
  const { siteUrl, siteName } = seoConfig
  const categoryName = typeof tool.category === 'string' ? tool.category : tool.category?.name || 'AI Tools'
  const toolUrl = `${siteUrl}/tools/${tool._id}`

  return {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: tool.name,
    description: tool.description,
    url: tool.url,
    applicationCategory: categoryName,
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: tool.pricing === 'Free' ? '0' : undefined,
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
    },
    aggregateRating: tool.rating && tool.ratingCount ? {
      '@type': 'AggregateRating',
      ratingValue: tool.rating,
      reviewCount: tool.ratingCount,
      bestRating: 5,
      worstRating: 1,
    } : undefined,
    image: tool.imageUrl,
    publisher: {
      '@type': 'Organization',
      name: tool.company || siteName,
    },
    dateCreated: tool.createdAt,
    dateModified: tool.updatedAt,
    mainEntityOfPage: toolUrl,
  }
}

export function generateWebsiteJsonLd() {
  const { siteUrl, siteName, defaultDescription } = seoConfig

  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: siteName,
    description: defaultDescription,
    url: siteUrl,
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${siteUrl}/tools?search={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
    publisher: {
      '@type': 'Organization',
      name: siteName,
      url: siteUrl,
    },
  }
}

export function generateOrganizationJsonLd() {
  const { siteUrl, siteName, defaultDescription } = seoConfig

  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: siteName,
    description: defaultDescription,
    url: siteUrl,
    logo: `${siteUrl}/images/logo.png`,
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'customer service',
      availableLanguage: 'English',
    },
    sameAs: [
      // Add your social media profiles here
      'https://twitter.com/aitoolsdirectory',
      'https://linkedin.com/company/aitoolsdirectory',
    ],
  }
}

export function generateBreadcrumbJsonLd(items: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  }
}

export function generateFAQJsonLd(faqs: Array<{ question: string; answer: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  }
}
