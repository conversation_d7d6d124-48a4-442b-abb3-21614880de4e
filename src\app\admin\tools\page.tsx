'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AdminLayout from '@/components/admin/AdminLayout';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';

interface Tool {
  _id: string;
  name: string;
  category: string;
  submittedBy: string;
  submittedDate: string;
  status: 'pending' | 'approved' | 'rejected';
  views: number;
  clicks: number;
  revenue: number;
  description: string;
  website: string;
  pricing: string;
  rejectionReason?: string;
}

const AdminToolsPage: React.FC = () => {
  const [tools, setTools] = useState<Tool[]>([]);
  const [filteredTools, setFilteredTools] = useState<Tool[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterCategory, setFilterCategory] = useState('all');

  useEffect(() => {
    // Load tools (in real app, fetch from API)
    const mockTools: Tool[] = [
      {
        _id: '1',
        name: 'AI Content Generator Pro',
        category: 'Content Creation',
        submittedBy: '<EMAIL>',
        submittedDate: '2024-01-18',
        status: 'pending',
        views: 0,
        clicks: 0,
        revenue: 0,
        description: 'Advanced AI content generation tool with GPT-4 integration',
        website: 'https://aicontentgen.com',
        pricing: 'freemium'
      },
      {
        _id: '2',
        name: 'Smart Image Enhancer',
        category: 'Image Processing',
        submittedBy: '<EMAIL>',
        submittedDate: '2024-01-15',
        status: 'approved',
        views: 2847,
        clicks: 234,
        revenue: 47,
        description: 'AI-powered image enhancement and upscaling tool',
        website: 'https://smartenhancer.ai',
        pricing: 'paid'
      },
      {
        _id: '3',
        name: 'Voice Clone Studio',
        category: 'Audio Processing',
        submittedBy: '<EMAIL>',
        submittedDate: '2024-01-12',
        status: 'rejected',
        views: 0,
        clicks: 0,
        revenue: 0,
        description: 'Create realistic voice clones with AI technology',
        website: 'https://voiceclone.studio',
        pricing: 'subscription',
        rejectionReason: 'Ethical concerns about voice cloning technology'
      },
      {
        _id: '4',
        name: 'Data Analysis Bot',
        category: 'Data Analysis',
        submittedBy: '<EMAIL>',
        submittedDate: '2024-01-10',
        status: 'approved',
        views: 1923,
        clicks: 156,
        revenue: 31,
        description: 'Automated data analysis and visualization tool',
        website: 'https://dataanalysisbot.com',
        pricing: 'freemium'
      },
      {
        _id: '5',
        name: 'Code Review AI',
        category: 'Development Tools',
        submittedBy: '<EMAIL>',
        submittedDate: '2024-01-20',
        status: 'pending',
        views: 0,
        clicks: 0,
        revenue: 0,
        description: 'AI-powered code review and optimization suggestions',
        website: 'https://codereviewai.dev',
        pricing: 'free'
      }
    ];

    setTools(mockTools);
    setFilteredTools(mockTools);
  }, []);

  useEffect(() => {
    let filtered = tools;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(tool => 
        tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tool.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tool.submittedBy.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (filterStatus !== 'all') {
      filtered = filtered.filter(tool => tool.status === filterStatus);
    }

    // Filter by category
    if (filterCategory !== 'all') {
      filtered = filtered.filter(tool => tool.category === filterCategory);
    }

    setFilteredTools(filtered);
  }, [searchTerm, filterStatus, filterCategory, tools]);

  const handleToolAction = (toolId: string, action: string, reason?: string) => {
    switch (action) {
      case 'approve':
        if (confirm('Approve this tool for listing?')) {
          setTools(tools.map(tool => 
            tool._id === toolId 
              ? { ...tool, status: 'approved' as const }
              : tool
          ));
          alert(`Tool ${toolId} approved successfully!`);
        }
        break;
      case 'reject':
        const rejectionReason = prompt('Please provide a reason for rejection:');
        if (rejectionReason) {
          setTools(tools.map(tool => 
            tool._id === toolId 
              ? { ...tool, status: 'rejected' as const, rejectionReason }
              : tool
          ));
          alert(`Tool ${toolId} rejected.`);
        }
        break;
      case 'delete':
        if (confirm('Are you sure you want to delete this tool? This action cannot be undone.')) {
          setTools(tools.filter(tool => tool._id !== toolId));
          alert(`Tool ${toolId} deleted successfully!`);
        }
        break;
      case 'feature':
        if (confirm('Feature this tool on the homepage?')) {
          alert(`Tool ${toolId} added to featured tools!`);
        }
        break;
      default:
        break;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Approved</span>;
      case 'pending':
        return <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Pending</span>;
      case 'rejected':
        return <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">Rejected</span>;
      default:
        return <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">Unknown</span>;
    }
  };

  const categories = ['Content Creation', 'Image Processing', 'Audio Processing', 'Data Analysis', 'Development Tools'];
  const pendingCount = tools.filter(tool => tool.status === 'pending').length;
  const approvedCount = tools.filter(tool => tool.status === 'approved').length;
  const rejectedCount = tools.filter(tool => tool.status === 'rejected').length;

  return (
    <AdminLayout>
      <div className="container mx-auto px-4 py-8">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-2">
            Tool Management
          </h1>
          <p className="text-lg text-muted-foreground">
            Review, approve, and manage AI tool submissions
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="p-6 text-center">
            <div className="text-2xl font-bold text-blue-600 mb-1">{tools.length}</div>
            <div className="text-sm text-muted-foreground">Total Tools</div>
          </Card>
          <Card className="p-6 text-center bg-yellow-50 border-yellow-200">
            <div className="text-2xl font-bold text-yellow-600 mb-1">{pendingCount}</div>
            <div className="text-sm text-muted-foreground">Pending Review</div>
          </Card>
          <Card className="p-6 text-center bg-green-50 border-green-200">
            <div className="text-2xl font-bold text-green-600 mb-1">{approvedCount}</div>
            <div className="text-sm text-muted-foreground">Approved</div>
          </Card>
          <Card className="p-6 text-center bg-red-50 border-red-200">
            <div className="text-2xl font-bold text-red-600 mb-1">{rejectedCount}</div>
            <div className="text-sm text-muted-foreground">Rejected</div>
          </Card>
        </div>

        {/* Filters */}
        <Card className="p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search tools by name, category, or submitter..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <select
                className="px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
              
              <select
                className="px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
          </div>
        </Card>

        {/* Tools List */}
        <div className="space-y-6">
          {filteredTools.length === 0 ? (
            <Card className="p-8 text-center">
              <p className="text-muted-foreground">No tools found matching your filters.</p>
            </Card>
          ) : (
            filteredTools.map((tool) => (
              <Card key={tool._id} className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="text-xl font-semibold text-foreground">{tool.name}</h3>
                      {getStatusBadge(tool.status)}
                    </div>
                    
                    <p className="text-muted-foreground mb-3">{tool.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <strong>Category:</strong> {tool.category}<br/>
                        <strong>Submitted by:</strong> {tool.submittedBy}<br/>
                        <strong>Date:</strong> {new Date(tool.submittedDate).toLocaleDateString()}
                      </div>
                      <div>
                        <strong>Website:</strong> <a href={tool.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">{tool.website}</a><br/>
                        <strong>Pricing:</strong> {tool.pricing}<br/>
                        {tool.status === 'approved' && (
                          <>
                            <strong>Performance:</strong> {tool.views} views, {tool.clicks} clicks
                          </>
                        )}
                      </div>
                    </div>
                    
                    {tool.status === 'rejected' && tool.rejectionReason && (
                      <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                        <p className="text-red-600 text-sm">
                          <strong>Rejection reason:</strong> {tool.rejectionReason}
                        </p>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex flex-wrap gap-2">
                    {tool.status === 'pending' && (
                      <>
                        <Button 
                          size="sm" 
                          onClick={() => handleToolAction(tool._id, 'approve')}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          Approve
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleToolAction(tool._id, 'reject')}
                          className="text-red-600 border-red-600 hover:bg-red-50"
                        >
                          Reject
                        </Button>
                      </>
                    )}
                    
                    {tool.status === 'approved' && (
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => handleToolAction(tool._id, 'feature')}
                        className="text-blue-600"
                      >
                        Feature
                      </Button>
                    )}
                    
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => window.open(tool.website, '_blank')}
                    >
                      Visit Site
                    </Button>
                    
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleToolAction(tool._id, 'delete')}
                      className="text-red-600"
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>

        {/* Bulk Actions */}
        {pendingCount > 0 && (
          <Card className="p-6 mt-8">
            <h3 className="text-lg font-semibold mb-4">Bulk Actions</h3>
            <div className="flex gap-2">
              <Button 
                onClick={() => alert('Bulk approval would approve all pending tools')}
                className="bg-green-600 hover:bg-green-700"
              >
                Approve All Pending ({pendingCount})
              </Button>
              <Button 
                variant="outline"
                onClick={() => alert('Export pending tools list would generate a CSV')}
              >
                Export Pending List
              </Button>
            </div>
          </Card>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminToolsPage;
