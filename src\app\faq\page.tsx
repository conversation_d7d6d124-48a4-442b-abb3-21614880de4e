import React from 'react';
import { Card } from '@/components/ui/Card';
import Link from 'next/link';

export default function FAQPage() {
  const faqs = [
    {
      category: "Getting Started",
      questions: [
        {
          question: "What is AI Tools Directory?",
          answer: "AI Tools Directory is a comprehensive platform that curates and organizes the best AI tools available. We help users discover, compare, and access AI solutions for various needs including productivity, creativity, development, and business applications."
        },
        {
          question: "How do I access the tools?",
          answer: "You can preview featured tools on our homepage for free. To access our complete directory of 1000+ AI tools, browse by categories, and submit your own tools, you need an All Access subscription for £5/month."
        },
        {
          question: "Is there a free trial?",
          answer: "Yes! We offer a 7-day free trial for new subscribers. You can cancel anytime during the trial period without being charged."
        }
      ]
    },
    {
      category: "Subscription & Billing",
      questions: [
        {
          question: "How much does the subscription cost?",
          answer: "Our All Access subscription costs £5 per month, billed monthly. This gives you unlimited access to all AI tools, categories, and the ability to submit your own tools."
        },
        {
          question: "Can I cancel my subscription?",
          answer: "Yes, you can cancel your subscription at any time through your account settings. You'll retain access to all features until the end of your current billing period."
        },
        {
          question: "Do you offer refunds?",
          answer: "Subscription fees are generally non-refundable, except as required by law. However, you can cancel at any time to avoid future charges."
        },
        {
          question: "What payment methods do you accept?",
          answer: "We accept all major credit cards including Visa, Mastercard, and American Express. Payments are processed securely through our payment partners."
        }
      ]
    },
    {
      category: "Tool Submission",
      questions: [
        {
          question: "How do I submit my AI tool?",
          answer: "To submit your AI tool, you need an All Access subscription. Once subscribed, go to the 'Submit Tool' page in your dashboard and fill out the comprehensive form with your tool's details."
        },
        {
          question: "What information do I need to provide?",
          answer: "You'll need to provide your tool's name, description, website URL, category, pricing model, key features, use cases, logo, screenshots, and your contact information."
        },
        {
          question: "How long does the review process take?",
          answer: "We typically review submitted tools within 3-5 business days. You'll receive an email notification once your tool is approved and live in our directory."
        },
        {
          question: "Can I edit my tool listing after submission?",
          answer: "Yes, you can request updates to your tool listing by contacting our support team. We'll help you make the necessary changes to keep your listing current."
        }
      ]
    },
    {
      category: "Technical Support",
      questions: [
        {
          question: "I'm having trouble logging in. What should I do?",
          answer: "First, make sure you're using the correct email and password. If you've forgotten your password, use the 'Forgot Password' link on the login page. If you continue having issues, contact our support team."
        },
        {
          question: "The website isn't loading properly. How can I fix this?",
          answer: "Try refreshing the page, clearing your browser cache, or using a different browser. If the problem persists, it might be a temporary server issue. Contact us if the problem continues."
        },
        {
          question: "How do I update my account information?",
          answer: "You can update your account information through your dashboard. Go to Account Settings to change your name, email, or other profile details."
        },
        {
          question: "Can I use the platform on mobile devices?",
          answer: "Yes! Our platform is fully responsive and works great on mobile devices, tablets, and desktop computers."
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-background py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-foreground mb-4">
              Frequently Asked Questions
            </h1>
            <p className="text-xl text-muted-foreground">
              Find answers to common questions about AI Tools Directory
            </p>
          </div>

          <div className="space-y-8">
            {faqs.map((category, categoryIndex) => (
              <Card key={categoryIndex} className="p-8">
                <h2 className="text-2xl font-semibold text-foreground mb-6 border-b border-border pb-2">
                  {category.category}
                </h2>
                
                <div className="space-y-6">
                  {category.questions.map((faq, faqIndex) => (
                    <div key={faqIndex}>
                      <h3 className="text-lg font-medium text-foreground mb-2">
                        {faq.question}
                      </h3>
                      <p className="text-muted-foreground leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  ))}
                </div>
              </Card>
            ))}
          </div>

          {/* Contact Section */}
          <div className="mt-12 text-center">
            <Card className="p-8">
              <h2 className="text-2xl font-semibold text-foreground mb-4">
                Still have questions?
              </h2>
              <p className="text-muted-foreground mb-6">
                Can't find the answer you're looking for? Our support team is here to help.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact">
                  <button className="px-6 py-3 bg-primary text-primary-foreground font-medium rounded-md hover:bg-primary/90 transition-colors">
                    Contact Support
                  </button>
                </Link>
                <a href="mailto:<EMAIL>">
                  <button className="px-6 py-3 border border-primary text-primary font-medium rounded-md hover:bg-primary/5 transition-colors">
                    Email Us
                  </button>
                </a>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
