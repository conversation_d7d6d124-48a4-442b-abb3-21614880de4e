'use client'

import React, { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { FaStar, FaRegStar } from 'react-icons/fa'
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { generatePlaceholderImage, generateFallbackPlaceholder } from '@/lib/generatePlaceholderImage'

export interface ToolCardProps {
  id: string
  name: string
  description: string
  imageUrl: string
  rating: number
  category: string | { _id: string; name: string; slug?: string }
  pricing: 'Free' | 'Freemium' | 'Paid'
  url: string
}

const ToolCard: React.FC<ToolCardProps> = ({
  id,
  name,
  description,
  imageUrl,
  rating,
  category,
  pricing,
  url,
}) => {
  const [imageSrc, setImageSrc] = useState(imageUrl)
  const [isImageError, setIsImageError] = useState(false)

  // Generate stars based on rating
  const renderStars = () => {
    const stars = []
    const ratingValue = rating || 0
    const fullStars = Math.floor(ratingValue)
    const hasHalfStar = ratingValue % 1 >= 0.5

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <FaStar 
            key={i} 
            className="text-yellow-400" 
            data-testid={`full-star-${i}`}
          />
        )
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <FaStar 
            key={i} 
            className="text-yellow-400" 
            style={{ clipPath: 'inset(0 50% 0 0)' }} 
            data-testid={`half-star-${i}`}
          />
        )
      } else {
        stars.push(
          <FaRegStar 
            key={i} 
            className="text-gray-300" 
            data-testid={`empty-star-${i}`}
          />
        )
      }
    }
    return stars
  }

  // Get pricing badge color
  const getPricingColor = () => {
    switch (pricing) {
      case 'Free':
        return 'bg-green-100 text-green-800'
      case 'Freemium':
        return 'bg-blue-100 text-blue-800'
      case 'Paid':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleImageError = () => {
    if (!isImageError) {
      setIsImageError(true)
      // Use static local placeholder as the most reliable fallback
      setImageSrc('/images/tool-placeholder.svg')
    }
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <span className={`text-xs font-medium px-2 py-1 rounded-full ${getPricingColor()}`}>
            {pricing}
          </span>
          <div className="flex items-center space-x-1">
            {renderStars()}
            <span className="text-xs text-gray-500 ml-1">{(rating || 0).toFixed(1)}</span>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="relative h-40 w-full mb-4 rounded-md overflow-hidden">
          <Image
            src={imageSrc}
            alt={name}
            fill
            className="object-cover"
            onError={handleImageError}
            priority={false}
          />
        </div>
        <h3 className="text-lg font-semibold mb-1">{name}</h3>
        <p className="text-sm text-gray-600 mb-2">{description}</p>
        {typeof category === 'string' ? (
          <span className="text-xs text-gray-500">{category}</span>
        ) : category && category.name ? (
          category.slug ? (
            <Link 
              href={`/categories/${category.slug}`}
              className="text-xs text-blue-600 hover:text-blue-800 hover:underline"
              onClick={(e) => e.stopPropagation()}
            >
              {category.name}
            </Link>
          ) : (
            <span className="text-xs text-gray-500">{category.name}</span>
          )
        ) : (
          <span className="text-xs text-gray-500">Uncategorized</span>
        )}
      </CardContent>
      <CardFooter className="pt-2">
        <Button asChild className="w-full">
          <Link href={`/tools/${id}`}>View Details</Link>
        </Button>
      </CardFooter>
    </Card>
  )
}

export default ToolCard 