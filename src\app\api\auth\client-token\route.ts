import { NextRequest, NextResponse } from 'next/server'
import AntiScrapingService from '@/lib/antiScraping'

export async function POST(request: NextRequest) {
  try {
    const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
    const userAgent = request.headers.get('user-agent') || ''
    const referer = request.headers.get('referer') || ''
    
    // Basic validation
    if (!referer || !referer.includes(process.env.NEXT_PUBLIC_APP_URL || 'localhost')) {
      return NextResponse.json({ error: 'Invalid request' }, { status: 403 })
    }
    
    // Check if request comes from our domain
    const requestedWith = request.headers.get('x-requested-with')
    if (requestedWith !== 'XMLHttpRequest') {
      return NextResponse.json({ error: 'Invalid request' }, { status: 403 })
    }
    
    const antiScraping = AntiScrapingService.getInstance()
    
    // Generate client token
    const token = antiScraping.generateClientToken()
    
    console.log(`Generated client token for ${ip}`)
    
    return NextResponse.json({ 
      token,
      expiresIn: 3600 // 1 hour
    })
    
  } catch (error) {
    console.error('Error generating client token:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
