import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'

export async function authMiddleware(request: NextRequest) {
  const token = await getToken({ req: request })
  
  // Check if user is authenticated
  if (!token) {
    const url = new URL('/login', request.url)
    url.searchParams.set('callbackUrl', request.nextUrl.pathname)
    return NextResponse.redirect(url)
  }
  
  // Check if user is paid for protected routes
  const isPaid = token.isPaid === true && token.subscriptionStatus === 'active'
  const isAdmin = token.role === 'admin'
  
  // List of routes that require paid access
  const paidRoutes = ['/tools', '/categories']
  
  // Check if current path requires paid access
  const requiresPaidAccess = paidRoutes.some(route => 
    request.nextUrl.pathname === route || 
    request.nextUrl.pathname.startsWith(`${route}/`)
  )
  
  if (requiresPaidAccess && !isPaid && !isAdmin) {
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }
  
  return NextResponse.next()
}
