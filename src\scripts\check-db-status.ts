import dbConnect from '../lib/db';
import Tool from '../models/Tool';
import Category from '../models/Category';
import mongoose from 'mongoose';

async function checkDatabaseStatus() {
  try {
    await dbConnect();
    
    // Get total tools count
    const totalTools = await Tool.countDocuments();
    console.log(`Total tools in database: ${totalTools}`);
    
    // Get total categories count
    const totalCategories = await Category.countDocuments();
    console.log(`Total categories in database: ${totalCategories}`);
    
    // Get categories with tool counts
    const categories = await Category.find({}).sort({ name: 1 });
    console.log('\nCategories with tool counts:');
    categories.forEach((cat: any) => {
      console.log(`  ${cat.name}: ${cat.toolCount} tools`);
    });
    
    // Calculate pagination info (12 tools per page)
    const toolsPerPage = 12;
    const totalPages = Math.ceil(totalTools / toolsPerPage);
    console.log(`\nPagination info:`);
    console.log(`  Tools per page: ${toolsPerPage}`);
    console.log(`  Total pages: ${totalPages}`);
    
    console.log('\n✅ Database status check complete');
  } catch (error) {
    console.error('❌ Error checking database status:', error);
  } finally {
    await mongoose.disconnect();
  }
}

checkDatabaseStatus();
