'use client'

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useSession } from 'next-auth/react';
import { canAccessPaidFeatures } from '@/lib/auth';
import UpgradePrompt from '@/components/ui/UpgradePrompt';
import Link from 'next/link';

const DashboardPage: React.FC = () => {
  const searchParams = useSearchParams();
  const { data: session, status } = useSession();
  const subscribed = searchParams.get('subscribed');
  const [showWelcome, setShowWelcome] = useState(false);

  const isPaidUser = canAccessPaidFeatures(session);

  useEffect(() => {
    if (subscribed === 'true') {
      setShowWelcome(true);
      // Remove the parameter from URL after showing welcome
      const timeout = setTimeout(() => {
        setShowWelcome(false);
      }, 5000);
      return () => clearTimeout(timeout);
    }
  }, [subscribed]);

  const stats = {
    toolsViewed: 47,
    toolsSubmitted: 3,
    toolsApproved: 2,
    toolsPending: 1
  };

  // Show loading state while checking session
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Show upgrade prompt for non-paid users
  if (!isPaidUser) {
    return <UpgradePrompt feature="the dashboard and all AI tools" description="Upgrade to access the full dashboard, browse all tools, and submit your own AI tools." />;
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-12">

        {/* Welcome Message */}
        {showWelcome && (
          <div className="mb-8">
            <Card className="p-6 bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-green-500/10 rounded-full flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-foreground">Welcome to AI Tools Directory!</h2>
                  <p className="text-muted-foreground">Your subscription is now active. Start exploring and submitting AI tools.</p>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-2">
            Dashboard
          </h1>
          <p className="text-lg text-muted-foreground">
            Manage your AI tools and explore new ones
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="p-6 text-center hover:shadow-lg transition-shadow">
            <div className="w-12 h-12 bg-blue-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">Explore Tools</h3>
            <p className="text-sm text-muted-foreground mb-4">Browse 3000+ AI tools</p>
            <Link href="/tools">
              <Button className="w-full">Browse Tools</Button>
            </Link>
          </Card>

          <Card className="p-6 text-center hover:shadow-lg transition-shadow">
            <div className="w-12 h-12 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">Submit Tool</h3>
            <p className="text-sm text-muted-foreground mb-4">Add your AI tool to our directory</p>
            <Link href="/submit-tool">
              <Button className="w-full">Submit Tool</Button>
            </Link>
          </Card>

          <Card className="p-6 text-center hover:shadow-lg transition-shadow">
            <div className="w-12 h-12 bg-purple-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">Analytics</h3>
            <p className="text-sm text-muted-foreground mb-4">View your tool performance</p>
            <Link href="/analytics">
              <Button variant="outline" className="w-full">View Analytics</Button>
            </Link>
          </Card>

          <Card className="p-6 text-center hover:shadow-lg transition-shadow">
            <div className="w-12 h-12 bg-orange-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">Settings</h3>
            <p className="text-sm text-muted-foreground mb-4">Manage your account</p>
            <Link href="/settings">
              <Button variant="outline" className="w-full">Settings</Button>
            </Link>
          </Card>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-foreground mb-6">Your Activity</h2>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-500">{stats.toolsViewed}</div>
                <div className="text-sm text-muted-foreground">Tools Viewed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-500">{stats.toolsSubmitted}</div>
                <div className="text-sm text-muted-foreground">Tools Submitted</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-500">{stats.toolsApproved}</div>
                <div className="text-sm text-muted-foreground">Tools Approved</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-500">{stats.toolsPending}</div>
                <div className="text-sm text-muted-foreground">Tools Pending</div>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h2 className="text-xl font-semibold text-foreground mb-6">Subscription Status</h2>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Plan:</span>
                <span className="font-medium text-foreground">All Access Monthly</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Status:</span>
                <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">Active</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Next billing:</span>
                <span className="font-medium text-foreground">
                  {new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Amount:</span>
                <span className="font-medium text-foreground">£5.00/month</span>
              </div>
              <Button variant="outline" className="w-full" onClick={() => {
                // In a real app, this would open subscription management
                alert('Subscription management coming soon! You can cancel by contacting <NAME_EMAIL>');
              }}>
                Manage Subscription
              </Button>
            </div>
          </Card>
        </div>

        {/* Recent Tools */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-foreground mb-6">Your Recent Tool Submissions</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border border-border rounded-lg">
              <div>
                <h3 className="font-medium text-foreground">AI Content Generator Pro</h3>
                <p className="text-sm text-muted-foreground">Content Creation • Submitted 2 days ago</p>
              </div>
              <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">Approved</span>
            </div>
            
            <div className="flex items-center justify-between p-4 border border-border rounded-lg">
              <div>
                <h3 className="font-medium text-foreground">Smart Image Enhancer</h3>
                <p className="text-sm text-muted-foreground">Image Processing • Submitted 5 days ago</p>
              </div>
              <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">Approved</span>
            </div>
            
            <div className="flex items-center justify-between p-4 border border-border rounded-lg">
              <div>
                <h3 className="font-medium text-foreground">Voice Recognition API</h3>
                <p className="text-sm text-muted-foreground">Audio Processing • Submitted 1 week ago</p>
              </div>
              <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">Under Review</span>
            </div>
          </div>
          
          <div className="mt-6 text-center">
            <Link href="/my-tools">
              <Button variant="outline">View All Submissions</Button>
            </Link>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default DashboardPage;
