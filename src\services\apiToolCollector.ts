import dbConnect from '../lib/db';
import Tool from '../models/Tool';
import Category from '../models/Category';

interface GitHubRepo {
  name: string;
  description: string;
  html_url: string;
  stargazers_count: number;
  topics: string[];
  language: string;
  updated_at: string;
}

interface ProductHuntPost {
  name: string;
  tagline: string;
  description: string;
  redirect_url: string;
  screenshot_url: {
    url: string;
  };
  topics: {
    nodes: Array<{ name: string }>;
  };
}

class APIToolCollector {
  private categoryMap: Map<string, string> = new Map();
  private githubToken: string | null = null;
  private productHuntToken: string | null = null;

  constructor() {
    this.githubToken = process.env.GITHUB_TOKEN || null;
    this.productHuntToken = process.env.PRODUCT_HUNT_TOKEN || null;
  }

  async initialize() {
    await dbConnect();
    await this.loadCategoryMap();
  }

  private async loadCategoryMap() {
    const categories = await Category.find({});
    categories.forEach(category => {
      this.categoryMap.set(category.name.toLowerCase(), category._id.toString());
      this.categoryMap.set(category.slug, category._id.toString());
    });
  }

  private determineCategory(topics: string[], language?: string, description?: string): string {
    const allText = [...topics, language || '', description || ''].join(' ').toLowerCase();
    
    const categoryKeywords: { [key: string]: string[] } = {
      'language-models': ['nlp', 'natural-language', 'gpt', 'language-model', 'chatbot', 'conversation', 'text-generation'],
      'image-generation': ['image', 'computer-vision', 'gan', 'diffusion', 'art', 'photo', 'visual'],
      'code-generation': ['code', 'programming', 'developer', 'coding', 'automation', 'copilot'],
      'audio-ai': ['audio', 'speech', 'voice', 'music', 'sound'],
      'video-generation': ['video', 'multimedia', 'animation'],
      'data-analysis': ['data', 'analytics', 'visualization', 'statistics', 'ml', 'machine-learning'],
      'content-creation': ['content', 'writing', 'marketing', 'blog'],
      'education': ['education', 'learning', 'tutorial', 'course'],
      'productivity': ['productivity', 'automation', 'workflow', 'business'],
      'transcription': ['transcription', 'speech-to-text', 'subtitle']
    };

    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      if (keywords.some(keyword => allText.includes(keyword))) {
        return this.categoryMap.get(category) || this.categoryMap.get('other')!;
      }
    }

    return this.categoryMap.get('other')!;
  }

  async collectFromGitHub(): Promise<{ success: number; skipped: number; errors: number }> {
    const stats = { success: 0, skipped: 0, errors: 0 };
    
    if (!this.githubToken) {
      console.log('⚠️ No GitHub token provided, skipping GitHub collection');
      return stats;
    }

    console.log('🐙 Collecting AI tools from GitHub...');

    try {
      // Search for AI-related repositories
      const queries = [
        'artificial-intelligence language:python stars:>100',
        'machine-learning language:python stars:>100',
        'ai-tool stars:>50',
        'chatbot language:python stars:>50',
        'image-generation stars:>50',
        'text-generation stars:>50'
      ];

      for (const query of queries) {
        try {
          const response = await fetch(
            `https://api.github.com/search/repositories?q=${encodeURIComponent(query)}&sort=stars&order=desc&per_page=20`,
            {
              headers: {
                'Authorization': `token ${this.githubToken}`,
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'AITools-Collector/1.0'
              }
            }
          );

          if (!response.ok) {
            console.log(`⚠️ GitHub API error for query "${query}": ${response.status}`);
            continue;
          }

          const data = await response.json();
          const repos: GitHubRepo[] = data.items || [];

          for (const repo of repos) {
            try {
              // Check if tool already exists
              const existingTool = await Tool.findOne({
                $or: [
                  { name: { $regex: new RegExp(`^${repo.name}$`, 'i') } },
                  { url: repo.html_url }
                ]
              });

              if (existingTool) {
                stats.skipped++;
                continue;
              }

              // Create tool from GitHub repo
              const slug = repo.name.toLowerCase().replace(/[^a-z0-9-]/g, '-');
              const categoryId = this.determineCategory(repo.topics, repo.language, repo.description);

              const toolData = {
                name: repo.name,
                slug,
                description: repo.description || 'Open-source AI tool from GitHub',
                category: categoryId,
                url: repo.html_url,
                pricing: 'Free',
                imageUrl: `https://api.dicebear.com/7.x/shapes/svg?seed=${encodeURIComponent(repo.name)}&backgroundColor=24292e,0366d6,28a745`,
                features: [
                  'Open-source',
                  'GitHub repository',
                  `${repo.stargazers_count} stars`,
                  `Language: ${repo.language || 'Mixed'}`
                ],
                rating: Math.min(5, Math.floor(repo.stargazers_count / 100) + 3),
                ratingCount: Math.floor(repo.stargazers_count / 10),
                status: 'active',
                apiAvailable: true,
                tags: repo.topics
              };

              await Tool.create(toolData);
              console.log(`✅ Added GitHub tool: ${repo.name} (${repo.stargazers_count} stars)`);
              stats.success++;

              // Rate limiting for GitHub API
              await new Promise(resolve => setTimeout(resolve, 1000));

            } catch (error) {
              console.error(`❌ Error processing repo ${repo.name}:`, error);
              stats.errors++;
            }
          }

          // Rate limiting between queries
          await new Promise(resolve => setTimeout(resolve, 2000));

        } catch (error) {
          console.error(`❌ Error with GitHub query "${query}":`, error);
          stats.errors++;
        }
      }

    } catch (error) {
      console.error('❌ GitHub collection failed:', error);
      stats.errors++;
    }

    return stats;
  }

  async collectFromProductHunt(): Promise<{ success: number; skipped: number; errors: number }> {
    const stats = { success: 0, skipped: 0, errors: 0 };
    
    if (!this.productHuntToken) {
      console.log('⚠️ No Product Hunt token provided, skipping Product Hunt collection');
      return stats;
    }

    console.log('🚀 Collecting AI tools from Product Hunt...');

    try {
      // GraphQL query for AI tools
      const query = `
        query {
          posts(first: 50, topic: "artificial-intelligence", order: VOTES) {
            nodes {
              name
              tagline
              description
              url
              featuredAt
              votesCount
              commentsCount
              thumbnail {
                url
              }
              topics {
                nodes {
                  name
                }
              }
            }
          }
        }
      `;

      const response = await fetch('https://api.producthunt.com/v2/api/graphql', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.productHuntToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ query })
      });

      if (!response.ok) {
        console.log(`⚠️ Product Hunt API error: ${response.status}`);
        return stats;
      }

      const data = await response.json();
      const posts = data.data?.posts?.nodes || [];

      for (const post of posts) {
        try {
          // Check if tool already exists
          const existingTool = await Tool.findOne({
            $or: [
              { name: { $regex: new RegExp(`^${post.name}$`, 'i') } },
              { url: post.url }
            ]
          });

          if (existingTool) {
            stats.skipped++;
            continue;
          }

          // Create tool from Product Hunt post
          const slug = post.name.toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-');

          const topics = post.topics?.nodes?.map((t: any) => t.name) || [];
          const categoryId = this.determineCategory(topics, undefined, post.description);

          const toolData = {
            name: post.name,
            slug,
            description: post.description || post.tagline || 'AI tool featured on Product Hunt',
            category: categoryId,
            url: post.url,
            pricing: 'Contact for pricing',
            imageUrl: post.thumbnail?.url || `https://api.dicebear.com/7.x/shapes/svg?seed=${encodeURIComponent(post.name)}&backgroundColor=da552f,ff6154,ff9500`,
            features: [
              'Featured on Product Hunt',
              `${post.votesCount || 0} votes`,
              `${post.commentsCount || 0} comments`,
              'Recently launched'
            ],
            rating: Math.min(5, Math.floor((post.votesCount || 0) / 50) + 3),
            ratingCount: post.votesCount || 0,
            status: 'active',
            apiAvailable: false,
            tags: topics
          };

          await Tool.create(toolData);
          console.log(`✅ Added Product Hunt tool: ${post.name} (${post.votesCount} votes)`);
          stats.success++;

          // Rate limiting
          await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (error) {
          console.error(`❌ Error processing Product Hunt post ${post.name}:`, error);
          stats.errors++;
        }
      }

    } catch (error) {
      console.error('❌ Product Hunt collection failed:', error);
      stats.errors++;
    }

    return stats;
  }

  async collectFromHuggingFace(): Promise<{ success: number; skipped: number; errors: number }> {
    const stats = { success: 0, skipped: 0, errors: 0 };

    console.log('🤗 Collecting AI models from Hugging Face...');

    try {
      // Get popular models from different categories
      const categories = ['text-generation', 'text-to-image', 'image-to-text', 'speech-recognition', 'translation'];

      for (const category of categories) {
        try {
          const response = await fetch(
            `https://huggingface.co/api/models?pipeline_tag=${category}&sort=downloads&direction=-1&limit=20`,
            {
              headers: {
                'User-Agent': 'AITools-Collector/1.0'
              }
            }
          );

          if (!response.ok) {
            console.log(`⚠️ Hugging Face API error for ${category}: ${response.status}`);
            continue;
          }

          const models = await response.json();

          for (const model of models) {
            try {
              // Check if tool already exists
              const existingTool = await Tool.findOne({
                name: { $regex: new RegExp(`^${model.modelId}$`, 'i') }
              });

              if (existingTool) {
                stats.skipped++;
                continue;
              }

              // Map Hugging Face categories to our categories
              const categoryMapping: { [key: string]: string } = {
                'text-generation': 'language-models',
                'text-to-image': 'image-generation',
                'image-to-text': 'image-generation',
                'speech-recognition': 'transcription',
                'translation': 'language-models'
              };

              const ourCategory = categoryMapping[category] || 'other';
              const categoryId = this.categoryMap.get(ourCategory)!;

              const slug = model.modelId.toLowerCase().replace(/[^a-z0-9-]/g, '-');

              const toolData = {
                name: model.modelId,
                slug,
                description: `AI model for ${category.replace('-', ' ')} tasks from Hugging Face`,
                category: categoryId,
                url: `https://huggingface.co/${model.modelId}`,
                pricing: 'Free',
                imageUrl: `https://api.dicebear.com/7.x/shapes/svg?seed=${encodeURIComponent(model.modelId)}&backgroundColor=ff9d00,ffcc4d,ffeb3b`,
                features: [
                  'Hugging Face model',
                  `Pipeline: ${category}`,
                  `Downloads: ${model.downloads || 0}`,
                  'Open-source'
                ],
                rating: Math.min(5, Math.floor((model.downloads || 0) / 1000) + 3),
                ratingCount: Math.floor((model.downloads || 0) / 100),
                status: 'active',
                apiAvailable: true,
                tags: model.tags || []
              };

              await Tool.create(toolData);
              console.log(`✅ Added Hugging Face model: ${model.modelId}`);
              stats.success++;

              // Rate limiting
              await new Promise(resolve => setTimeout(resolve, 500));

            } catch (error) {
              console.error(`❌ Error processing model ${model.modelId}:`, error);
              stats.errors++;
            }
          }

          // Rate limiting between categories
          await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (error) {
          console.error(`❌ Error with Hugging Face category "${category}":`, error);
          stats.errors++;
        }
      }

    } catch (error) {
      console.error('❌ Hugging Face collection failed:', error);
      stats.errors++;
    }

    return stats;
  }

  async collectAll(): Promise<void> {
    console.log('🤖 Starting API-based tool collection...');
    
    let totalSuccess = 0;
    let totalSkipped = 0;
    let totalErrors = 0;

    // Collect from GitHub
    const githubStats = await this.collectFromGitHub();
    totalSuccess += githubStats.success;
    totalSkipped += githubStats.skipped;
    totalErrors += githubStats.errors;

    // Collect from Product Hunt
    const phStats = await this.collectFromProductHunt();
    totalSuccess += phStats.success;
    totalSkipped += phStats.skipped;
    totalErrors += phStats.errors;

    // Collect from Hugging Face
    const hfStats = await this.collectFromHuggingFace();
    totalSuccess += hfStats.success;
    totalSkipped += hfStats.skipped;
    totalErrors += hfStats.errors;

    console.log('\n📊 API Collection Results:');
    console.log(`✅ New tools added: ${totalSuccess}`);
    console.log(`⚪ Tools skipped (already exist): ${totalSkipped}`);
    console.log(`❌ Errors: ${totalErrors}`);
  }
}

export async function runAPICollector(): Promise<void> {
  const collector = new APIToolCollector();
  
  try {
    await collector.initialize();
    await collector.collectAll();
  } catch (error) {
    console.error('❌ API collection failed:', error);
    throw error;
  }
}

export { APIToolCollector };
