'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import AdminLayout from '@/components/admin/AdminLayout';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

const AdminDashboard: React.FC = () => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeSubscriptions: 0,
    monthlyRevenue: 0,
    totalTools: 0,
    pendingTools: 0,
    totalViews: 0,
    monthlyViews: 0
  });

  useEffect(() => {
    // Load admin stats (in real app, fetch from API)
    setStats({
      totalUsers: 1247,
      activeSubscriptions: 892,
      monthlyRevenue: 4460, // £5 * 892 subscribers
      totalTools: 1834,
      pendingTools: 23,
      totalViews: 87432,
      monthlyViews: 12847
    });
  }, []);

  return (
    <AdminLayout>
      <div className="container mx-auto px-4 py-8">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-2">
            Admin Dashboard
          </h1>
          <p className="text-lg text-muted-foreground">
            Manage your AI Tools Directory platform.
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="p-6 text-center bg-blue-50 border-blue-200">
            <div className="text-3xl font-bold text-blue-600 mb-2">{stats.totalUsers}</div>
            <div className="text-sm text-muted-foreground">Total Users</div>
            <div className="text-xs text-green-600 mt-1">+12% this month</div>
          </Card>
          
          <Card className="p-6 text-center bg-green-50 border-green-200">
            <div className="text-3xl font-bold text-green-600 mb-2">{stats.activeSubscriptions}</div>
            <div className="text-sm text-muted-foreground">Active Subscriptions</div>
            <div className="text-xs text-green-600 mt-1">+8% this month</div>
          </Card>
          
          <Card className="p-6 text-center bg-purple-50 border-purple-200">
            <div className="text-3xl font-bold text-purple-600 mb-2">£{stats.monthlyRevenue.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Monthly Revenue</div>
            <div className="text-xs text-green-600 mt-1">+15% this month</div>
          </Card>
          
          <Card className="p-6 text-center bg-orange-50 border-orange-200">
            <div className="text-3xl font-bold text-orange-600 mb-2">{stats.totalTools}</div>
            <div className="text-sm text-muted-foreground">Total Tools</div>
            <div className="text-xs text-blue-600 mt-1">{stats.pendingTools} pending review</div>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          
          {/* User Management */}
          <Card className="p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-blue-500/10 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold">User Management</h3>
            </div>
            <p className="text-muted-foreground mb-4">Manage users, subscriptions, and permissions</p>
            <Link href="/admin/users">
              <Button className="w-full">Manage Users</Button>
            </Link>
          </Card>

          {/* Analytics & Revenue */}
          <Card className="p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-green-500/10 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold">Analytics & Revenue</h3>
            </div>
            <p className="text-muted-foreground mb-4">View detailed analytics and financial reports</p>
            <Link href="/admin/analytics">
              <Button className="w-full">View Analytics</Button>
            </Link>
          </Card>

          {/* Tool Management */}
          <Card className="p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-purple-500/10 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold">Tool Management</h3>
            </div>
            <p className="text-muted-foreground mb-4">Review, approve, and manage AI tools</p>
            <Link href="/admin/tools">
              <Button className="w-full">Manage Tools</Button>
            </Link>
          </Card>

          {/* SEO Management */}
          <Card className="p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-yellow-500/10 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold">SEO Management</h3>
            </div>
            <p className="text-muted-foreground mb-4">Optimize SEO for homepage and tool pages</p>
            <Link href="/admin/seo">
              <Button className="w-full">SEO Settings</Button>
            </Link>
          </Card>

          {/* Content Management */}
          <Card className="p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-red-500/10 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold">Content Management</h3>
            </div>
            <p className="text-muted-foreground mb-4">Manage categories, featured tools, and content</p>
            <Link href="/admin/content">
              <Button className="w-full">Manage Content</Button>
            </Link>
          </Card>

          {/* System Settings */}
          <Card className="p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gray-500/10 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold">System Settings</h3>
            </div>
            <p className="text-muted-foreground mb-4">Configure platform settings and preferences</p>
            <Link href="/admin/settings">
              <Button className="w-full">System Settings</Button>
            </Link>
          </Card>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-6">Recent User Activity</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                <div>
                  <div className="font-medium">New user registration</div>
                  <div className="text-sm text-muted-foreground"><EMAIL></div>
                </div>
                <div className="text-sm text-muted-foreground">2 min ago</div>
              </div>
              <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                <div>
                  <div className="font-medium">Subscription renewal</div>
                  <div className="text-sm text-muted-foreground"><EMAIL></div>
                </div>
                <div className="text-sm text-muted-foreground">15 min ago</div>
              </div>
              <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                <div>
                  <div className="font-medium">Tool submission</div>
                  <div className="text-sm text-muted-foreground">AI Code Reviewer by TechCorp</div>
                </div>
                <div className="text-sm text-muted-foreground">1 hour ago</div>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-6">Pending Actions</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div>
                  <div className="font-medium">Tool Review Required</div>
                  <div className="text-sm text-muted-foreground">23 tools awaiting approval</div>
                </div>
                <Link href="/admin/tools">
                  <Button size="sm">Review</Button>
                </Link>
              </div>
              <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div>
                  <div className="font-medium">User Support Tickets</div>
                  <div className="text-sm text-muted-foreground">7 open tickets</div>
                </div>
                <Button size="sm" onClick={() => alert('Support system coming soon!')}>
                  View
                </Button>
              </div>
              <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                <div>
                  <div className="font-medium">Monthly Report Ready</div>
                  <div className="text-sm text-muted-foreground">December 2024 analytics</div>
                </div>
                <Link href="/admin/analytics">
                  <Button size="sm">Download</Button>
                </Link>
              </div>
            </div>
          </Card>
        </div>

        {/* Back to Regular Dashboard */}
        <div className="mt-8 text-center">
          <Link href="/dashboard">
            <Button variant="outline">← Back to User Dashboard</Button>
          </Link>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
