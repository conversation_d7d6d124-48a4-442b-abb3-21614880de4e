# Authentication & Access Control Testing Guide

This guide helps you test the authentication and paid user access control implementation.

## Prerequisites

1. Start the development server: `npm run dev`
2. Ensure MongoDB is running and connected
3. Have test users in your database (see User Setup section)

## User Setup

You'll need different types of users to test the authentication flow:

### 1. Create a Free User
```javascript
// In MongoDB or through registration
{
  name: "Free User",
  email: "<EMAIL>",
  password: "password123", // Will be hashed
  isPaid: false,
  subscriptionStatus: "inactive",
  role: "user"
}
```

### 2. Create a Paid User
```javascript
// In MongoDB or by updating an existing user
{
  name: "Paid User",
  email: "<EMAIL>",
  password: "password123", // Will be hashed
  isPaid: true,
  subscriptionStatus: "active",
  role: "user"
}
```

### 3. Create an Admin User
```javascript
// In MongoDB or by updating an existing user
{
  name: "Admin User",
  email: "<EMAIL>",
  password: "password123", // Will be hashed
  isPaid: false, // Admin doesn't need to be paid
  subscriptionStatus: "inactive",
  role: "admin"
}
```

## Manual Testing Scenarios

### Scenario 1: Non-authenticated User
1. Open browser in incognito mode
2. Try to access these URLs directly:
   - `http://localhost:3000/tools`
   - `http://localhost:3000/categories`
   - `http://localhost:3000/dashboard`
   - `http://localhost:3000/submit-tool`

**Expected Result:** All should redirect to `/login`

### Scenario 2: Free User (Not Paid)
1. Login with free user credentials
2. Should be redirected to `/dashboard`
3. Dashboard should show upgrade prompt
4. Try to access:
   - `/tools` - Should redirect to dashboard or show upgrade prompt
   - `/categories` - Should redirect to dashboard or show upgrade prompt
5. Click "Upgrade to All Access" button
6. Should be redirected to `/pricing`

**Expected Result:** Free users cannot access tools/categories pages

### Scenario 3: Paid User
1. Login with paid user credentials
2. Should be redirected to `/dashboard`
3. Dashboard should show full dashboard with stats
4. Try to access:
   - `/tools` - Should work normally
   - `/categories` - Should work normally
   - `/categories/[slug]` - Should work normally
5. All features should be accessible

**Expected Result:** Paid users can access all features

### Scenario 4: Admin User
1. Login with admin user credentials
2. Should be redirected to `/dashboard`
3. Dashboard should show full dashboard
4. Try to access:
   - `/tools` - Should work normally
   - `/categories` - Should work normally
   - All other protected routes should work

**Expected Result:** Admin users can access all features regardless of payment status

## Testing the Pricing Flow

### Test Subscription Simulation
1. Login as free user
2. Go to `/pricing`
3. Click "Subscribe Now" on the All Access plan
4. Should redirect to `/dashboard?subscribed=true`
5. Should show welcome message
6. User should now have access to all features

**Note:** This is a simulated flow. In production, you'd integrate with a real payment processor.

## API Testing

### Test Protected API Endpoints
You can test API endpoints using curl or Postman:

```bash
# Test without authentication
curl http://localhost:3000/api/tools
# Should return 401 or redirect

# Test with authentication (after login)
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" http://localhost:3000/api/tools
# Should return tools data for paid users
```

## Common Issues & Troubleshooting

### Issue: Infinite redirect loops
- Check that middleware is not conflicting
- Ensure NextAuth is properly configured
- Verify environment variables are set

### Issue: Session not persisting
- Check that NEXTAUTH_SECRET is set
- Verify database connection
- Clear browser cookies and try again

### Issue: User not recognized as paid
- Check user's `isPaid` field in database
- Verify `subscriptionStatus` is "active"
- Check session callback in NextAuth config

### Issue: Middleware not working
- Verify middleware.ts is in the root directory
- Check the matcher configuration
- Ensure authMiddleware is properly imported

## Database Queries for Testing

### Check User Status
```javascript
// In MongoDB shell or Compass
db.users.find({ email: "<EMAIL>" })
```

### Update User to Paid Status
```javascript
// In MongoDB shell or Compass
db.users.updateOne(
  { email: "<EMAIL>" },
  { 
    $set: { 
      isPaid: true, 
      subscriptionStatus: "active",
      subscriptionStartDate: new Date(),
      subscriptionEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    } 
  }
)
```

### Make User Admin
```javascript
// In MongoDB shell or Compass
db.users.updateOne(
  { email: "<EMAIL>" },
  { $set: { role: "admin" } }
)
```

## Automated Testing

Run the automated test script:
```bash
npm install playwright
node test-auth-flow.js
```

This will test the basic authentication flow automatically.

## Security Checklist

- [ ] Non-authenticated users cannot access protected pages
- [ ] Free users cannot access paid features
- [ ] Paid users can access all features
- [ ] Admin users can access all features
- [ ] Session expires appropriately
- [ ] Middleware protects all necessary routes
- [ ] API endpoints are properly protected
- [ ] User roles are correctly enforced
- [ ] Payment status is accurately checked
- [ ] Upgrade prompts are shown to free users
