import dbConnect from '../lib/db';
import Category from '../models/Category';

// Define initial categories
const categories = [
  {
    name: 'Language Models',
    description: 'AI models that can understand and generate human language',
    slug: 'language-models',
    icon: 'message-square',
    order: 1
  },
  {
    name: 'Image Generation',
    description: 'AI tools that create images from text descriptions',
    slug: 'image-generation',
    icon: 'image',
    order: 2
  },
  {
    name: 'Content Creation',
    description: 'AI tools for creating various types of content',
    slug: 'content-creation',
    icon: 'edit',
    order: 3
  },
  {
    name: 'Writing',
    description: 'AI tools for writing and editing text',
    slug: 'writing',
    icon: 'pen-tool',
    order: 4
  },
  {
    name: 'Productivity',
    description: 'AI tools to enhance personal and professional productivity',
    slug: 'productivity',
    icon: 'zap',
    order: 5
  },
  {
    name: 'Transcription',
    description: 'AI tools for converting speech to text',
    slug: 'transcription',
    icon: 'mic',
    order: 6
  },
  {
    name: 'Video Editing',
    description: 'AI tools for editing and enhancing videos',
    slug: 'video-editing',
    icon: 'video',
    order: 7
  },
  {
    name: 'Video Generation',
    description: 'AI tools for creating videos from text or images',
    slug: 'video-generation',
    icon: 'film',
    order: 8
  },
  {
    name: 'Code Generation',
    description: 'AI tools for generating and assisting with code',
    slug: 'code-generation',
    icon: 'code',
    order: 9
  },
  {
    name: 'Education',
    description: 'AI tools for learning and educational purposes',
    slug: 'education',
    icon: 'book',
    order: 10
  },
  {
    name: 'Audio AI',
    description: 'AI tools for audio processing and generation',
    slug: 'audio-ai',
    icon: 'music',
    order: 11
  },
  {
    name: 'Data Analysis',
    description: 'AI tools for analyzing and visualizing data',
    slug: 'data-analysis',
    icon: 'bar-chart',
    order: 12
  },
  {
    name: 'Business',
    description: 'AI tools for business operations and management',
    slug: 'business',
    icon: 'briefcase',
    order: 13
  },
  {
    name: 'Other',
    description: 'Other AI tools that don\'t fit into specific categories',
    slug: 'other',
    icon: 'grid',
    order: 14
  }
];

async function seedCategories() {
  try {
    // Connect to database
    await dbConnect();
    console.log('Connected to database');

    // Clear existing categories
    await Category.deleteMany({});
    console.log('Cleared existing categories');

    // Create new categories
    for (const category of categories) {
      await Category.create(category);
      console.log(`Created category: ${category.name}`);
    }

    console.log('Categories seeded successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding categories:', error);
    process.exit(1);
  }
}

seedCategories(); 