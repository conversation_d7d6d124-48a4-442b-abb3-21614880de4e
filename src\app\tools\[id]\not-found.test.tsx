import React from 'react'
import { render, screen } from '@testing-library/react'
import NotFound from './not-found'

// Mock next/link
jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ children, ...props }: any) => <a {...props}>{children}</a>,
}))

describe('NotFound Page', () => {
  it('renders not found message', () => {
    render(<NotFound />)
    expect(screen.getByText('Tool Not Found')).toBeInTheDocument()
    expect(screen.getByText("Sorry, we couldn't find the AI tool you're looking for.")).toBeInTheDocument()
  })

  it('renders back to directory link', () => {
    render(<NotFound />)
    const link = screen.getByText('Back to Tools Directory')
    expect(link).toBeInTheDocument()
    expect(link.closest('a')).toHaveAttribute('href', '/')
  })
}) 