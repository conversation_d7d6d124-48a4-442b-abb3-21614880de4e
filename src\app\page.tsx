import React from 'react'
import HeroSection from '@/components/ui/HeroSection'
import FeaturedTools from '@/components/tools/FeaturedTools'
import CategoriesSection from '@/components/categories/CategoriesSection'
import StructuredData from '@/components/seo/StructuredData'
import { generateFAQJsonLd, generateBreadcrumbJsonLd, seoConfig } from '@/lib/seo'

// FAQ data for structured data
const faqs = [
  {
    question: "What is AI Tools Directory?",
    answer: "AI Tools Directory is a comprehensive platform that helps you discover and explore the best AI tools across various categories including productivity, creativity, business, and more."
  },
  {
    question: "How do I find the right AI tool for my needs?",
    answer: "You can browse by categories, use our search functionality, or check out our featured tools section. Each tool comes with detailed descriptions, ratings, and user reviews."
  },
  {
    question: "Are the AI tools listed free to use?",
    answer: "We feature both free and paid AI tools. Each tool listing clearly indicates its pricing model - whether it's free, freemium, or paid."
  },
  {
    question: "How often is the directory updated?",
    answer: "Our directory is updated daily with new AI tools and the latest information about existing tools to ensure you have access to the most current AI innovations."
  }
]

// Breadcrumb data
const breadcrumbs = [
  { name: "Home", url: seoConfig.siteUrl }
]

export default function Home() {
  return (
    <>
      {/* Structured Data for Homepage */}
      <StructuredData data={[
        generateFAQJsonLd(faqs),
        generateBreadcrumbJsonLd(breadcrumbs)
      ]} />
      
      <div className="min-h-screen bg-background">
        <HeroSection />
        
        <main>
          <FeaturedTools />
          <CategoriesSection />
          
          {/* SEO Content Section */}
          <section className="py-16 bg-gradient-to-r from-blue-50 to-purple-50">
            <div className="container mx-auto px-4">
              <div className="max-w-4xl mx-auto text-center">
                <h2 className="text-3xl font-bold mb-6 text-gray-800">
                  Discover the Future of AI Technology
                </h2>
                <p className="text-lg text-gray-600 mb-8">
                  Welcome to the most comprehensive AI tools directory. Whether you're a developer, 
                  content creator, business owner, or AI enthusiast, find the perfect AI solution 
                  to enhance your productivity and creativity.
                </p>
                <div className="grid md:grid-cols-3 gap-8 text-left">
                  <div>
                    <h3 className="text-xl font-semibold mb-3 text-gray-800">🤖 AI Chatbots & Assistants</h3>
                    <p className="text-gray-600">
                      Discover intelligent conversational AI tools that can help with customer service, 
                      content creation, and personal assistance.
                    </p>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-3 text-gray-800">🎨 Creative AI Tools</h3>
                    <p className="text-gray-600">
                      Explore AI-powered tools for image generation, video editing, music creation, 
                      and artistic design that unleash your creativity.
                    </p>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-3 text-gray-800">💼 Business Automation</h3>
                    <p className="text-gray-600">
                      Find AI solutions for workflow automation, data analysis, marketing, 
                      and business intelligence to scale your operations.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>

        <footer className="bg-muted py-8 mt-12">
          <div className="container mx-auto px-4 text-center text-muted-foreground">
            <p>© {new Date().getFullYear()} AI Tools Directory. All rights reserved.</p>
          </div>
        </footer>
      </div>
    </>
  )
}
