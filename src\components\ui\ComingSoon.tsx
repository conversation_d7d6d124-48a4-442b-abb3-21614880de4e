'use client'

import React from 'react';
import { Button } from './Button';

interface ComingSoonProps {
  title?: string;
  message?: string;
  subMessage?: string;
  type?: 'tools' | 'categories' | 'general';
  showRetry?: boolean;
  onRetry?: () => void;
}

export default function ComingSoon({ 
  title = "AI Tools Coming Soon!",
  message = "We're setting up our comprehensive AI tools database.",
  subMessage = "Database is currently unavailable. Please check back later.",
  type = 'tools',
  showRetry = true,
  onRetry
}: ComingSoonProps) {
  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  const getIcon = () => {
    if (type === 'tools') {
      return (
        <svg className="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
        </svg>
      );
    }

    if (type === 'categories') {
      return (
        <svg className="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
      );
    }

    return (
      <svg className="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    );
  };

  return (
    <div className="text-center py-12 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg border border-blue-100">
      <div className="max-w-md mx-auto">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          {getIcon()}
        </div>
        <h3 className="text-xl font-bold text-gray-800 mb-2">{title}</h3>
        <p className="text-gray-600 mb-4">{message}</p>
        <div className="bg-white/60 rounded-lg p-3 mb-4">
          <p className="text-sm text-gray-500">{subMessage}</p>
        </div>
        {showRetry && (
          <Button variant="outline" onClick={handleRetry}>
            Try Again
          </Button>
        )}
      </div>
    </div>
  );
}
