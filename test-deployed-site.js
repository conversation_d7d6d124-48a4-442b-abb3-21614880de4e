// Use node-fetch for compatibility
const fetch = require('node-fetch');

async function testDeployedSite() {
  const baseUrl = 'https://ai-tools-git-sreejagatab-imprint-sreeganeshs-projects.vercel.app';
  
  console.log('Testing deployed site...\n');
  
  // Test main page
  try {
    console.log('1. Testing main page...');
    const mainResponse = await fetch(baseUrl);
    console.log(`   Status: ${mainResponse.status}`);
    const mainText = await mainResponse.text();
    console.log(`   Content length: ${mainText.length} characters`);
    console.log(`   Contains "AI Tools": ${mainText.includes('AI Tools')}`);
    console.log(`   Contains "error": ${mainText.toLowerCase().includes('error')}`);
  } catch (error) {
    console.log(`   Error: ${error.message}`);
  }
  
  // Test debug endpoint
  try {
    console.log('\n2. Testing debug endpoint...');
    const debugResponse = await fetch(`${baseUrl}/debug`);
    console.log(`   Status: ${debugResponse.status}`);
    const debugText = await debugResponse.text();
    console.log(`   Content: ${debugText.substring(0, 500)}...`);
  } catch (error) {
    console.log(`   Error: ${error.message}`);
  }
  
  // Test tools API
  try {
    console.log('\n3. Testing tools API...');
    const toolsResponse = await fetch(`${baseUrl}/api/tools`);
    console.log(`   Status: ${toolsResponse.status}`);
    const toolsText = await toolsResponse.text();
    console.log(`   Content type: ${toolsResponse.headers.get('content-type')}`);
    console.log(`   Content: ${toolsText.substring(0, 500)}...`);
  } catch (error) {
    console.log(`   Error: ${error.message}`);
  }
  
  // Test categories API
  try {
    console.log('\n4. Testing categories API...');
    const categoriesResponse = await fetch(`${baseUrl}/api/categories`);
    console.log(`   Status: ${categoriesResponse.status}`);
    const categoriesText = await categoriesResponse.text();
    console.log(`   Content type: ${categoriesResponse.headers.get('content-type')}`);
    console.log(`   Content: ${categoriesText.substring(0, 500)}...`);
  } catch (error) {
    console.log(`   Error: ${error.message}`);
  }
}

testDeployedSite().catch(console.error);
