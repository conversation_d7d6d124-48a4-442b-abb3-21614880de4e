import fs from 'fs';
import path from 'path';
import { SmartDuplicateHandler } from '../services/smartDuplicateHandler';
import { DatabaseStatsManager } from '../services/databaseStatsManager';
import { generateFallbackPlaceholder } from '../lib/generatePlaceholderImage';

interface RawTool {
  name: string;
  category: string;
  pricing: string;
  description: string;
}

class TodoToolsImporter {
  private duplicateHandler: SmartDuplicateHandler;
  private statsManager: DatabaseStatsManager;

  constructor() {
    this.duplicateHandler = new SmartDuplicateHandler();
    this.statsManager = new DatabaseStatsManager();
  }

  private parseToolsFromTodoFile(): RawTool[] {
    const todoPath = path.join(process.cwd(), 'todo.md');
    
    if (!fs.existsSync(todoPath)) {
      throw new Error('todo.md file not found');
    }

    const content = fs.readFileSync(todoPath, 'utf-8');
    const lines = content.split('\n').filter(line => line.trim());
    
    const tools: RawTool[] = [];
    let processingTabularData = true;
    let currentCategory = 'Other';
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;
      
      // Skip header line
      if (i === 0 && line.startsWith('Name\t')) continue;
      
      // Check if this is a category header (starts with emoji or special formatting)
      if (line.match(/^[📈🎯🎨🔊💼🎬🎮📊🛡️🏠🎓💻🔗👥💡🌐🤖]\s/)) {
        processingTabularData = false;
        currentCategory = this.extractCategoryFromHeader(line);
        continue;
      }
      
      // Check if we're still in tabular format
      if (processingTabularData && line.includes('\t')) {
        const parts = this.parseTabSeparatedLine(line);
        if (parts.length >= 4) {
          tools.push({
            name: parts[0].trim(),
            category: parts[1].trim(),
            pricing: parts[2].trim(),
            description: parts[3].trim().replace(/^"(.*)"$/, '$1')
          });
        }
      } else if (!processingTabularData) {
        // Process simple tool names in list format
        if (!line.match(/^[📈🎯🎨🔊💼🎬🎮📊🛡️🏠🎓💻🔗👥💡🌐🤖]\s/) && 
            !line.startsWith('#') && 
            line.length > 2) {
          tools.push({
            name: line,
            category: currentCategory,
            pricing: 'Free', // Default to Free for listed tools
            description: `AI tool in the ${currentCategory} category.`
          });
        }
      } else {
        // We've moved past tabular format but haven't hit a category header yet
        processingTabularData = false;
      }
    }
    
    return tools;
  }

  private parseTabSeparatedLine(line: string): string[] {
    const parts: string[] = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (char === '"' && (i === 0 || line[i - 1] === '\t')) {
        inQuotes = true;
        current += char;
      } else if (char === '"' && inQuotes && (i === line.length - 1 || line[i + 1] === '\t')) {
        inQuotes = false;
        current += char;
      } else if (char === '\t' && !inQuotes) {
        parts.push(current);
        current = '';
      } else {
        current += char;
      }
    }
    
    if (current) {
      parts.push(current);
    }
    
    return parts;
  }

  private mapCategoryName(categoryName: string): string | null {
    const normalized = categoryName.toLowerCase();
    
    // Category mapping logic
    const mappings: Record<string, string> = {
      'human resources': 'human-resources',
      'analytics & data': 'analytics-data',
      'translation & nlp': 'translation-nlp',
      'social media automation': 'social-media',
      'video & editing': 'video-editing',
      'chatbots ai': 'chatbots',
      'image & design': 'image-design',
      'audio & music': 'audio-music',
      'education & learning': 'education-learning',
      'virtual assistants': 'virtual-assistants',
      'code assistance': 'code-assistance',
      'cybersecurity & fraud': 'cybersecurity',
      'software & website': 'software-development',
      'e-commerce': 'ecommerce',
      'legal assistance': 'legal',
      'real estate': 'other' // Map to other if no real estate category
    };
    
    const mapped = mappings[normalized];
    if (mapped) {
      return mapped;
    }
    
    // Default to 'other' category
    return 'other';
  }

  private extractCategoryFromHeader(line: string): string {
    // Remove emoji and clean up the category name
    const categoryName = line.replace(/^[📈🎯🎨🔊💼🎬🎮📊🛡️🏠🎓💻🔗👥💡🌐🤖]\s*/, '').trim();
    
    // Map common category patterns
    const categoryMappings: Record<string, string> = {
      'AI Marketing & SEO': 'Social Media',
      'AI Specialized Tools': 'Other',
      'AI Content Creation': 'Content Creation',
      'AI Design & Image': 'Image Generation',
      'AI Audio & Music': 'Audio AI',
      'AI Business & Productivity': 'Productivity',
      'AI Video & Editing': 'Video Editing',
      'AI Gaming & Entertainment': 'Other',
      'AI Data & Analytics': 'Data Analysis',
      'AI Security & Privacy': 'Other',
      'AI Real Estate': 'Other',
      'AI Education & Learning': 'Education',
      'AI Development & Code': 'Code Generation',
      'AI Integration & APIs': 'Other',
      'AI Community & Collaboration': 'Other',
      'AI Innovation & Research': 'Other',
      'AI Global & Multilingual': 'Translation & NLP',
      'AI Automation & Workflows': 'Other'
    };
    
    return categoryMappings[categoryName] || 'Other';
  }

  private convertRawToolToTool(rawTool: RawTool): any {
    const categorySlug = this.mapCategoryName(rawTool.category);
    
    // Map pricing to correct enum values
    let pricing: string;
    const pricingLower = rawTool.pricing.toLowerCase();
    if (pricingLower === 'free') {
      pricing = 'Free';
    } else if (pricingLower === 'freemium') {
      pricing = 'Freemium';
    } else {
      pricing = 'Paid';
    }
    
    // Generate a placeholder URL based on tool name
    const urlSlug = rawTool.name.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
    const placeholderUrl = `https://placeholder.com/tools/${urlSlug}`;
    
    return {
      name: rawTool.name,
      description: rawTool.description,
      url: placeholderUrl, // Generate placeholder URL
      imageUrl: generateFallbackPlaceholder(rawTool.name), // Generate local placeholder image
      category: categorySlug, // Use slug instead of ObjectId
      pricing: pricing,
      features: [], // Will be populated by duplicate handler if merging
      source: 'todo.md import'
    };
  }

  async importTools(): Promise<void> {
    console.log('🚀 Starting todo.md tools import...');
    
    try {
      // Initialize the duplicate handler
      await this.duplicateHandler.initialize();
      
      const rawTools = this.parseToolsFromTodoFile();
      console.log(`📋 Parsed ${rawTools.length} tools from todo.md`);
      
      let imported = 0;
      let duplicates = 0;
      let errors = 0;
      
      console.log('🔄 Processing tools with duplicate detection...');
      
      for (let i = 0; i < rawTools.length; i++) {
        const rawTool = rawTools[i];
        
        try {
          console.log(`\n[${i + 1}/${rawTools.length}] Processing: ${rawTool.name}`);
          
          const tool = this.convertRawToolToTool(rawTool);
          
          const result = await this.duplicateHandler.processNewTool(tool);
          
          if (result.action === 'created') {
            imported++;
            console.log(`✅ Added: ${tool.name}`);
          } else if (result.action === 'updated') {
            duplicates++;
            console.log(`🔄 Updated existing: ${tool.name}`);
          } else {
            duplicates++;
            console.log(`⏭️  Skipped: ${tool.name}`);
          }
          
        } catch (error) {
          errors++;
          console.error(`❌ Error processing ${rawTool.name}:`, error);
        }
      }
      
      console.log('\n📊 Import Summary:');
      console.log(`✅ Successfully imported: ${imported} tools`);
      console.log(`🔄 Duplicates handled: ${duplicates} tools`);
      console.log(`❌ Errors: ${errors} tools`);
      console.log(`📋 Total processed: ${rawTools.length} tools`);
      
      // Update database statistics
      console.log('\n🔄 Updating database statistics...');
      await this.statsManager.updateCategoryCounts();
      const stats = await this.statsManager.generateReport();
      
      console.log('\n📈 Final Database Stats:');
      console.log(stats);
      
    } catch (error) {
      console.error('❌ Import failed:', error);
      throw error;
    }
  }
}

// Run the import if this script is executed directly
if (require.main === module) {
  const importer = new TodoToolsImporter();
  importer.importTools()
    .then(() => {
      console.log('\n🎉 Todo.md import completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Import failed:', error);
      process.exit(1);
    });
}

export { TodoToolsImporter };
