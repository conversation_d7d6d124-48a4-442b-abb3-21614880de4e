/**
 * Generates a placeholder image URL based on the tool's name
 * Uses DiceBear API to create a unique avatar for each tool
 * Falls back to a data URL SVG if external services are unavailable
 */
export function generatePlaceholderImage(name: string): string {
  // Encode the name for use in URL
  const encodedName = encodeURIComponent(name);
  
  // Return a URL that includes the name as a seed for consistent generation
  return `https://api.dicebear.com/7.x/shapes/svg?seed=${encodedName}&backgroundColor=b6e3f4,c0aede,d1f4d9,ffdfbf,ffd5dc`;
}

/**
 * Generates a fallback SVG placeholder as a data URL when external services fail
 */
export function generateFallbackPlaceholder(name: string): string {
  const initials = name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
    
  const colors = ['#3b82f6', '#8b5cf6', '#10b981', '#f59e0b', '#ef4444', '#6366f1'];
  const colorIndex = name.length % colors.length;
  const backgroundColor = colors[colorIndex];
  
  const svg = `
    <svg width="400" height="400" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
      <rect width="400" height="400" fill="${backgroundColor}"/>
      <text x="200" y="220" font-family="Arial, sans-serif" font-size="120" font-weight="bold" 
            text-anchor="middle" fill="white">${initials}</text>
    </svg>
  `;
  
  return `data:image/svg+xml;base64,${btoa(svg)}`;
} 