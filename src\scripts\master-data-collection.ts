import { runAPICollector } from '../services/apiToolCollector';
import { runEnhancedScraper } from '../services/enhancedMultiSourceScraper';
import { SmartDuplicateHandler } from '../services/smartDuplicateHandler';
import DatabaseStatsManager from '../services/databaseStatsManager';

interface CollectionResults {
  api: { success: number; skipped: number; errors: number };
  scraper: {
    rss: { success: number; skipped: number; errors: number };
    github: { success: number; skipped: number; errors: number };
    total: { success: number; skipped: number; errors: number };
  };
  maintenance: {
    duplicatesRemoved: number;
    categoriesUpdated: number;
  };
}

async function runMasterDataCollection(): Promise<CollectionResults> {
  console.log('🚀 MASTER DATA COLLECTION STARTED');
  console.log('=' .repeat(60));
  
  const results: CollectionResults = {
    api: { success: 0, skipped: 0, errors: 0 },
    scraper: {
      rss: { success: 0, skipped: 0, errors: 0 },
      github: { success: 0, skipped: 0, errors: 0 },
      total: { success: 0, skipped: 0, errors: 0 }
    },
    maintenance: {
      duplicatesRemoved: 0,
      categoriesUpdated: 0
    }
  };

  try {
    // Phase 1: API Collection
    console.log('\n🔌 PHASE 1: API-Based Collection');
    console.log('-'.repeat(40));
    
    try {
      await runAPICollector();
      console.log('✅ API collection completed');
    } catch (error) {
      console.error('❌ API collection failed:', error);
      results.api.errors = 1;
    }

    // Phase 2: Enhanced Multi-Source Scraping
    console.log('\n🕷️ PHASE 2: Enhanced Multi-Source Scraping');
    console.log('-'.repeat(40));
    
    try {
      const scraperResults = await runEnhancedScraper();
      results.scraper = scraperResults;
      console.log('✅ Enhanced scraping completed');
    } catch (error) {
      console.error('❌ Enhanced scraping failed:', error);
      results.scraper.total.errors = 1;
    }

    // Phase 3: Duplicate Cleanup
    console.log('\n🔧 PHASE 3: Smart Duplicate Handling');
    console.log('-'.repeat(40));
    
    try {
      const duplicateHandler = new SmartDuplicateHandler();
      const cleanupResult = await duplicateHandler.cleanupAllDuplicates();
      results.maintenance.duplicatesRemoved = cleanupResult.removed;
      console.log(`✅ Duplicate cleanup: ${cleanupResult.merged} merged, ${cleanupResult.removed} removed`);
    } catch (error) {
      console.error('❌ Duplicate cleanup failed:', error);
    }

    // Phase 4: Database Maintenance & Stats Update
    console.log('\n📊 PHASE 4: Database Maintenance & Stats');
    console.log('-'.repeat(40));
    
    try {
      const statsManager = new DatabaseStatsManager();
      await statsManager.runMaintenanceAfterScraping();
      console.log('✅ Database maintenance completed');
    } catch (error) {
      console.error('❌ Database maintenance failed:', error);
    }

    // Final Summary
    console.log('\n🎉 COLLECTION SUMMARY');
    console.log('=' .repeat(60));
    
    const totalNewTools = results.scraper.total.success;
    const totalProcessed = results.scraper.total.success + results.scraper.total.skipped;
    
    console.log(`📈 Collection Results:`);
    console.log(`   • RSS Feeds: ${results.scraper.rss.success} new tools`);
    console.log(`   • GitHub Repos: ${results.scraper.github.success} new tools`);
    console.log(`   • Total New Tools: ${totalNewTools}`);
    console.log(`   • Total Processed: ${totalProcessed}`);
    console.log(`   • Duplicates Removed: ${results.maintenance.duplicatesRemoved}`);
    
    console.log(`\n⚡ Success Rate: ${totalProcessed > 0 ? (totalNewTools / totalProcessed * 100).toFixed(1) : '0'}%`);
    
    if (results.scraper.total.errors > 0 || results.api.errors > 0) {
      console.log(`⚠️  Errors encountered during collection`);
    }

  } catch (error) {
    console.error('💥 Master collection failed:', error);
  }

  return results;
}

async function main() {
  const startTime = Date.now();
  
  try {
    const results = await runMasterDataCollection();
    
    const duration = (Date.now() - startTime) / 1000;
    console.log(`\n⏱️  Total execution time: ${duration.toFixed(1)} seconds`);
    
    // Exit with error code if there were significant issues
    if (results.scraper.total.errors > 0 && results.scraper.total.success === 0) {
      process.exit(1);
    }
    
    console.log('\n🎯 Master data collection completed successfully!');
    process.exit(0);
    
  } catch (error) {
    console.error('💥 Fatal error in master collection:', error);
    process.exit(1);
  }
}

main();
