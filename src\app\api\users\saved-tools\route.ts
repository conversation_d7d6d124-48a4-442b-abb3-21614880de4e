import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { authMiddleware } from '@/middleware/auth';

export async function GET(request: NextRequest) {
  try {
    // Apply auth middleware
    const authResponse = await authMiddleware(request);
    if (authResponse instanceof NextResponse) {
      return authResponse;
    }
    
    await dbConnect();
    
    // Get user ID from headers set by middleware
    const userId = request.headers.get('x-user-id');
    
    // Find user by ID and populate saved tools
    const user = await User.findById(userId)
      .select('savedTools')
      .populate('savedTools');
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(user.savedTools);
  } catch (error) {
    console.error('Error fetching saved tools:', error);
    return NextResponse.json(
      { error: 'Failed to fetch saved tools' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Apply auth middleware
    const authResponse = await authMiddleware(request);
    if (authResponse instanceof NextResponse) {
      return authResponse;
    }
    
    await dbConnect();
    
    // Get user ID from headers set by middleware
    const userId = request.headers.get('x-user-id');
    
    // Get tool ID from request body
    const { toolId } = await request.json();
    
    if (!toolId) {
      return NextResponse.json(
        { error: 'Tool ID is required' },
        { status: 400 }
      );
    }
    
    // Find user by ID
    const user = await User.findById(userId);
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Check if tool is already saved
    if (user.savedTools.includes(toolId)) {
      return NextResponse.json(
        { error: 'Tool already saved' },
        { status: 400 }
      );
    }
    
    // Add tool to saved tools
    user.savedTools.push(toolId);
    
    // Save updated user
    await user.save();
    
    return NextResponse.json({ message: 'Tool saved successfully' });
  } catch (error) {
    console.error('Error saving tool:', error);
    return NextResponse.json(
      { error: 'Failed to save tool' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Apply auth middleware
    const authResponse = await authMiddleware(request);
    if (authResponse instanceof NextResponse) {
      return authResponse;
    }
    
    await dbConnect();
    
    // Get user ID from headers set by middleware
    const userId = request.headers.get('x-user-id');
    
    // Get tool ID from request body
    const { toolId } = await request.json();
    
    if (!toolId) {
      return NextResponse.json(
        { error: 'Tool ID is required' },
        { status: 400 }
      );
    }
    
    // Find user by ID
    const user = await User.findById(userId);
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Remove tool from saved tools
    user.savedTools = user.savedTools.filter(
      (id: any) => id.toString() !== toolId
    );
    
    // Save updated user
    await user.save();
    
    return NextResponse.json({ message: 'Tool removed from saved tools' });
  } catch (error) {
    console.error('Error removing saved tool:', error);
    return NextResponse.json(
      { error: 'Failed to remove saved tool' },
      { status: 500 }
    );
  }
} 