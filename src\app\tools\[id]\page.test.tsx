import React from 'react'
import { render, screen } from '@testing-library/react'
import ToolPage from './page'
import { tools } from '@/data/tools'

// Mock next/navigation
jest.mock('next/navigation', () => ({
  notFound: jest.fn(),
}))

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => <img {...props} />,
}))

// Mock next/link
jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ children, ...props }: any) => <a {...props}>{children}</a>,
}))

describe('ToolPage', () => {
  it('renders tool details correctly', () => {
    const tool = tools[0] // ChatGPT
    render(<ToolPage params={{ id: tool.id }} />)

    // Check if main elements are rendered
    expect(screen.getByText(tool.name)).toBeInTheDocument()
    expect(screen.getByText(tool.description)).toBeInTheDocument()
    expect(screen.getByText(tool.category)).toBeInTheDocument()
    expect(screen.getByText(tool.pricing)).toBeInTheDocument()
    expect(screen.getByText(tool.rating.toFixed(1))).toBeInTheDocument()

    // Check if back link is present
    expect(screen.getByText('Back to Tools')).toBeInTheDocument()

    // Check if visit website button is present
    const visitButton = screen.getByText('Visit Website')
    expect(visitButton).toBeInTheDocument()
    expect(visitButton.closest('a')).toHaveAttribute('href', tool.url)
    expect(visitButton.closest('a')).toHaveAttribute('target', '_blank')
    expect(visitButton.closest('a')).toHaveAttribute('rel', 'noopener noreferrer')
  })

  it('calls notFound when tool is not found', () => {
    const notFound = require('next/navigation').notFound
    render(<ToolPage params={{ id: 'non-existent-id' }} />)
    expect(notFound).toHaveBeenCalled()
  })

  it('renders correct number of stars based on rating', () => {
    const tool = tools[0] // ChatGPT with rating 4.8
    render(<ToolPage params={{ id: tool.id }} />)

    const fullStars = screen.getAllByTestId(/^full-star-/)
    const halfStars = screen.getAllByTestId(/^half-star-/)
    const emptyStars = screen.getAllByTestId(/^empty-star-/)

    expect(fullStars).toHaveLength(4)
    expect(halfStars).toHaveLength(1)
    expect(emptyStars).toHaveLength(0)
  })

  it('applies correct pricing badge color', () => {
    const freeTool = tools.find(t => t.pricing === 'Free')!
    const freemiumTool = tools.find(t => t.pricing === 'Freemium')!
    const paidTool = tools.find(t => t.pricing === 'Paid')!

    // Test Free pricing
    const { rerender } = render(<ToolPage params={{ id: freeTool.id }} />)
    expect(screen.getByText('Free')).toHaveClass('bg-green-100', 'text-green-800')

    // Test Freemium pricing
    rerender(<ToolPage params={{ id: freemiumTool.id }} />)
    expect(screen.getByText('Freemium')).toHaveClass('bg-blue-100', 'text-blue-800')

    // Test Paid pricing
    rerender(<ToolPage params={{ id: paidTool.id }} />)
    expect(screen.getByText('Paid')).toHaveClass('bg-purple-100', 'text-purple-800')
  })
}) 