'use client'

import React, { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { canAccessPaidFeatures } from '@/lib/auth';
import Link from 'next/link';
import UpgradePrompt from '@/components/ui/UpgradePrompt';

interface DashboardLayoutProps {
  children: React.ReactNode;
  requiresPaid?: boolean;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children, requiresPaid = false }) => {
  const router = useRouter();
  const pathname = usePathname();
  const { data: session, status } = useSession();

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/login?callbackUrl=' + encodeURIComponent(pathname));
      return;
    }
  }, [session, status, router, pathname]);

  const isActive = (path: string) => {
    return pathname === path;
  };

  const dashboardNavItems = [
    { href: '/dashboard', label: 'Overview', icon: '📊', requiresPaid: false },
    { href: '/dashboard/my-tools', label: 'My Tools', icon: '🔧', requiresPaid: true },
    { href: '/dashboard/analytics', label: 'Analytics', icon: '📈', requiresPaid: true },
    { href: '/dashboard/settings', label: 'Settings', icon: '⚙️', requiresPaid: false },
  ];

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  // Check if page requires paid access and user doesn't have it
  if (requiresPaid && !canAccessPaidFeatures(session)) {
    return (
      <UpgradePrompt 
        feature="dashboard features" 
        description="Upgrade to access advanced dashboard features including tool management and analytics." 
      />
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Dashboard Header */}
      <div className="bg-card border-b">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/" className="text-lg font-semibold text-muted-foreground hover:text-foreground">
                ← Back to Site
              </Link>
              <div className="h-6 w-px bg-border"></div>
              <h1 className="text-xl font-bold">Dashboard</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-muted-foreground">
                Welcome, {session.user?.name}
              </span>
              <div className="h-8 w-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">
                {session.user?.name?.charAt(0).toUpperCase()}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Dashboard Sidebar */}
        <div className="hidden md:flex md:w-64 md:flex-col">
          <div className="flex-1 bg-card border-r">
            <nav className="p-4 space-y-2">
              {dashboardNavItems.map((item) => {
                const hasAccess = !item.requiresPaid || canAccessPaidFeatures(session);
                
                return (
                  <Link
                    key={item.href}
                    href={hasAccess ? item.href : '/pricing'}
                    className={`flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      isActive(item.href)
                        ? 'bg-primary text-primary-foreground'
                        : hasAccess
                        ? 'text-muted-foreground hover:text-foreground hover:bg-muted'
                        : 'text-muted-foreground/50 cursor-not-allowed'
                    }`}
                  >
                    <span className="text-lg">{item.icon}</span>
                    <span>{item.label}</span>
                    {!hasAccess && (
                      <span className="ml-auto text-xs bg-primary text-primary-foreground px-1.5 py-0.5 rounded">
                        Pro
                      </span>
                    )}
                  </Link>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Mobile Dashboard Navigation */}
        <div className="md:hidden w-full">
          <div className="bg-card border-b p-4">
            <div className="flex space-x-2 overflow-x-auto">
              {dashboardNavItems.map((item) => {
                const hasAccess = !item.requiresPaid || canAccessPaidFeatures(session);
                
                return (
                  <Link
                    key={item.href}
                    href={hasAccess ? item.href : '/pricing'}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium whitespace-nowrap transition-colors ${
                      isActive(item.href)
                        ? 'bg-primary text-primary-foreground'
                        : hasAccess
                        ? 'text-muted-foreground hover:text-foreground hover:bg-muted'
                        : 'text-muted-foreground/50 cursor-not-allowed'
                    }`}
                  >
                    <span>{item.icon}</span>
                    <span>{item.label}</span>
                    {!hasAccess && (
                      <span className="text-xs bg-primary text-primary-foreground px-1 py-0.5 rounded">
                        Pro
                      </span>
                    )}
                  </Link>
                );
              })}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          {children}
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;
