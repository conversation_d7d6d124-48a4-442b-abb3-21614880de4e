import { Session } from 'next-auth'

export interface ExtendedUser {
  id: string
  email: string
  name: string
  role: string
  isPaid: boolean
  subscriptionStatus: string
  image?: string
}

export interface ExtendedSession extends Session {
  user: ExtendedUser
}

export function isPaidUser(session: Session | null): boolean {
  if (!session?.user) return false
  
  const user = session.user as ExtendedUser
  return !!(user.isPaid && user.subscriptionStatus === 'active')
}

export function isAdmin(session: Session | null): boolean {
  if (!session?.user) return false
  
  const user = session.user as ExtendedUser
  return user.role === 'admin'
}

export function canAccessPaidFeatures(session: Session | null): boolean {
  return isPaidUser(session) || isAdmin(session)
}
