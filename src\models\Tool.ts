import mongoose from 'mongoose';

// Define the schema for the Tool model
const ToolSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please provide a name for the tool'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters'],
  },
  description: {
    type: String,
    required: [true, 'Please provide a description for the tool'],
    trim: true,
    maxlength: [1000, 'Description cannot be more than 1000 characters'],
  },
  imageUrl: {
    type: String,
    required: [true, 'Please provide an image URL for the tool'],
    trim: true,
  },
  rating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5,
  },
  ratingCount: {
    type: Number,
    default: 0,
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'Please provide a category for the tool'],
  },
  pricing: {
    type: String,
    required: [true, 'Please provide pricing information for the tool'],
    enum: ['Free', 'Freemium', 'Paid'],
  },
  url: {
    type: String,
    required: [true, 'Please provide a URL for the tool'],
    trim: true,
  },
  features: {
    type: [String],
    default: [],
  },
  tags: {
    type: [String],
    default: [],
  },
  company: {
    type: String,
    trim: true,
  },
  apiAvailable: {
    type: Boolean,
    default: false,
  },
  apiUrl: {
    type: String,
    trim: true,
  },
  documentationUrl: {
    type: String,
    trim: true,
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'pending'],
    default: 'active',
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Update the updatedAt field before saving
ToolSchema.pre('save', function(this: any, next: () => void) {
  this.updatedAt = new Date();
  next();
});

// Create text indexes for search functionality
ToolSchema.index({ name: 'text', description: 'text', category: 'text', tags: 'text' });

// Check if the model already exists to prevent overwriting
export default mongoose.models.Tool || mongoose.model('Tool', ToolSchema); 