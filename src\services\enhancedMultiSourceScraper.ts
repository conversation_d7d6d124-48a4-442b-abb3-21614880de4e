import Parser from 'rss-parser';
import { SmartDuplicateHandler } from './smartDuplicateHandler';
import dbConnect from '../lib/db';
import Category from '../models/Category';

interface RSSSource {
  name: string;
  url: string;
  category: string;
  enabled: boolean;
  parseStrategy: 'ai-news' | 'github-releases' | 'product-announcements' | 'blog-posts';
}

interface GitHubRepo {
  owner: string;
  repo: string;
  category: string;
  enabled: boolean;
}

interface VideoSource {
  platform: 'youtube' | 'vimeo';
  channelId?: string;
  playlistId?: string;
  searchQuery?: string;
  category: string;
  enabled: boolean;
}

interface ScrapedTool {
  name: string;
  description: string;
  url?: string;
  category?: string;
  source: string;
  features?: string[];
  company?: string;
  pricing?: string;
  imageUrl?: string;
}

// RSS feeds for AI tool announcements and news
const RSS_SOURCES: RSSSource[] = [
  {
    name: 'AI News - MIT Technology Review',
    url: 'https://www.technologyreview.com/rss/',
    category: 'other',
    enabled: true,
    parseStrategy: 'ai-news'
  },
  {
    name: 'VentureBeat AI',
    url: 'https://venturebeat.com/feed/',
    category: 'other',
    enabled: true,
    parseStrategy: 'ai-news'
  },
  {
    name: 'GitHub Trending AI',
    url: 'https://github.com/trending/python.atom',
    category: 'code-generation',
    enabled: true,
    parseStrategy: 'github-releases'
  }
];

// GitHub repositories to monitor
const GITHUB_REPOS: GitHubRepo[] = [
  { owner: 'openai', repo: 'whisper', category: 'transcription', enabled: true },
  { owner: 'microsoft', repo: 'semantic-kernel', category: 'code-generation', enabled: true },
  { owner: 'langchain-ai', repo: 'langchain', category: 'code-generation', enabled: true },
  { owner: 'huggingface', repo: 'transformers', category: 'language-models', enabled: true },
  { owner: 'microsoft', repo: 'DialoGPT', category: 'language-models', enabled: true },
  { owner: 'openai', repo: 'openai-python', category: 'code-generation', enabled: true },
  { owner: 'anthropics', repo: 'anthropic-sdk-python', category: 'language-models', enabled: true },
  { owner: 'chroma-core', repo: 'chroma', category: 'data-analysis', enabled: true },
  { owner: 'pinecone-io', repo: 'pinecone-python-client', category: 'data-analysis', enabled: true },
  { owner: 'weaviate', repo: 'weaviate', category: 'data-analysis', enabled: true }
];

// Video sources for AI tool discovery
const VIDEO_SOURCES: VideoSource[] = [
  {
    platform: 'youtube',
    searchQuery: 'AI tools 2024 productivity',
    category: 'productivity',
    enabled: false // Disabled due to API requirements
  }
];

export class EnhancedMultiSourceScraper {
  private duplicateHandler: SmartDuplicateHandler;
  private parser: Parser;
  private categoryMap: Map<string, string> = new Map();

  constructor() {
    this.duplicateHandler = new SmartDuplicateHandler();
    this.parser = new Parser({
      customFields: {
        item: ['author', 'creator', 'category', 'tags']
      }
    });
  }

  async initialize() {
    await dbConnect();
    await this.loadCategoryMap();
  }

  private async loadCategoryMap() {
    const categories = await Category.find({});
    categories.forEach(category => {
      this.categoryMap.set(category.slug, category._id.toString());
      this.categoryMap.set(category.name.toLowerCase(), category._id.toString());
    });
  }

  private extractToolFromRSSItem(item: any, source: RSSSource): ScrapedTool | null {
    try {
      let name = '';
      let description = '';
      let url = item.link || '';

      switch (source.parseStrategy) {
        case 'ai-news':
          name = item.title?.replace(/^\[.*?\]\s*/, '') || ''; // Remove prefixes like [Paper] 
          description = item.contentSnippet || item.summary || item.content || '';
          break;

        case 'product-announcements':
          name = item.title || '';
          description = item.contentSnippet || item.summary || '';
          break;

        case 'github-releases':
          name = item.title?.replace(/^Release\s*/, '') || '';
          description = item.contentSnippet || `GitHub release: ${item.title}`;
          break;

        case 'blog-posts':
          name = item.title || '';
          description = item.contentSnippet || item.summary || '';
          break;
      }

      // Filter out non-tool content
      if (!name || name.length < 3) return null;
      if (!description || description.length < 20) return null;

      // Skip purely news/article content
      const toolIndicators = ['tool', 'ai', 'model', 'api', 'platform', 'app', 'software', 'library', 'framework'];
      const hasToolIndicator = toolIndicators.some(indicator => 
        name.toLowerCase().includes(indicator) || description.toLowerCase().includes(indicator)
      );

      if (!hasToolIndicator) return null;

      return {
        name: name.trim(),
        description: this.cleanDescription(description),
        url,
        category: source.category,
        source: `rss-${source.name}`,
        features: this.extractFeatures(description),
        company: item.author || item.creator
      };

    } catch (error) {
      console.error(`Error parsing RSS item from ${source.name}:`, error);
      return null;
    }
  }

  private cleanDescription(description: string): string {
    return description
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim()
      .substring(0, 500); // Limit length
  }

  private extractFeatures(text: string): string[] {
    const features: string[] = [];
    const featurePatterns = [
      /supports?\s+([^.]+)/gi,
      /features?\s+([^.]+)/gi,
      /includes?\s+([^.]+)/gi,
      /provides?\s+([^.]+)/gi,
      /enables?\s+([^.]+)/gi
    ];

    featurePatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const feature = match.replace(pattern, '$1').trim();
          if (feature.length > 5 && feature.length < 100) {
            features.push(feature);
          }
        });
      }
    });

    return features.slice(0, 5); // Limit to 5 features
  }

  async scrapeRSSFeeds(): Promise<{ success: number; skipped: number; errors: number }> {
    const stats = { success: 0, skipped: 0, errors: 0 };

    for (const source of RSS_SOURCES) {
      if (!source.enabled) continue;

      try {
        console.log(`📡 Parsing RSS feed: ${source.name}`);
        const feed = await this.parser.parseURL(source.url);

        const tools: ScrapedTool[] = [];
        
        for (const item of feed.items.slice(0, 10)) { // Limit to recent 10 items
          const tool = this.extractToolFromRSSItem(item, source);
          if (tool) {
            tools.push(tool);
          }
        }

        console.log(`🔍 Found ${tools.length} potential tools from ${source.name}`);

        for (const tool of tools) {
          const result = await this.duplicateHandler.processNewTool(tool);
          if (result.action === 'created') stats.success++;
          else if (result.action === 'updated') stats.success++;
          else stats.skipped++;
        }

      } catch (error) {
        console.error(`❌ Error scraping RSS ${source.name}:`, error);
        stats.errors++;
      }

      // Rate limiting
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    return stats;
  }

  async scrapeGitHubRepos(): Promise<{ success: number; skipped: number; errors: number }> {
    const stats = { success: 0, skipped: 0, errors: 0 };
    const githubToken = process.env.GITHUB_TOKEN;

    if (!githubToken) {
      console.log('⚠️ GitHub token not provided, skipping GitHub scraping');
      return stats;
    }

    for (const repo of GITHUB_REPOS) {
      if (!repo.enabled) continue;

      try {
        console.log(`🐙 Fetching GitHub repo: ${repo.owner}/${repo.repo}`);
        
        const response = await fetch(`https://api.github.com/repos/${repo.owner}/${repo.repo}`, {
          headers: {
            'Authorization': `token ${githubToken}`,
            'Accept': 'application/vnd.github.v3+json'
          }
        });

        if (!response.ok) {
          throw new Error(`GitHub API error: ${response.status}`);
        }

        const repoData = await response.json();

        const tool: ScrapedTool = {
          name: repoData.name,
          description: repoData.description || `AI tool from ${repo.owner}`,
          url: repoData.html_url,
          category: repo.category,
          source: `github-${repo.owner}`,
          company: repo.owner,
          features: [
            `Open source on GitHub`,
            `${repoData.stargazers_count} stars`,
            `${repoData.language || 'Multiple'} programming language`,
            repoData.license?.name || 'Custom license'
          ],
          pricing: 'Free'
        };

        const result = await this.duplicateHandler.processNewTool(tool);
        if (result.action === 'created' || result.action === 'updated') {
          stats.success++;
        } else {
          stats.skipped++;
        }

      } catch (error) {
        console.error(`❌ Error scraping GitHub ${repo.owner}/${repo.repo}:`, error);
        stats.errors++;
      }

      // Rate limiting for GitHub API
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    return stats;
  }

  async scrapeAllSources(): Promise<{
    rss: { success: number; skipped: number; errors: number };
    github: { success: number; skipped: number; errors: number };
    total: { success: number; skipped: number; errors: number };
  }> {
    console.log('🚀 Starting Enhanced Multi-Source Scraping...');

    const rssStats = await this.scrapeRSSFeeds();
    const githubStats = await this.scrapeGitHubRepos();

    const total = {
      success: rssStats.success + githubStats.success,
      skipped: rssStats.skipped + githubStats.skipped,
      errors: rssStats.errors + githubStats.errors
    };

    return {
      rss: rssStats,
      github: githubStats,
      total
    };
  }
}

export async function runEnhancedScraper(): Promise<{
  rss: { success: number; skipped: number; errors: number };
  github: { success: number; skipped: number; errors: number };
  total: { success: number; skipped: number; errors: number };
}> {
  const scraper = new EnhancedMultiSourceScraper();
  
  try {
    await scraper.initialize();
    const results = await scraper.scrapeAllSources();

    console.log('\n📊 Enhanced Scraping Results:');
    console.log('RSS Feeds:');
    console.log(`  ✅ Success: ${results.rss.success}`);
    console.log(`  ⚪ Skipped: ${results.rss.skipped}`);
    console.log(`  ❌ Errors: ${results.rss.errors}`);
    
    console.log('GitHub Repos:');
    console.log(`  ✅ Success: ${results.github.success}`);
    console.log(`  ⚪ Skipped: ${results.github.skipped}`);
    console.log(`  ❌ Errors: ${results.github.errors}`);
    
    console.log('Total:');
    console.log(`  ✅ Success: ${results.total.success}`);
    console.log(`  ⚪ Skipped: ${results.total.skipped}`);
    console.log(`  ❌ Errors: ${results.total.errors}`);

    return results;

  } catch (error) {
    console.error('❌ Enhanced scraper failed:', error);
    return {
      rss: { success: 0, skipped: 0, errors: 1 },
      github: { success: 0, skipped: 0, errors: 0 },
      total: { success: 0, skipped: 0, errors: 1 }
    };
  }
}
