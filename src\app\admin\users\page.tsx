'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AdminLayout from '@/components/admin/AdminLayout';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';

interface User {
  _id: string;
  name: string;
  email: string;
  role: string;
  subscriptionStatus: string;
  joinDate: string;
  lastActive: string;
  toolsSubmitted: number;
  revenue: number;
}

const AdminUsersPage: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  useEffect(() => {
    // Load users (in real app, fetch from API)
    const mockUsers: User[] = [
      {
        _id: '1',
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'admin',
        subscriptionStatus: 'active',
        joinDate: '2024-01-15',
        lastActive: '2024-01-20',
        toolsSubmitted: 5,
        revenue: 0
      },
      {
        _id: '2',
        name: 'Regular User',
        email: '<EMAIL>',
        role: 'user',
        subscriptionStatus: 'active',
        joinDate: '2024-01-10',
        lastActive: '2024-01-20',
        toolsSubmitted: 3,
        revenue: 25 // 5 months * £5
      },
      {
        _id: '3',
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'user',
        subscriptionStatus: 'active',
        joinDate: '2024-01-05',
        lastActive: '2024-01-19',
        toolsSubmitted: 8,
        revenue: 40 // 8 months * £5
      },
      {
        _id: '4',
        name: 'Sarah Wilson',
        email: '<EMAIL>',
        role: 'user',
        subscriptionStatus: 'active',
        joinDate: '2023-12-20',
        lastActive: '2024-01-20',
        toolsSubmitted: 12,
        revenue: 60 // 12 months * £5
      },
      {
        _id: '5',
        name: 'Mike Johnson',
        email: '<EMAIL>',
        role: 'user',
        subscriptionStatus: 'cancelled',
        joinDate: '2023-11-15',
        lastActive: '2024-01-18',
        toolsSubmitted: 2,
        revenue: 15 // 3 months * £5
      },
      {
        _id: '6',
        name: 'Emma Davis',
        email: '<EMAIL>',
        role: 'user',
        subscriptionStatus: 'trial',
        joinDate: '2024-01-18',
        lastActive: '2024-01-20',
        toolsSubmitted: 1,
        revenue: 0
      }
    ];

    setUsers(mockUsers);
    setFilteredUsers(mockUsers);
  }, []);

  useEffect(() => {
    let filtered = users;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(user => 
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by subscription status
    if (filterStatus !== 'all') {
      filtered = filtered.filter(user => user.subscriptionStatus === filterStatus);
    }

    setFilteredUsers(filtered);
  }, [searchTerm, filterStatus, users]);

  const handleUserAction = (userId: string, action: string) => {
    switch (action) {
      case 'view':
        alert(`View user details for ID: ${userId}`);
        break;
      case 'suspend':
        if (confirm('Are you sure you want to suspend this user?')) {
          alert(`User ${userId} suspended`);
        }
        break;
      case 'delete':
        if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
          alert(`User ${userId} deleted`);
        }
        break;
      case 'promote':
        if (confirm('Promote this user to admin?')) {
          alert(`User ${userId} promoted to admin`);
        }
        break;
      default:
        break;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Active</span>;
      case 'trial':
        return <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">Trial</span>;
      case 'cancelled':
        return <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">Cancelled</span>;
      case 'suspended':
        return <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Suspended</span>;
      default:
        return <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">Unknown</span>;
    }
  };

  const totalRevenue = users.reduce((sum, user) => sum + user.revenue, 0);
  const activeSubscriptions = users.filter(user => user.subscriptionStatus === 'active').length;
  const trialUsers = users.filter(user => user.subscriptionStatus === 'trial').length;

  return (
    <AdminLayout>
      <div className="container mx-auto px-4 py-8">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-2">
            User Management
          </h1>
          <p className="text-lg text-muted-foreground">
            Manage users, subscriptions, and permissions
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="p-6 text-center">
            <div className="text-2xl font-bold text-blue-600 mb-1">{users.length}</div>
            <div className="text-sm text-muted-foreground">Total Users</div>
          </Card>
          <Card className="p-6 text-center">
            <div className="text-2xl font-bold text-green-600 mb-1">{activeSubscriptions}</div>
            <div className="text-sm text-muted-foreground">Active Subscriptions</div>
          </Card>
          <Card className="p-6 text-center">
            <div className="text-2xl font-bold text-purple-600 mb-1">{trialUsers}</div>
            <div className="text-sm text-muted-foreground">Trial Users</div>
          </Card>
          <Card className="p-6 text-center">
            <div className="text-2xl font-bold text-orange-600 mb-1">£{totalRevenue}</div>
            <div className="text-sm text-muted-foreground">Total Revenue</div>
          </Card>
        </div>

        {/* Filters */}
        <Card className="p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search users by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={filterStatus === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterStatus('all')}
              >
                All
              </Button>
              <Button
                variant={filterStatus === 'active' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterStatus('active')}
              >
                Active
              </Button>
              <Button
                variant={filterStatus === 'trial' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterStatus('trial')}
              >
                Trial
              </Button>
              <Button
                variant={filterStatus === 'cancelled' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterStatus('cancelled')}
              >
                Cancelled
              </Button>
            </div>
          </div>
        </Card>

        {/* Users Table */}
        <Card className="p-6">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">User</th>
                  <th className="text-left py-3 px-4">Email</th>
                  <th className="text-left py-3 px-4">Role</th>
                  <th className="text-left py-3 px-4">Status</th>
                  <th className="text-left py-3 px-4">Join Date</th>
                  <th className="text-left py-3 px-4">Tools</th>
                  <th className="text-left py-3 px-4">Revenue</th>
                  <th className="text-left py-3 px-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.map((user) => (
                  <tr key={user._id} className="border-b hover:bg-muted/30">
                    <td className="py-3 px-4">
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-muted-foreground">
                          Last active: {new Date(user.lastActive).toLocaleDateString()}
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4">{user.email}</td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        user.role === 'admin' 
                          ? 'bg-purple-100 text-purple-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {user.role}
                      </span>
                    </td>
                    <td className="py-3 px-4">{getStatusBadge(user.subscriptionStatus)}</td>
                    <td className="py-3 px-4">{new Date(user.joinDate).toLocaleDateString()}</td>
                    <td className="py-3 px-4">{user.toolsSubmitted}</td>
                    <td className="py-3 px-4">£{user.revenue}</td>
                    <td className="py-3 px-4">
                      <div className="flex gap-2">
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleUserAction(user._id, 'view')}
                        >
                          View
                        </Button>
                        {user.role !== 'admin' && (
                          <>
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => handleUserAction(user._id, 'promote')}
                            >
                              Promote
                            </Button>
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => handleUserAction(user._id, 'suspend')}
                              className="text-yellow-600"
                            >
                              Suspend
                            </Button>
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => handleUserAction(user._id, 'delete')}
                              className="text-red-600"
                            >
                              Delete
                            </Button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default AdminUsersPage;
