# AI Tools Directory - Environment Variables Example
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Application environment
NODE_ENV=development

# Application URL (used for callbacks and links)
NEXT_PUBLIC_APP_URL=http://localhost:3000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# MongoDB connection string
# For local development:
MONGODB_URI=mongodb://localhost:27017/aitools

# For MongoDB Atlas (production):
# MONGODB_URI=mongodb+srv://username:<EMAIL>/aitools?retryWrites=true&w=majority

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================

# JWT secret key (must be at least 32 characters)
# Generate a secure key with: openssl rand -base64 32
JWT_SECRET=your-super-secret-jwt-key-must-be-at-least-32-characters-long

# NextAuth configuration (if using NextAuth)
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key-here

# Session secret (for additional security)
SESSION_SECRET=another-secret-key-for-sessions-32-chars-min

# =============================================================================
# PAYMENT INTEGRATION (Required for subscription system)
# =============================================================================

# Stripe configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# PayPal configuration (alternative)
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret

# =============================================================================
# EMAIL SERVICES (Required for notifications)
# =============================================================================

# SendGrid for transactional emails
SENDGRID_API_KEY=SG.your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>

# Alternative: SMTP configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-smtp-password
SMTP_FROM=<EMAIL>

# =============================================================================
# SOCIAL AUTHENTICATION (Optional)
# =============================================================================

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# GitHub OAuth
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# =============================================================================
# AI & EXTERNAL APIS (Optional for enhanced features)
# =============================================================================

# OpenAI API for enhanced categorization and features
OPENAI_API_KEY=sk-your_openai_api_key

# Google Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# =============================================================================
# ADMIN FEATURES & SECURITY
# =============================================================================

# Admin panel access control
ADMIN_SECRET_KEY=admin-secret-for-additional-security

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# MONITORING & ANALYTICS (Optional)
# =============================================================================

# Sentry for error tracking
SENTRY_DSN=https://<EMAIL>/project_id

# LogRocket for session recording
LOGROCKET_APP_ID=your_logrocket_app_id

# =============================================================================
# PERFORMANCE & CACHING (Optional)
# =============================================================================

# Redis for caching (if implemented)
REDIS_URL=redis://localhost:6379

# Elasticsearch for advanced search (if implemented)
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD=your_password

# =============================================================================
# FILE STORAGE (If implementing file uploads)
# =============================================================================

# AWS S3 configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-bucket-name

# Cloudinary for image management
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable development features
NEXT_PUBLIC_ENABLE_DEVTOOLS=true

# Database seeding
ENABLE_AUTO_SEED=false

# Logging level
LOG_LEVEL=info

# =============================================================================
# WEBHOOKS & INTEGRATIONS (Optional)
# =============================================================================

# Webhook secret for external integrations
WEBHOOK_SECRET=your_webhook_secret

# Slack integration for admin notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url

# =============================================================================
# SECURITY HEADERS & CORS
# =============================================================================

# Allowed origins for CORS
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com

# Content Security Policy
CSP_REPORT_URI=https://yourdomain.com/api/csp-report

# =============================================================================
# ANTI-SCRAPING PROTECTION (Optional)
# =============================================================================

# Enable strict anti-scraping protection in production
# Set to 'true' only if you need aggressive bot protection
# Leave as 'false' for normal operation with legitimate users
ANTI_SCRAPING_ENABLED=false
NEXT_PUBLIC_ANTI_SCRAPING_ENABLED=false

# Rate limiting settings (requests per minute)
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=60000