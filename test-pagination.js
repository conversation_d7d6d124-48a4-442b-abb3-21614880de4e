#!/usr/bin/env node

// Test script to verify pagination functionality for the Other category
const testPages = [1, 2, 10, 50, 81]; // Test various pages including first, middle, and last
const baseUrl = 'http://localhost:3002/api/tools?category=Other&limit=12';

async function testPagination() {
  console.log('🔍 Testing pagination for Other category...\n');
  
  for (const page of testPages) {
    try {
      const url = `${baseUrl}&page=${page}`;
      const response = await fetch(url);
      const data = await response.json();
      
      console.log(`Page ${page}:`);
      console.log(`  ✅ Total tools: ${data.total}`);
      console.log(`  ✅ Current page: ${data.page}`);
      console.log(`  ✅ Total pages: ${data.totalPages}`);
      console.log(`  ✅ Tools returned: ${data.tools?.length || 0}`);
      
      // Validate data
      if (data.total !== 971) {
        console.log(`  ❌ ERROR: Expected total 971, got ${data.total}`);
      }
      if (data.page !== page) {
        console.log(`  ❌ ERROR: Expected page ${page}, got ${data.page}`);
      }
      if (data.totalPages !== 81) {
        console.log(`  ❌ ERROR: Expected totalPages 81, got ${data.totalPages}`);
      }
      
      // Check tools count for last page
      if (page === 81 && data.tools?.length !== 11) {
        console.log(`  ❌ ERROR: Last page should have 11 tools, got ${data.tools?.length}`);
      } else if (page !== 81 && data.tools?.length !== 12) {
        console.log(`  ❌ ERROR: Page should have 12 tools, got ${data.tools?.length}`);
      }
      
      console.log('');
    } catch (error) {
      console.log(`  ❌ ERROR testing page ${page}: ${error.message}\n`);
    }
  }
  
  console.log('✅ Pagination test completed!');
}

testPagination();
