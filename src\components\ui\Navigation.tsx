'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from './Button';
import { useSession } from 'next-auth/react';

const Navigation: React.FC = () => {
  const pathname = usePathname();
  const { data: session, status } = useSession();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [user, setUser] = useState<any>(null);

  // Check authentication state
  useEffect(() => {
    const checkAuthState = () => {
      const token = localStorage.getItem('token');
      const userData = localStorage.getItem('user');
      setIsLoggedIn(!!token && !!userData);
      setUser(userData ? JSON.parse(userData) : null);
    };

    checkAuthState();
    // Listen for storage changes to update auth state across tabs
    window.addEventListener('storage', checkAuthState);
    // Listen for custom auth events
    window.addEventListener('authStateChanged', checkAuthState);

    return () => {
      window.removeEventListener('storage', checkAuthState);
      window.removeEventListener('authStateChanged', checkAuthState);
    };
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setIsLoggedIn(false);
    setUser(null);
    // Dispatch custom event to notify other components
    window.dispatchEvent(new Event('authStateChanged'));
    closeMenu();
  };

  const isActive = (path: string) => {
    return pathname === path || pathname?.startsWith(`${path}/`);
  };

  return (
    <header className="bg-background border-b sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <Link href="/" className="font-bold text-xl">
            AI Tools Directory
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-6">
            {/* Only show Tools and Categories if paid user */}
            {session && (session.user && (session.user as any).isPaid) ? (
              <>
                <Link 
                  href="/tools" 
                  className={`text-sm font-medium transition-colors hover:text-primary ${
                    isActive('/tools') ? 'text-primary' : 'text-foreground'
                  }`}
                >
                  Tools
                </Link>
                <Link 
                  href="/categories" 
                  className={`text-sm font-medium transition-colors hover:text-primary ${
                    isActive('/categories') ? 'text-primary' : 'text-foreground'
                  }`}
                >
                  Categories
                </Link>
              </>
            ) : null}
            <Link 
              href="/pricing" 
              className={`text-sm font-medium transition-colors hover:text-primary ${
                isActive('/pricing') ? 'text-primary' : 'text-foreground'
              }`}
            >
              Pricing
            </Link>
            <Link 
              href="/about" 
              className={`text-sm font-medium transition-colors hover:text-primary ${
                isActive('/about') ? 'text-primary' : 'text-foreground'
              }`}
            >
              About
            </Link>
          </nav>

          <div className="hidden md:flex items-center space-x-4">
            {isLoggedIn ? (
              <>
                <Link href="/dashboard">
                  <Button variant="ghost" size="sm">Dashboard</Button>
                </Link>
                <Link href="/submit-tool">
                  <Button size="sm">Submit Tool</Button>
                </Link>
                {user?.role === 'admin' && (
                  <Link href="/admin">
                    <Button variant="outline" size="sm">Admin</Button>
                  </Link>
                )}
                <Button variant="outline" size="sm" onClick={handleLogout}>
                  Log Out
                </Button>
              </>
            ) : (
              <>
                <Link href="/login">
                  <Button variant="ghost" size="sm">Log In</Button>
                </Link>
                <Link href="/register">
                  <Button size="sm">Sign Up</Button>
                </Link>
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button 
            className="md:hidden p-2 rounded-md hover:bg-muted"
            onClick={toggleMenu}
            aria-label="Toggle menu"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="24" 
              height="24" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round"
            >
              {isMenuOpen ? (
                <>
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </>
              ) : (
                <>
                  <line x1="3" y1="12" x2="21" y2="12"></line>
                  <line x1="3" y1="6" x2="21" y2="6"></line>
                  <line x1="3" y1="18" x2="21" y2="18"></line>
                </>
              )}
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-background border-t">
          <div className="container mx-auto px-4 py-4 space-y-4">
            <nav className="flex flex-col space-y-4">
              <Link 
                href="/tools" 
                className={`text-sm font-medium transition-colors hover:text-primary ${
                  isActive('/tools') ? 'text-primary' : 'text-foreground'
                }`}
                onClick={closeMenu}
              >
                Tools
              </Link>
              <Link 
                href="/categories" 
                className={`text-sm font-medium transition-colors hover:text-primary ${
                  isActive('/categories') ? 'text-primary' : 'text-foreground'
                }`}
                onClick={closeMenu}
              >
                Categories
              </Link>
              <Link 
                href="/pricing" 
                className={`text-sm font-medium transition-colors hover:text-primary ${
                  isActive('/pricing') ? 'text-primary' : 'text-foreground'
                }`}
                onClick={closeMenu}
              >
                Pricing
              </Link>
              <Link 
                href="/about" 
                className={`text-sm font-medium transition-colors hover:text-primary ${
                  isActive('/about') ? 'text-primary' : 'text-foreground'
                }`}
                onClick={closeMenu}
              >
                About
              </Link>
            </nav>
            <div className="flex flex-col space-y-2 pt-4 border-t">
              {isLoggedIn ? (
                <>
                  <Link href="/dashboard" onClick={closeMenu}>
                    <Button variant="ghost" className="w-full justify-center">Dashboard</Button>
                  </Link>
                  <Link href="/submit-tool" onClick={closeMenu}>
                    <Button className="w-full justify-center">Submit Tool</Button>
                  </Link>
                  {user?.role === 'admin' && (
                    <Link href="/admin" onClick={closeMenu}>
                      <Button variant="outline" className="w-full justify-center">Admin</Button>
                    </Link>
                  )}
                  <Button variant="outline" className="w-full justify-center" onClick={handleLogout}>
                    Log Out
                  </Button>
                </>
              ) : (
                <>
                  <Link href="/login" onClick={closeMenu}>
                    <Button variant="ghost" className="w-full justify-center">Log In</Button>
                  </Link>
                  <Link href="/register" onClick={closeMenu}>
                    <Button className="w-full justify-center">Sign Up</Button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Navigation;