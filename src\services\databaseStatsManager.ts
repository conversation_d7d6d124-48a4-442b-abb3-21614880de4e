import dbConnect from '../lib/db';
import Tool from '../models/Tool';
import Category from '../models/Category';

interface DatabaseStats {
  totalTools: number;
  activeTools: number;
  totalCategories: number;
  categoriesWithTools: number;
  categoryStats: Array<{
    _id: string;
    name: string;
    slug: string;
    toolCount: number;
  }>;
  recentlyAdded: number;
  sourceStats: Array<{
    source: string;
    count: number;
  }>;
}

export class DatabaseStatsManager {
  
  /**
   * Get comprehensive database statistics
   */
  async getFullStats(): Promise<DatabaseStats> {
    await dbConnect();

    // Get total tool count
    const totalTools = await Tool.countDocuments();
    
    // Get active tool count
    const activeTools = await Tool.countDocuments({ status: 'active' });

    // Get total categories
    const totalCategories = await Category.countDocuments();

    // Get categories with tool counts
    const categoryStats = await Category.aggregate([
      {
        $lookup: {
          from: 'tools',
          localField: '_id',
          foreignField: 'category',
          as: 'tools'
        }
      },
      {
        $project: {
          name: 1,
          slug: 1,
          toolCount: { $size: '$tools' }
        }
      },
      {
        $sort: { toolCount: -1 }
      }
    ]);

    const categoriesWithTools = categoryStats.filter(cat => cat.toolCount > 0).length;

    // Get recently added tools (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const recentlyAdded = await Tool.countDocuments({
      createdAt: { $gte: sevenDaysAgo }
    });

    // Get source statistics
    const sourceStats = await Tool.aggregate([
      {
        $group: {
          _id: '$source',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          source: '$_id',
          count: 1,
          _id: 0
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    return {
      totalTools,
      activeTools,
      totalCategories,
      categoriesWithTools,
      categoryStats,
      recentlyAdded,
      sourceStats
    };
  }

  /**
   * Update all category tool counts
   */
  async updateCategoryCounts(): Promise<void> {
    await dbConnect();

    console.log('🔄 Updating category tool counts...');

    const categories = await Category.find({});
    
    for (const category of categories) {
      const toolCount = await Tool.countDocuments({ category: category._id });
      
      await Category.findByIdAndUpdate(category._id, {
        $set: { toolCount }
      });
    }

    console.log(`✅ Updated tool counts for ${categories.length} categories`);
  }

  /**
   * Clean up empty categories and invalid references
   */
  async cleanupDatabase(): Promise<{ removedTools: number; updatedCategories: number }> {
    await dbConnect();

    console.log('🧹 Cleaning up database...');

    // Remove tools with invalid category references
    const validCategoryIds = (await Category.find({}, '_id')).map(c => c._id);
    const invalidToolsResult = await Tool.deleteMany({
      category: { $nin: validCategoryIds }
    });

    // Fix tools that have string category references instead of ObjectIds
    const toolsWithStringCategories = await Tool.find({ 
      category: { $type: 'string' } 
    });

    for (const tool of toolsWithStringCategories) {
      const category = await Category.findOne({
        $or: [
          { slug: tool.category },
          { name: { $regex: new RegExp(`^${tool.category}$`, 'i') } }
        ]
      });

      if (category) {
        await Tool.findByIdAndUpdate(tool._id, {
          category: category._id
        });
      }
    }

    await this.updateCategoryCounts();

    return {
      removedTools: invalidToolsResult.deletedCount,
      updatedCategories: validCategoryIds.length
    };
  }

  /**
   * Generate a comprehensive report
   */
  async generateReport(): Promise<string> {
    const stats = await this.getFullStats();
    
    let report = `📊 DATABASE STATUS REPORT\n`;
    report += `${'='.repeat(50)}\n\n`;
    
    report += `📈 OVERVIEW STATISTICS:\n`;
    report += `   Total Tools: ${stats.totalTools}\n`;
    report += `   Active Tools: ${stats.activeTools}\n`;
    report += `   Total Categories: ${stats.totalCategories}\n`;
    report += `   Categories with Tools: ${stats.categoriesWithTools}\n`;
    report += `   Recently Added (7 days): ${stats.recentlyAdded}\n\n`;

    report += `📁 CATEGORY BREAKDOWN:\n`;
    stats.categoryStats.forEach(cat => {
      const percentage = stats.totalTools > 0 ? (cat.toolCount / stats.totalTools * 100).toFixed(1) : '0';
      report += `   ${cat.name}: ${cat.toolCount} tools (${percentage}%)\n`;
    });

    if (stats.sourceStats.length > 0) {
      report += `\n📥 DATA SOURCES:\n`;
      stats.sourceStats.forEach(source => {
        if (source.source) {
          const percentage = (source.count / stats.totalTools * 100).toFixed(1);
          report += `   ${source.source}: ${source.count} tools (${percentage}%)\n`;
        }
      });
    }

    report += `\n💡 INSIGHTS:\n`;
    
    // Generate insights
    const topCategory = stats.categoryStats[0];
    if (topCategory) {
      report += `   • Most popular category: ${topCategory.name} (${topCategory.toolCount} tools)\n`;
    }

    const emptyCategories = stats.totalCategories - stats.categoriesWithTools;
    if (emptyCategories > 0) {
      report += `   • ${emptyCategories} categories need more tools\n`;
    }

    if (stats.recentlyAdded > 0) {
      report += `   • Database growing actively with ${stats.recentlyAdded} recent additions\n`;
    }

    const avgToolsPerCategory = (stats.totalTools / stats.categoriesWithTools).toFixed(1);
    report += `   • Average tools per active category: ${avgToolsPerCategory}\n`;

    return report;
  }

  /**
   * Auto-maintenance routine to run after scraping
   */
  async runMaintenanceAfterScraping(): Promise<void> {
    console.log('🔧 Running post-scraping maintenance...');
    
    try {
      // Clean up database
      const cleanupResult = await this.cleanupDatabase();
      console.log(`🗑️ Cleanup: Removed ${cleanupResult.removedTools} invalid tools`);
      
      // Update all counts
      await this.updateCategoryCounts();
      
      // Generate and log report
      const report = await this.generateReport();
      console.log('\n' + report);
      
      console.log('✅ Maintenance completed successfully');
      
    } catch (error) {
      console.error('❌ Maintenance failed:', error);
    }
  }
}

export default DatabaseStatsManager;
