import cron, { ScheduledTask } from 'node-cron';
import { runAPICollector } from './apiToolCollector';
import { runToolsScraper } from './toolsScraper';
import dbConnect from '../lib/db';
import Tool from '../models/Tool';

interface SchedulerConfig {
  apiCollection: {
    enabled: boolean;
    schedule: string; // cron expression
  };
  webScraping: {
    enabled: boolean;
    schedule: string;
  };
  cleanup: {
    enabled: boolean;
    schedule: string;
  };
}

const DEFAULT_CONFIG: SchedulerConfig = {
  apiCollection: {
    enabled: true,
    schedule: '0 2 * * *', // Daily at 2 AM
  },
  webScraping: {
    enabled: true,
    schedule: '0 4 * * 0', // Weekly on Sunday at 4 AM
  },
  cleanup: {
    enabled: true,
    schedule: '0 3 * * 0', // Weekly on Sunday at 3 AM
  }
};

class DataCollectionScheduler {
  private config: SchedulerConfig;
  private tasks: ScheduledTask[] = [];

  constructor(config: SchedulerConfig = DEFAULT_CONFIG) {
    this.config = config;
  }

  private async logActivity(action: string, result: string) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${action}: ${result}`);
    
    // You could also log to a database or file here
    // await ActivityLog.create({ timestamp, action, result });
  }

  private async cleanupOldData() {
    try {
      await dbConnect();
      
      // Remove tools that haven't been updated in 6 months and have 0 ratings
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      
      const result = await Tool.deleteMany({
        updatedAt: { $lt: sixMonthsAgo },
        ratingCount: 0,
        status: 'inactive'
      });
      
      await this.logActivity('CLEANUP', `Removed ${result.deletedCount} stale tools`);
      
    } catch (error) {
      await this.logActivity('CLEANUP_ERROR', `Failed: ${error}`);
    }
  }

  private async runScheduledAPICollection() {
    try {
      await this.logActivity('API_COLLECTION', 'Starting scheduled API collection');
      await runAPICollector();
      await this.logActivity('API_COLLECTION', 'Completed successfully');
    } catch (error) {
      await this.logActivity('API_COLLECTION_ERROR', `Failed: ${error}`);
    }
  }

  private async runScheduledWebScraping() {
    try {
      await this.logActivity('WEB_SCRAPING', 'Starting scheduled web scraping');
      await runToolsScraper();
      await this.logActivity('WEB_SCRAPING', 'Completed successfully');
    } catch (error) {
      await this.logActivity('WEB_SCRAPING_ERROR', `Failed: ${error}`);
    }
  }

  start() {
    console.log('🕒 Starting Data Collection Scheduler...');
    
    // Schedule API collection
    if (this.config.apiCollection.enabled) {
      const apiTask = cron.schedule(
        this.config.apiCollection.schedule,
        () => this.runScheduledAPICollection()
      );
      this.tasks.push(apiTask);
      apiTask.start();
      console.log(`✅ API Collection scheduled: ${this.config.apiCollection.schedule}`);
    }

    // Schedule web scraping
    if (this.config.webScraping.enabled) {
      const scrapingTask = cron.schedule(
        this.config.webScraping.schedule,
        () => this.runScheduledWebScraping()
      );
      this.tasks.push(scrapingTask);
      scrapingTask.start();
      console.log(`✅ Web Scraping scheduled: ${this.config.webScraping.schedule}`);
    }

    // Schedule cleanup
    if (this.config.cleanup.enabled) {
      const cleanupTask = cron.schedule(
        this.config.cleanup.schedule,
        () => this.cleanupOldData()
      );
      this.tasks.push(cleanupTask);
      cleanupTask.start();
      console.log(`✅ Cleanup scheduled: ${this.config.cleanup.schedule}`);
    }

    console.log('🚀 Scheduler is running. All scheduled tasks are active.');
  }

  stop() {
    this.tasks.forEach(task => task.stop());
    this.tasks = [];
    console.log('⏹️ Scheduler stopped. All tasks cancelled.');
  }

  async runManualCollection() {
    console.log('🔄 Running manual collection...');
    await this.runScheduledAPICollection();
    await this.runScheduledWebScraping();
    console.log('✅ Manual collection completed');
  }

  getStatus() {
    return {
      isRunning: this.tasks.length > 0,
      tasksCount: this.tasks.length,
      config: this.config
    };
  }
}

// Export for use in other parts of the application
export { DataCollectionScheduler, DEFAULT_CONFIG };

// Main function for running as a standalone process
export async function startScheduler(config?: SchedulerConfig) {
  const scheduler = new DataCollectionScheduler(config);
  
  scheduler.start();
  
  // Graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Received SIGINT, shutting down scheduler...');
    scheduler.stop();
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    console.log('\n🛑 Received SIGTERM, shutting down scheduler...');
    scheduler.stop();
    process.exit(0);
  });
  
  // Keep the process alive
  console.log('📡 Scheduler is running. Press Ctrl+C to stop.');
  process.stdin.resume();
}
