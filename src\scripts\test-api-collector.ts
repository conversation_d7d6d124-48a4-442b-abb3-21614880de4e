import { APIToolCollector } from '../services/apiToolCollector';

async function testAPICollector() {
  console.log('🧪 Testing API Tool Collector (Hugging Face only)...');
  
  const collector = new APIToolCollector();
  
  try {
    await collector.initialize();
    console.log('✅ Initialized successfully');
    
    // Test only Hugging Face collection for now (no API keys needed)
    const stats = await collector.collectFromHuggingFace();
    
    console.log('\n📊 Test Results:');
    console.log(`✅ New tools added: ${stats.success}`);
    console.log(`⚪ Tools skipped: ${stats.skipped}`);
    console.log(`❌ Errors: ${stats.errors}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testAPICollector();
