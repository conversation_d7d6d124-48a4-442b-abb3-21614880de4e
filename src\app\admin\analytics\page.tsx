'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AdminLayout from '@/components/admin/AdminLayout';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

const AdminAnalyticsPage: React.FC = () => {
  const [analytics, setAnalytics] = useState({
    revenue: {
      thisMonth: 4460,
      lastMonth: 4125,
      growth: 8.1,
      totalRevenue: 52340,
      averageRevenuePerUser: 42.50
    },
    traffic: {
      totalViews: 87432,
      monthlyViews: 12847,
      dailyAverage: 415,
      bounceRate: 32.4,
      averageSessionDuration: '3:42'
    },
    subscriptions: {
      newSubscriptions: 47,
      cancellations: 12,
      netGrowth: 35,
      churnRate: 3.2,
      conversionRate: 12.8
    },
    tools: {
      totalSubmissions: 89,
      approvedThisMonth: 67,
      pendingReview: 23,
      rejectedThisMonth: 12,
      averageApprovalTime: '2.3 days'
    }
  });

  useEffect(() => {
    // In a real app, fetch analytics data from API
  }, []);

  const topTools = [
    { name: 'ChatGPT Alternative', views: 4521, clicks: 892, revenue: 180 },
    { name: 'AI Image Generator Pro', views: 3847, clicks: 723, revenue: 145 },
    { name: 'Code Assistant AI', views: 3214, clicks: 651, revenue: 130 },
    { name: 'Content Creator Suite', views: 2987, clicks: 567, revenue: 115 },
    { name: 'Data Analysis Bot', views: 2456, clicks: 434, revenue: 87 }
  ];

  const recentTransactions = [
    { id: 'TXN001', user: '<EMAIL>', amount: 5, date: '2024-01-20', type: 'Subscription' },
    { id: 'TXN002', user: '<EMAIL>', amount: 5, date: '2024-01-20', type: 'Renewal' },
    { id: 'TXN003', user: '<EMAIL>', amount: -5, date: '2024-01-19', type: 'Refund' },
    { id: 'TXN004', user: '<EMAIL>', amount: 5, date: '2024-01-19', type: 'Trial Conversion' },
    { id: 'TXN005', user: '<EMAIL>', amount: 5, date: '2024-01-18', type: 'Subscription' }
  ];

  const handleExport = (type: string) => {
    alert(`Exporting ${type} data... This would generate a CSV/PDF file in a real application.`);
  };

  return (
    <AdminLayout>
      <div className="container mx-auto px-4 py-8">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-2">
            Analytics & Revenue
          </h1>
          <p className="text-lg text-muted-foreground">
            Detailed insights into platform performance and financial metrics
          </p>
        </div>

        {/* Revenue Metrics */}
        <div className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">Revenue Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            <Card className="p-6 text-center bg-green-50 border-green-200">
              <div className="text-3xl font-bold text-green-600 mb-2">£{analytics.revenue.thisMonth.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">This Month</div>
              <div className="text-xs text-green-600 mt-1">+{analytics.revenue.growth}% vs last month</div>
            </Card>
            
            <Card className="p-6 text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">£{analytics.revenue.totalRevenue.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">Total Revenue</div>
              <div className="text-xs text-muted-foreground mt-1">All time</div>
            </Card>
            
            <Card className="p-6 text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">£{analytics.revenue.averageRevenuePerUser}</div>
              <div className="text-sm text-muted-foreground">ARPU</div>
              <div className="text-xs text-muted-foreground mt-1">Per user/month</div>
            </Card>
            
            <Card className="p-6 text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">{analytics.subscriptions.newSubscriptions}</div>
              <div className="text-sm text-muted-foreground">New Subscriptions</div>
              <div className="text-xs text-green-600 mt-1">This month</div>
            </Card>
            
            <Card className="p-6 text-center">
              <div className="text-3xl font-bold text-red-600 mb-2">{analytics.subscriptions.churnRate}%</div>
              <div className="text-sm text-muted-foreground">Churn Rate</div>
              <div className="text-xs text-muted-foreground mt-1">Monthly</div>
            </Card>
          </div>
        </div>

        {/* Traffic Metrics */}
        <div className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">Traffic & Engagement</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            <Card className="p-6 text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">{analytics.traffic.totalViews.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">Total Views</div>
              <div className="text-xs text-muted-foreground mt-1">All time</div>
            </Card>
            
            <Card className="p-6 text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">{analytics.traffic.monthlyViews.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">Monthly Views</div>
              <div className="text-xs text-green-600 mt-1">+15% vs last month</div>
            </Card>
            
            <Card className="p-6 text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">{analytics.traffic.dailyAverage}</div>
              <div className="text-sm text-muted-foreground">Daily Average</div>
              <div className="text-xs text-muted-foreground mt-1">Page views</div>
            </Card>
            
            <Card className="p-6 text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">{analytics.traffic.bounceRate}%</div>
              <div className="text-sm text-muted-foreground">Bounce Rate</div>
              <div className="text-xs text-green-600 mt-1">-2.1% improvement</div>
            </Card>
            
            <Card className="p-6 text-center">
              <div className="text-3xl font-bold text-indigo-600 mb-2">{analytics.traffic.averageSessionDuration}</div>
              <div className="text-sm text-muted-foreground">Avg Session</div>
              <div className="text-xs text-green-600 mt-1">+12% vs last month</div>
            </Card>
          </div>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card className="p-6">
            <h3 className="text-xl font-semibold mb-4">Revenue Trend (Last 6 Months)</h3>
            <div className="h-64 bg-muted/30 rounded-lg flex items-center justify-center">
              <p className="text-muted-foreground">Revenue chart visualization would go here</p>
            </div>
            <div className="mt-4 grid grid-cols-6 gap-2 text-sm text-center">
              <div>
                <div className="font-medium">£3,845</div>
                <div className="text-muted-foreground">Aug</div>
              </div>
              <div>
                <div className="font-medium">£3,920</div>
                <div className="text-muted-foreground">Sep</div>
              </div>
              <div>
                <div className="font-medium">£4,125</div>
                <div className="text-muted-foreground">Oct</div>
              </div>
              <div>
                <div className="font-medium">£4,280</div>
                <div className="text-muted-foreground">Nov</div>
              </div>
              <div>
                <div className="font-medium">£4,125</div>
                <div className="text-muted-foreground">Dec</div>
              </div>
              <div>
                <div className="font-medium text-green-600">£4,460</div>
                <div className="text-muted-foreground">Jan</div>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-xl font-semibold mb-4">User Growth</h3>
            <div className="h-64 bg-muted/30 rounded-lg flex items-center justify-center">
              <p className="text-muted-foreground">User growth chart would go here</p>
            </div>
            <div className="mt-4 grid grid-cols-3 gap-4 text-sm text-center">
              <div>
                <div className="font-medium text-green-600">+{analytics.subscriptions.newSubscriptions}</div>
                <div className="text-muted-foreground">New Users</div>
              </div>
              <div>
                <div className="font-medium text-red-600">-{analytics.subscriptions.cancellations}</div>
                <div className="text-muted-foreground">Cancellations</div>
              </div>
              <div>
                <div className="font-medium text-blue-600">+{analytics.subscriptions.netGrowth}</div>
                <div className="text-muted-foreground">Net Growth</div>
              </div>
            </div>
          </Card>
        </div>

        {/* Top Performing Tools */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card className="p-6">
            <h3 className="text-xl font-semibold mb-4">Top Performing Tools</h3>
            <div className="space-y-4">
              {topTools.map((tool, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                  <div>
                    <div className="font-medium">{tool.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {tool.views.toLocaleString()} views • {tool.clicks} clicks
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-green-600">£{tool.revenue}</div>
                    <div className="text-sm text-muted-foreground">
                      {((tool.clicks / tool.views) * 100).toFixed(1)}% CTR
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-xl font-semibold mb-4">Recent Transactions</h3>
            <div className="space-y-4">
              {recentTransactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                  <div>
                    <div className="font-medium">{transaction.type}</div>
                    <div className="text-sm text-muted-foreground">{transaction.user}</div>
                  </div>
                  <div className="text-right">
                    <div className={`font-semibold ${transaction.amount > 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {transaction.amount > 0 ? '+' : ''}£{Math.abs(transaction.amount)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {new Date(transaction.date).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>

        {/* Export Options */}
        <Card className="p-6 mb-8">
          <h3 className="text-xl font-semibold mb-4">Export Reports</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button onClick={() => handleExport('revenue')} className="w-full">
              Export Revenue Report
            </Button>
            <Button onClick={() => handleExport('users')} variant="outline" className="w-full">
              Export User Analytics
            </Button>
            <Button onClick={() => handleExport('tools')} variant="outline" className="w-full">
              Export Tool Performance
            </Button>
            <Button onClick={() => handleExport('financial')} variant="outline" className="w-full">
              Export Financial Summary
            </Button>
          </div>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default AdminAnalyticsPage;
