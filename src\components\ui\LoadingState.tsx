'use client'

import React from 'react';

interface LoadingStateProps {
  title?: string;
  message?: string;
  type?: 'tools' | 'categories' | 'general';
  count?: number;
}

export default function LoadingState({ 
  title = "Loading...",
  message,
  type = 'general',
  count = 6
}: LoadingStateProps) {
  const renderSkeletons = () => {
    if (type === 'tools') {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(count)].map((_, i) => (
            <div key={i} className="bg-card rounded-lg shadow-md p-4 h-64 animate-pulse">
              <div className="bg-muted h-32 rounded-md mb-4"></div>
              <div className="h-6 bg-muted rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-muted rounded w-1/2 mb-4"></div>
              <div className="h-4 bg-muted rounded w-full"></div>
            </div>
          ))}
        </div>
      );
    }

    if (type === 'categories') {
      return (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {[...Array(count)].map((_, i) => (
            <div key={i} className="bg-card rounded-lg shadow-md p-4 h-32 animate-pulse">
              <div className="h-6 bg-muted rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-muted rounded w-1/2"></div>
            </div>
          ))}
        </div>
      );
    }

    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-muted-foreground">{title}</p>
        {message && <p className="text-sm text-muted-foreground mt-2">{message}</p>}
      </div>
    );
  };

  return (
    <div className="py-12">
      <div className="container mx-auto px-4">
        {type !== 'general' && (
          <h2 className="text-2xl font-bold mb-6">{title}</h2>
        )}
        {renderSkeletons()}
      </div>
    </div>
  );
}
