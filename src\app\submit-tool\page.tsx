'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { canAccessPaidFeatures } from '@/lib/auth';
import UpgradePrompt from '@/components/ui/UpgradePrompt';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';

const SubmitToolPage: React.FC = () => {
  const router = useRouter();
  const { data: session, status } = useSession();

  const [formData, setFormData] = useState({
    toolName: '',
    description: '',
    website: '',
    category: '',
    pricing: 'free',
    features: '',
    useCases: '',
    logoUrl: '',
    screenshots: '',
    submitterName: '',
    submitterEmail: '',
    companyName: ''
  });
  
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Check authentication
  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/login?callbackUrl=/submit-tool');
      return;
    }
    if (!canAccessPaidFeatures(session)) {
      router.push('/dashboard');
      return;
    }
  }, [session, status, router]);

  // Show loading state while checking session
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Show upgrade prompt for non-paid users
  if (!session || !canAccessPaidFeatures(session)) {
    return <UpgradePrompt feature="tool submission" description="Upgrade to submit your own AI tools to our directory and reach thousands of users." />;
  }

  const categories = [
    'Content Creation',
    'Image Generation',
    'Video Editing',
    'Audio Processing',
    'Text Analysis',
    'Data Analysis',
    'Automation',
    'Customer Service',
    'Marketing',
    'Development Tools',
    'Design Tools',
    'Productivity',
    'Other'
  ];

  const pricingOptions = [
    { value: 'free', label: 'Free' },
    { value: 'freemium', label: 'Freemium' },
    { value: 'paid', label: 'Paid' },
    { value: 'subscription', label: 'Subscription' },
    { value: 'one-time', label: 'One-time Purchase' },
  ];

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.toolName.trim()) {
      newErrors.toolName = 'Tool name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.trim().length < 50) {
      newErrors.description = 'Description must be at least 50 characters';
    }

    if (!formData.website.trim()) {
      newErrors.website = 'Website URL is required';
    } else if (!/^https?:\/\/.+\..+/.test(formData.website)) {
      newErrors.website = 'Please enter a valid website URL';
    }

    if (!formData.category) {
      newErrors.category = 'Please select a category';
    }

    if (!formData.features.trim()) {
      newErrors.features = 'Key features are required';
    }

    if (!formData.submitterName.trim()) {
      newErrors.submitterName = 'Your name is required';
    }

    if (!formData.submitterEmail.trim()) {
      newErrors.submitterEmail = 'Your email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.submitterEmail)) {
      newErrors.submitterEmail = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    
    try {
      // Simulate submission processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('Tool submission:', formData);
      
      setShowSuccess(true);
      
      // Redirect after success
      setTimeout(() => {
        router.push('/dashboard');
      }, 3000);
      
    } catch (error) {
      console.error('Submission error:', error);
      setErrors({ submit: 'Submission failed. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  if (showSuccess) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="container mx-auto px-4">
          <div className="max-w-md mx-auto text-center">
            <Card className="p-8">
              <div className="w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-foreground mb-4">
                Tool Submitted Successfully!
              </h1>
              <p className="text-muted-foreground mb-6">
                Thank you for submitting <strong>{formData.toolName}</strong>. Our team will review your submission and get back to you within 24-48 hours.
              </p>
              <p className="text-sm text-muted-foreground">
                Redirecting to your dashboard...
              </p>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-3xl mx-auto">
          
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Submit Your AI Tool
            </h1>
            <p className="text-lg text-muted-foreground">
              Share your AI tool with our community of over 10,000 users
            </p>
          </div>

          {/* Benefits */}
          <Card className="p-6 mb-8 bg-gradient-to-r from-blue-50 to-purple-50">
            <h2 className="text-xl font-semibold text-foreground mb-4">Why Submit to AI Tools Directory?</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-500/10 rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-foreground">High Visibility</h3>
                <p className="text-sm text-muted-foreground">Reach thousands of AI enthusiasts and potential users</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-foreground">Fast Review</h3>
                <p className="text-sm text-muted-foreground">Priority review within 24-48 hours for subscribers</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-500/10 rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-foreground">Analytics</h3>
                <p className="text-sm text-muted-foreground">Track views, clicks, and user engagement</p>
              </div>
            </div>
          </Card>

          {/* Submission Form */}
          <Card className="p-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              
              {/* Tool Information */}
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-foreground">Tool Information</h2>
                
                <div>
                  <label htmlFor="toolName" className="block text-sm font-medium text-foreground mb-1">
                    Tool Name *
                  </label>
                  <Input
                    type="text"
                    id="toolName"
                    name="toolName"
                    value={formData.toolName}
                    onChange={handleChange}
                    placeholder="e.g., GPT-4 Content Creator"
                    className={errors.toolName ? 'border-red-500' : ''}
                  />
                  {errors.toolName && <p className="text-red-500 text-xs mt-1">{errors.toolName}</p>}
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-foreground mb-1">
                    Description * (minimum 50 characters)
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows={4}
                    value={formData.description}
                    onChange={handleChange}
                    placeholder="Provide a detailed description of your AI tool, what it does, and how it helps users..."
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${errors.description ? 'border-red-500' : 'border-input'}`}
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>{errors.description || ''}</span>
                    <span>{formData.description.length}/50+ characters</span>
                  </div>
                </div>

                <div>
                  <label htmlFor="website" className="block text-sm font-medium text-foreground mb-1">
                    Website URL *
                  </label>
                  <Input
                    type="url"
                    id="website"
                    name="website"
                    value={formData.website}
                    onChange={handleChange}
                    placeholder="https://yourtool.com"
                    className={errors.website ? 'border-red-500' : ''}
                  />
                  {errors.website && <p className="text-red-500 text-xs mt-1">{errors.website}</p>}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="category" className="block text-sm font-medium text-foreground mb-1">
                      Category *
                    </label>
                    <select
                      id="category"
                      name="category"
                      value={formData.category}
                      onChange={handleChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${errors.category ? 'border-red-500' : 'border-input'}`}
                    >
                      <option value="">Select a category</option>
                      {categories.map(cat => (
                        <option key={cat} value={cat}>{cat}</option>
                      ))}
                    </select>
                    {errors.category && <p className="text-red-500 text-xs mt-1">{errors.category}</p>}
                  </div>

                  <div>
                    <label htmlFor="pricing" className="block text-sm font-medium text-foreground mb-1">
                      Pricing Model
                    </label>
                    <select
                      id="pricing"
                      name="pricing"
                      value={formData.pricing}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      {pricingOptions.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label htmlFor="features" className="block text-sm font-medium text-foreground mb-1">
                    Key Features *
                  </label>
                  <textarea
                    id="features"
                    name="features"
                    rows={3}
                    value={formData.features}
                    onChange={handleChange}
                    placeholder="List the main features of your tool (one per line or comma-separated)"
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${errors.features ? 'border-red-500' : 'border-input'}`}
                  />
                  {errors.features && <p className="text-red-500 text-xs mt-1">{errors.features}</p>}
                </div>

                <div>
                  <label htmlFor="useCases" className="block text-sm font-medium text-foreground mb-1">
                    Use Cases
                  </label>
                  <textarea
                    id="useCases"
                    name="useCases"
                    rows={3}
                    value={formData.useCases}
                    onChange={handleChange}
                    placeholder="Describe common use cases or scenarios where your tool would be helpful"
                    className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>
              </div>

              {/* Media */}
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-foreground">Media (Optional)</h2>
                
                <div>
                  <label htmlFor="logoUrl" className="block text-sm font-medium text-foreground mb-1">
                    Logo URL
                  </label>
                  <Input
                    type="url"
                    id="logoUrl"
                    name="logoUrl"
                    value={formData.logoUrl}
                    onChange={handleChange}
                    placeholder="https://yourtool.com/logo.png"
                  />
                  <p className="text-xs text-muted-foreground mt-1">Recommended: 200x200px PNG or SVG</p>
                </div>

                <div>
                  <label htmlFor="screenshots" className="block text-sm font-medium text-foreground mb-1">
                    Screenshot URLs
                  </label>
                  <textarea
                    id="screenshots"
                    name="screenshots"
                    rows={2}
                    value={formData.screenshots}
                    onChange={handleChange}
                    placeholder="https://yourtool.com/screenshot1.png, https://yourtool.com/screenshot2.png"
                    className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                  <p className="text-xs text-muted-foreground mt-1">Comma-separated URLs. Recommended: 1200x800px</p>
                </div>
              </div>

              {/* Contact Information */}
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-foreground">Contact Information</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="submitterName" className="block text-sm font-medium text-foreground mb-1">
                      Your Name *
                    </label>
                    <Input
                      type="text"
                      id="submitterName"
                      name="submitterName"
                      value={formData.submitterName}
                      onChange={handleChange}
                      placeholder="John Doe"
                      className={errors.submitterName ? 'border-red-500' : ''}
                    />
                    {errors.submitterName && <p className="text-red-500 text-xs mt-1">{errors.submitterName}</p>}
                  </div>

                  <div>
                    <label htmlFor="submitterEmail" className="block text-sm font-medium text-foreground mb-1">
                      Your Email *
                    </label>
                    <Input
                      type="email"
                      id="submitterEmail"
                      name="submitterEmail"
                      value={formData.submitterEmail}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                      className={errors.submitterEmail ? 'border-red-500' : ''}
                    />
                    {errors.submitterEmail && <p className="text-red-500 text-xs mt-1">{errors.submitterEmail}</p>}
                  </div>
                </div>

                <div>
                  <label htmlFor="companyName" className="block text-sm font-medium text-foreground mb-1">
                    Company Name (Optional)
                  </label>
                  <Input
                    type="text"
                    id="companyName"
                    name="companyName"
                    value={formData.companyName}
                    onChange={handleChange}
                    placeholder="Your Company"
                  />
                </div>
              </div>

              {errors.submit && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-600 text-sm">{errors.submit}</p>
                </div>
              )}

              <div className="pt-6">
                <Button 
                  type="submit" 
                  className="w-full" 
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Submitting...
                    </span>
                  ) : (
                    'Submit Tool for Review'
                  )}
                </Button>

                <p className="text-xs text-muted-foreground text-center mt-4">
                  By submitting, you agree that the information provided is accurate and that you have the right to submit this tool. 
                  Our team will review your submission within 24-48 hours.
                </p>
              </div>
            </form>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SubmitToolPage;
