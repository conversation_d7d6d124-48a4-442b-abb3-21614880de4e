/**
 * Authentication Flow Test Script
 * 
 * This script helps test the authentication flow and paid user access control.
 * It simulates different user scenarios to verify that the authentication
 * and access control are working correctly.
 * 
 * Run this script with: node test-auth-flow.js
 */

const { chromium } = require('playwright');
const assert = require('assert');

// Configuration
const BASE_URL = 'http://localhost:3000';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'password123',
  name: 'Test User'
};
const ADMIN_USER = {
  email: '<EMAIL>',
  password: 'adminpass123',
  name: 'Admin User'
};

// Test scenarios
async function runTests() {
  console.log('Starting authentication flow tests...');
  const browser = await chromium.launch({ headless: false });
  
  try {
    // Test 1: Non-authenticated user redirected from protected pages
    console.log('\n🧪 Test 1: Non-authenticated user redirected from protected pages');
    await testNonAuthenticatedRedirects(browser);
    
    // Test 2: Free user redirected from paid-only pages
    console.log('\n🧪 Test 2: Free user redirected from paid-only pages');
    await testFreeUserRedirects(browser);
    
    // Test 3: Paid user can access all pages
    console.log('\n🧪 Test 3: Paid user can access all pages');
    await testPaidUserAccess(browser);
    
    // Test 4: Admin user can access all pages
    console.log('\n🧪 Test 4: Admin user can access all pages');
    await testAdminUserAccess(browser);
    
    console.log('\n✅ All tests completed successfully!');
  } catch (error) {
    console.error('\n❌ Test failed:', error);
  } finally {
    await browser.close();
  }
}

// Test 1: Non-authenticated user redirected from protected pages
async function testNonAuthenticatedRedirects(browser) {
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // Test protected pages
  const protectedPages = ['/dashboard', '/tools', '/categories', '/submit-tool'];
  
  for (const path of protectedPages) {
    console.log(`Testing redirect from ${path}`);
    await page.goto(`${BASE_URL}${path}`);
    await page.waitForURL(`${BASE_URL}/login**`);
    
    const url = page.url();
    assert(url.startsWith(`${BASE_URL}/login`), `Should redirect to login, but got: ${url}`);
    console.log(`✓ Redirected to login from ${path}`);
  }
  
  await context.close();
}

// Test 2: Free user redirected from paid-only pages
async function testFreeUserRedirects(browser) {
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // Login as free user
  await page.goto(`${BASE_URL}/login`);
  await page.fill('input[name="email"]', TEST_USER.email);
  await page.fill('input[name="password"]', TEST_USER.password);
  await page.click('button[type="submit"]');
  
  // Wait for login to complete
  await page.waitForURL(`${BASE_URL}/dashboard`);
  console.log('✓ Logged in as free user');
  
  // Test paid-only pages
  const paidOnlyPages = ['/tools', '/categories'];
  
  for (const path of paidOnlyPages) {
    console.log(`Testing redirect from ${path}`);
    await page.goto(`${BASE_URL}${path}`);
    
    // Should be redirected to dashboard or show upgrade prompt
    await page.waitForTimeout(1000);
    const url = page.url();
    const hasUpgradeText = await page.textContent('body').then(text => 
      text.includes('Upgrade Required') || text.includes('upgrade')
    );
    
    assert(
      url === `${BASE_URL}/dashboard` || hasUpgradeText,
      `Should redirect to dashboard or show upgrade prompt, but got: ${url}`
    );
    console.log(`✓ Properly handled paid-only page ${path}`);
  }
  
  await context.close();
}

// Test 3: Paid user can access all pages
async function testPaidUserAccess(browser) {
  // This would require a paid user in the database
  // For testing purposes, you might need to manually update a user in the database
  console.log('⚠️ This test requires a paid user in the database');
  console.log('⚠️ Skipping test - implement when you have a paid user available');
}

// Test 4: Admin user can access all pages
async function testAdminUserAccess(browser) {
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // Login as admin user
  await page.goto(`${BASE_URL}/login`);
  await page.fill('input[name="email"]', ADMIN_USER.email);
  await page.fill('input[name="password"]', ADMIN_USER.password);
  await page.click('button[type="submit"]');
  
  // Wait for login to complete
  await page.waitForURL(`${BASE_URL}/dashboard`);
  console.log('✓ Logged in as admin user');
  
  // Test all protected pages
  const protectedPages = ['/dashboard', '/tools', '/categories', '/submit-tool'];
  
  for (const path of protectedPages) {
    console.log(`Testing access to ${path}`);
    await page.goto(`${BASE_URL}${path}`);
    
    // Wait for page to load
    await page.waitForTimeout(1000);
    const url = page.url();
    
    assert(
      url === `${BASE_URL}${path}`,
      `Admin should access ${path}, but got redirected to: ${url}`
    );
    console.log(`✓ Admin can access ${path}`);
  }
  
  await context.close();
}

// Run the tests
runTests().catch(console.error);
