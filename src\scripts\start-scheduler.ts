import { startScheduler } from '../services/dataCollectionScheduler';

async function main() {
  console.log('🕒 Starting AI Tools Data Collection Scheduler');
  console.log('='.repeat(50));
  console.log('This will run automated data collection:');
  console.log('• API Collection: Daily at 2 AM');
  console.log('• Web Scraping: Weekly on Sunday at 4 AM');
  console.log('• Data Cleanup: Weekly on Sunday at 3 AM');
  console.log('='.repeat(50));
  
  try {
    await startScheduler();
  } catch (error) {
    console.error('❌ Scheduler failed:', error);
    process.exit(1);
  }
}

main();
