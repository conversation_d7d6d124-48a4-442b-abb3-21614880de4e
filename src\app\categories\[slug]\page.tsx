'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import ToolCard from '@/components/tools/ToolCard';
import { fetchTools } from '@/services/toolsService';
import { Tool } from '@/types/tool';

interface Category {
  _id: string;
  name: string;
  slug: string;
  description: string;
}

export default function CategoryPage() {
  const params = useParams();
  const slug = params.slug as string;
  
  const [category, setCategory] = useState<Category | null>(null);
  const [tools, setTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalTools, setTotalTools] = useState(0);

  useEffect(() => {
    const fetchCategoryAndTools = async () => {
      try {
        setLoading(true);
        
        // Fetch category details (only on first load)
        if (!category) {
          const categoryResponse = await fetch(`/api/categories/${slug}`);
          if (!categoryResponse.ok) {
            throw new Error('Category not found');
          }
          const categoryData = await categoryResponse.json();
          setCategory(categoryData);
        }
        
        // Fetch tools in this category for current page
        const toolsResponse = await fetchTools({
          category: category?.name || 'Other', // Use 'Other' as fallback during first load
          sortBy: 'rating',
          page: currentPage
        });
        
        console.log('Tools response:', toolsResponse);        
        setTools(toolsResponse.tools || []);
        setTotalPages(toolsResponse.totalPages || 0);
        setTotalTools(toolsResponse.total || 0);
      } catch (err) {
        setError('Failed to load category');
        console.error('Error loading category:', err);
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchCategoryAndTools();
    }
  }, [slug, currentPage, category?.name]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="mb-8">
          <div className="h-8 bg-muted rounded w-1/3 mb-4 animate-pulse"></div>
          <div className="h-4 bg-muted rounded w-2/3 mb-2 animate-pulse"></div>
          <div className="h-4 bg-muted rounded w-1/2 animate-pulse"></div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-card rounded-lg shadow-md p-4 h-64 animate-pulse">
              <div className="bg-muted h-32 rounded-md mb-4"></div>
              <div className="h-6 bg-muted rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-muted rounded w-1/2 mb-4"></div>
              <div className="h-4 bg-muted rounded w-full"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error || !category) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold mb-4">Category Not Found</h1>
          <p className="text-muted-foreground mb-6">
            The category you're looking for doesn't exist or could not be loaded.
          </p>
          <Link href="/categories">
            <Button>Browse All Categories</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-12">        <div className="mb-6">
          <Link href="/categories" className="text-muted-foreground hover:text-foreground mb-4 inline-block">
            ← Back to Categories
          </Link>
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold mb-4">{category.name}</h1>
              <p className="text-muted-foreground max-w-3xl">
                {category.description}
              </p>
            </div>
            {totalTools > 0 && (
              <div className="text-sm text-muted-foreground">
                Showing {((currentPage - 1) * 12) + 1}-{Math.min(currentPage * 12, totalTools)} of {totalTools} tools
              </div>
            )}
          </div>
        </div>
      
      {tools.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground mb-4">
            No tools found in this category yet.
          </p>
          <Link href="/tools">
            <Button>Browse All Tools</Button>
          </Link>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {tools.map((tool) => (
              <ToolCard 
                key={tool._id} 
                id={tool._id}
                name={tool.name}
                description={tool.description}
                imageUrl={tool.imageUrl || ''}
                rating={tool.rating || 0}
                category={tool.category}
                pricing={'Free'} // Default to Free if not specified
                url={''} // Default to empty string if not specified
              />
            ))}
          </div>
          
          {/* Pagination Controls */}
          {totalPages > 1 && (
            <div className="flex justify-center items-center space-x-2 mt-8">
              <Button
                variant="outline"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              
              <div className="flex items-center space-x-1">
                {/* First page */}
                {currentPage > 3 && (
                  <>
                    <Button
                      variant={currentPage === 1 ? "default" : "outline"}
                      onClick={() => setCurrentPage(1)}
                      className="w-10 h-10"
                    >
                      1
                    </Button>
                    {currentPage > 4 && <span className="px-2">...</span>}
                  </>
                )}
                
                {/* Pages around current page */}
                {Array.from(
                  { length: Math.min(5, totalPages) },
                  (_, i) => {
                    const pageNum = Math.max(1, Math.min(totalPages, currentPage - 2 + i));
                    if (pageNum < 1 || pageNum > totalPages) return null;
                    if (currentPage <= 3) {
                      const page = i + 1;
                      if (page > totalPages) return null;
                      return (
                        <Button
                          key={page}
                          variant={currentPage === page ? "default" : "outline"}
                          onClick={() => setCurrentPage(page)}
                          className="w-10 h-10"
                        >
                          {page}
                        </Button>
                      );
                    } else if (currentPage >= totalPages - 2) {
                      const page = totalPages - 4 + i;
                      if (page < 1 || page > totalPages) return null;
                      return (
                        <Button
                          key={page}
                          variant={currentPage === page ? "default" : "outline"}
                          onClick={() => setCurrentPage(page)}
                          className="w-10 h-10"
                        >
                          {page}
                        </Button>
                      );
                    } else {
                      return (
                        <Button
                          key={pageNum}
                          variant={currentPage === pageNum ? "default" : "outline"}
                          onClick={() => setCurrentPage(pageNum)}
                          className="w-10 h-10"
                        >
                          {pageNum}
                        </Button>
                      );
                    }
                  }
                ).filter(Boolean)}
                
                {/* Last page */}
                {currentPage < totalPages - 2 && (
                  <>
                    {currentPage < totalPages - 3 && <span className="px-2">...</span>}
                    <Button
                      variant={currentPage === totalPages ? "default" : "outline"}
                      onClick={() => setCurrentPage(totalPages)}
                      className="w-10 h-10"
                    >
                      {totalPages}
                    </Button>
                  </>
                )}
              </div>
              
              <Button
                variant="outline"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          )}
          
          {/* Page info */}
          {totalPages > 1 && (
            <div className="text-center mt-4 text-sm text-muted-foreground">
              Page {currentPage} of {totalPages}
            </div>
          )}
        </>
      )}
    </div>
  );
} 