# MongoDB Setup Guide for Development

## Option 1: MongoDB Atlas (Recommended - Easy Setup)

### Steps to set up MongoDB Atlas:

1. **Create a free MongoDB Atlas account:**
   - Go to https://cloud.mongodb.com/
   - Sign up for a free account
   - Choose "Build a Database" → "Free Shared Cluster"

2. **Create a cluster:**
   - Choose AWS, Google Cloud, or Azure (any is fine)
   - Select a region close to you
   - Leave other settings as default
   - Click "Create Cluster"

3. **Set up database access:**
   - Go to "Database Access" in the left sidebar
   - Click "Add New Database User"
   - Choose "Password" authentication
   - Create a username and strong password
   - Set privilege to "Read and write to any database"
   - Click "Add User"

4. **Set up network access:**
   - Go to "Network Access" in the left sidebar
   - Click "Add IP Address"
   - Click "Allow Access from Anywhere" (for development)
   - Click "Confirm"

5. **Get your connection string:**
   - Go back to "Database" in the left sidebar
   - Click "Connect" on your cluster
   - Choose "Connect your application"
   - Copy the connection string
   - It looks like: `mongodb+srv://<username>:<password>@cluster0.xxxxx.mongodb.net/?retryWrites=true&w=majority`

6. **Update your .env.local file:**
   - Replace `<username>` with your database username
   - Replace `<password>` with your database password
   - Add `/aitools` before the `?` to specify the database name
   - Final format: `mongodb+srv://username:<EMAIL>/aitools?retryWrites=true&w=majority`

### Example:
```env
MONGODB_URI=mongodb+srv://myuser:<EMAIL>/aitools?retryWrites=true&w=majority
```

## Option 2: Local MongoDB Installation

If you prefer to install MongoDB locally:

### For Windows:
1. Download MongoDB Community Server from https://www.mongodb.com/try/download/community
2. Run the installer and follow the setup wizard
3. Start MongoDB service:
   ```powershell
   net start MongoDB
   ```
4. Use this connection string in your .env.local:
   ```env
   MONGODB_URI=mongodb://localhost:27017/aitools
   ```

## Testing Your Connection

After setting up MongoDB (either Atlas or local), run this command to test:

```bash
npm run dev
```

If the connection works, your Next.js app should start without database errors.

## Troubleshooting

### Common Issues:
1. **Connection timeout** - Check your network access settings in Atlas
2. **Authentication failed** - Verify username/password in connection string
3. **Database not found** - MongoDB will create the database automatically when you first write data

### Need Help?
- MongoDB Atlas Documentation: https://docs.atlas.mongodb.com/
- MongoDB Connection String Format: https://docs.mongodb.com/manual/reference/connection-string/

## Next Steps

Once MongoDB is connected:
1. Your app will automatically create collections when needed
2. You can use MongoDB Compass (GUI) to view your data
3. Run your existing scripts to seed data:
   ```bash
   npm run seed-all
   ```
