'use client'

import React, { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import ToolsList from '@/components/tools/ToolsList';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';

export default function ToolsPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Get query parameters
  const query = searchParams.get('q') || '';
  const category = searchParams.get('category') || '';
  const sortBy = (searchParams.get('sort') as 'rating' | 'name') || 'rating';
  const page = parseInt(searchParams.get('page') || '1');
  
  // State for form inputs
  const [searchQuery, setSearchQuery] = useState(query);
  const [selectedCategory, setSelectedCategory] = useState(category);
  const [selectedSort, setSelectedSort] = useState(sortBy);
  
  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories');
        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }
        const data = await response.json();
        setCategories(data);
      } catch (err) {
        console.error('Error loading categories:', err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchCategories();
  }, []);
  
  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    const params = new URLSearchParams();
    
    if (searchQuery) params.set('q', searchQuery);
    if (selectedCategory) params.set('category', selectedCategory);
    if (selectedSort) params.set('sort', selectedSort);
    
    // Reset to page 1 when searching
    params.set('page', '1');
    
    router.push(`/tools?${params.toString()}`);
  };
  
  return (
    <div className="container mx-auto px-4 py-12">
      <h1 className="text-3xl font-bold mb-8">AI Tools Directory</h1>
      
      <div className="bg-card rounded-lg shadow-md p-6 mb-8">
        <form onSubmit={handleSearch} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="search" className="block text-sm font-medium mb-1">
                Search
              </label>
              <Input
                id="search"
                type="text"
                placeholder="Search tools..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            <div>
              <label htmlFor="category" className="block text-sm font-medium mb-1">
                Category
              </label>
              <Select
                id="category"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                <option value="">All Categories</option>
                {categories.map((cat) => (
                  <option key={cat._id} value={cat.name}>
                    {cat.name}
                  </option>
                ))}
              </Select>
            </div>
            
            <div>
              <label htmlFor="sort" className="block text-sm font-medium mb-1">
                Sort By
              </label>
              <Select
                id="sort"
                value={selectedSort}
                onChange={(e) => setSelectedSort(e.target.value as 'rating' | 'name')}
              >
                <option value="rating">Rating</option>
                <option value="name">Name</option>
              </Select>
            </div>
          </div>
          
          <div className="flex justify-end">
            <Button type="submit">Search</Button>
          </div>
        </form>
      </div>
      
      <ToolsList 
        initialQuery={query}
        initialCategory={category}
        initialSortBy={sortBy}
        initialPage={page}
      />
    </div>
  );
} 