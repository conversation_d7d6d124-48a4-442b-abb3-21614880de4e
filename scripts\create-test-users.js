/**
 * <PERSON><PERSON>t to create test users for authentication testing
 * 
 * Run with: node scripts/create-test-users.js
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/aitools';

// User schema (simplified version)
const UserSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: { type: String, enum: ['user', 'admin'], default: 'user' },
  isPaid: { type: Boolean, default: false },
  subscriptionStatus: { type: String, enum: ['active', 'inactive', 'cancelled', 'expired'], default: 'inactive' },
  subscriptionStartDate: { type: Date, default: null },
  subscriptionEndDate: { type: Date, default: null },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Hash password before saving
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) {
    next();
  }
  
  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
  this.updatedAt = new Date();
  next();
});

const User = mongoose.model('User', UserSchema);

// Test users to create
const testUsers = [
  {
    name: 'Free User',
    email: '<EMAIL>',
    password: 'password123',
    role: 'user',
    isPaid: false,
    subscriptionStatus: 'inactive'
  },
  {
    name: 'Paid User',
    email: '<EMAIL>',
    password: 'password123',
    role: 'user',
    isPaid: true,
    subscriptionStatus: 'active',
    subscriptionStartDate: new Date(),
    subscriptionEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
  },
  {
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'password123',
    role: 'admin',
    isPaid: false, // Admin doesn't need to be paid
    subscriptionStatus: 'inactive'
  }
];

async function createTestUsers() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    console.log('Creating test users...');
    
    for (const userData of testUsers) {
      try {
        // Check if user already exists
        const existingUser = await User.findOne({ email: userData.email });
        
        if (existingUser) {
          console.log(`User ${userData.email} already exists, updating...`);
          
          // Update existing user
          await User.updateOne(
            { email: userData.email },
            {
              $set: {
                name: userData.name,
                role: userData.role,
                isPaid: userData.isPaid,
                subscriptionStatus: userData.subscriptionStatus,
                subscriptionStartDate: userData.subscriptionStartDate,
                subscriptionEndDate: userData.subscriptionEndDate,
                updatedAt: new Date()
              }
            }
          );
          
          console.log(`✓ Updated user: ${userData.email} (${userData.role})`);
        } else {
          // Create new user
          const user = new User(userData);
          await user.save();
          console.log(`✓ Created user: ${userData.email} (${userData.role})`);
        }
      } catch (error) {
        console.error(`✗ Error creating user ${userData.email}:`, error.message);
      }
    }

    console.log('\nTest users summary:');
    console.log('==================');
    console.log('Free User:  <EMAIL>  / password123  (Cannot access paid features)');
    console.log('Paid User:  <EMAIL>  / password123  (Can access all features)');
    console.log('Admin User: <EMAIL> / password123  (Can access all features)');
    console.log('\nYou can now test the authentication flow with these users.');

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
createTestUsers();
