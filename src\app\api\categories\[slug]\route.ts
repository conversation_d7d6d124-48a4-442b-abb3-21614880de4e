import { NextResponse } from 'next/server'
import dbConnect from '@/lib/db'
import Category from '@/models/Category'
import Tool from '@/models/Tool'

interface RouteParams {
  params: {
    slug: string
  }
}

export async function GET(request: Request, { params }: RouteParams) {
  try {
    await dbConnect()
    
    const { slug } = params
    
    // Find category by slug
    const category = await Category.findOne({ slug }).lean()
    
    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      )
    }

    // Get tool count for this category
    const toolCount = await Tool.countDocuments({ category: (category as any)._id })
    
    // Return category with tool count
    const categoryWithCount = {
      ...category,
      toolCount
    }

    return NextResponse.json(categoryWithCount)
  } catch (error) {
    console.error('Error fetching category:', error)
    return NextResponse.json(
      { error: 'Failed to fetch category' },
      { status: 500 }
    )
  }
}
