'use client'

import React, { useState, useEffect } from 'react';
import <PERSON> from 'next/link';
import { Button } from '../ui/Button';
import ComingSoon from '../ui/ComingSoon';
import LoadingState from '../ui/LoadingState';
import { useClientToken, apiCall } from '@/hooks/useClientToken';

// Mock data for when database is unavailable
const mockCategories = [
  {
    _id: 'mock-1',
    name: 'AI Chatbots',
    slug: 'ai-chatbots',
    description: 'Conversational AI tools for customer service, content creation, and assistance',
    icon: '🤖'
  },
  {
    _id: 'mock-2',
    name: 'Image Generation',
    slug: 'image-generation',
    description: 'AI tools for creating and editing images, artwork, and visual content',
    icon: '🎨'
  },
  {
    _id: 'mock-3',
    name: 'Writing & Content',
    slug: 'writing-content',
    description: 'AI-powered writing assistants for content creation, editing, and enhancement',
    icon: '✍️'
  },
  {
    _id: 'mock-4',
    name: 'Productivity',
    slug: 'productivity',
    description: 'AI tools to boost efficiency, automate tasks, and streamline workflows',
    icon: '⚡'
  },
  {
    _id: 'mock-5',
    name: 'Code & Development',
    slug: 'code-development',
    description: 'AI coding assistants and development tools for programmers',
    icon: '💻'
  },
  {
    _id: 'mock-6',
    name: 'Audio & Speech',
    slug: 'audio-speech',
    description: 'Voice synthesis, transcription, and audio processing AI tools',
    icon: '🔊'
  },
  {
    _id: 'mock-7',
    name: 'Video Creation',
    slug: 'video-creation',
    description: 'AI-powered video editing, generation, and enhancement tools',
    icon: '🎬'
  },
  {
    _id: 'mock-8',
    name: 'Business & Marketing',
    slug: 'business-marketing',
    description: 'AI solutions for analytics, marketing, and business intelligence',
    icon: '📊'
  }
];

interface Category {
  _id: string;
  name: string;
  slug: string;
  description: string;
  icon?: string;
}

const PublicCategoriesSection: React.FC = () => {
  // Initialize with mock data to prevent flashing
  const [categories, setCategories] = useState<Category[]>(mockCategories);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [useMockData, setUseMockData] = useState(true);
  const { isReady: tokenReady } = useClientToken();

  useEffect(() => {
    // Don't show loading state initially since we already have mock data
    let isMounted = true;

    const fetchCategories = async () => {
      try {
        // Try to fetch from API first
        const response = await apiCall('/api/categories?limit=8');

        if (!isMounted) return;

        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }

        const data = await response.json();
        if (data && data.length > 0) {
          setCategories(data.slice(0, 8));
          setUseMockData(false);
        } else {
          // If no categories from API, keep using mock data
          setCategories(mockCategories);
          setUseMockData(true);
        }
      } catch (err) {
        console.error('Error loading categories:', err);
        // Keep using mock data when API fails
        setCategories(mockCategories);
        setUseMockData(true);
        setError('Showing sample categories - database temporarily unavailable');
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // Wait for client token to be ready (or immediately if anti-scraping is disabled)
    if (tokenReady) {
      fetchCategories();
    }

    return () => {
      isMounted = false;
    };
  }, [tokenReady]);

  // We don't need a loading state since we initialize with mock data
  // This prevents flashing

  return (
    <div className="py-12 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Browse by Category</h2>
          <Link href="/pricing">
            <Button variant="ghost">View All Categories</Button>
          </Link>
        </div>
        
        {useMockData && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-700">
              <span className="font-medium">Preview Mode:</span> These are sample categories to showcase our directory. 
              <Link href="/pricing" className="underline hover:no-underline ml-1">
                Subscribe to access all categories and tools
              </Link>
            </p>
          </div>
        )}
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {categories.map((category) => (
            <Link 
              href="/pricing" 
              key={category._id}
              className="bg-card rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow"
            >
              <div className="flex flex-col h-full">
                <div className="flex items-center mb-2">
                  {category.icon && (
                    <span className="text-xl mr-2">{category.icon}</span>
                  )}
                  <h3 className="text-lg font-medium">{category.name}</h3>
                </div>
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {category.description}
                </p>
              </div>
            </Link>
          ))}
        </div>
        
        <div className="text-center mt-8">
          <Link href="/pricing">
            <Button size="lg">
              Explore All Categories - Subscribe Now
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PublicCategoriesSection;
