import { MetadataRoute } from 'next'
import { seoConfig } from '@/lib/seo'
import dbConnect from '@/lib/db'
import ToolModel from '@/models/Tool'
import CategoryModel from '@/models/Category'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const { siteUrl } = seoConfig
  
  try {
    await dbConnect()
    
    // Get all tools
    const tools = await ToolModel.find({ status: 'active' }).select('_id updatedAt').lean()
    
    // Get all categories
    const categories = await CategoryModel.find().select('slug updatedAt').lean()
    
    // Static routes
    const staticRoutes = [
      {
        url: siteUrl,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 1,
      },
      {
        url: `${siteUrl}/tools`,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 0.9,
      },
      {
        url: `${siteUrl}/categories`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.8,
      },
      {
        url: `${siteUrl}/about`,
        lastModified: new Date(),
        changeFrequency: 'monthly' as const,
        priority: 0.5,
      },
      {
        url: `${siteUrl}/privacy`,
        lastModified: new Date(),
        changeFrequency: 'monthly' as const,
        priority: 0.3,
      },
      {
        url: `${siteUrl}/terms`,
        lastModified: new Date(),
        changeFrequency: 'monthly' as const,
        priority: 0.3,
      },
    ]
    
    // Tool routes
    const toolRoutes = tools.map((tool) => ({
      url: `${siteUrl}/tools/${tool._id}`,
      lastModified: tool.updatedAt ? new Date(tool.updatedAt) : new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    }))
    
    // Category routes
    const categoryRoutes = categories.map((category) => ({
      url: `${siteUrl}/categories/${category.slug}`,
      lastModified: category.updatedAt ? new Date(category.updatedAt) : new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.6,
    }))
    
    return [...staticRoutes, ...toolRoutes, ...categoryRoutes]
  } catch (error) {
    console.error('Error generating sitemap:', error)
    
    // Return basic sitemap if database fails
    return [
      {
        url: siteUrl,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 1,
      },
      {
        url: `${siteUrl}/tools`,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 0.9,
      },
    ]
  }
}
