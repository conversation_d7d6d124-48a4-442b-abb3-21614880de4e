# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage
/test-results/
/playwright-report/
/test-coverage/
*.lcov

# next.js
/.next/
/out/

# production
/build
/dist/

# misc
.DS_Store
*.pem
.vscode/
.idea/

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# AI Tools Directory specific
/data/exports/
/data/backups/
/data/imports/
/logs/
*.log
/temp/
/tmp/

# Database dumps and backups
*.dump
*.backup
*.bak
/database-backups/

# Admin exports and reports
/admin-exports/
/analytics-reports/
/seo-reports/
*.csv
*.xlsx
*.pdf
/reports/

# Scraped data and cache
/scraped-data/
/cache/
*.cache
/scraper-cache/

# User uploads (if any)
/uploads/
/user-content/
/submitted-tools/

# Payment and subscription data
/payment-logs/
/subscription-data/
/financial-reports/

# Performance and monitoring
/performance-logs/
/monitoring-data/
*.perf

# Development tools
.eslintcache
.stylelintcache
.parcel-cache/
.turbo/

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# IDE and editor files
*.swp
*.swo
*~
.project
.settings/
*.sublime-project
*.sublime-workspace

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
lib-cov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Local environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Webpack Bundle Analyzer
.webpack-bundle-analyzer/

# Sentry Config File
.sentryclirc
