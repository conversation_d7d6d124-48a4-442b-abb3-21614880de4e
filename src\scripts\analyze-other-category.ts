import Tool from '../models/Tool.js';
import Category from '../models/Category.js';
import mongoose from 'mongoose';

async function analyzeOtherCategory() {
  try {
    console.log('⠙ Connecting to database...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aitools');
    
    // First, find the "Other" category
    const otherCategory = await Category.findOne({ slug: 'other' });
    if (!otherCategory) {
      console.log('❌ Other category not found');
      return;
    }
    
    const otherTools = await Tool.find({ category: otherCategory._id }).populate('category').limit(100);
    console.log(`Found ${otherTools.length} tools in Other category (showing first 100):`);
    console.log('');
    
    // Group by potential categories based on name and description
    const potentialCategories = new Map<string, string[]>();
    
    otherTools.forEach((tool: any) => {
      const name = tool.name.toLowerCase();
      const desc = (tool.description || '').toLowerCase();
      
      // Try to categorize based on keywords
      if (name.includes('chat') || name.includes('bot') || name.includes('ai assistant') || 
          desc.includes('chatbot') || desc.includes('conversation') || desc.includes('chat')) {
        addToCategory(potentialCategories, 'Chatbots & AI Assistants', tool.name);
      } else if (name.includes('design') || name.includes('logo') || name.includes('graphic') ||
                desc.includes('design') || desc.includes('logo') || desc.includes('graphic')) {
        addToCategory(potentialCategories, 'Design & Graphics', tool.name);
      } else if (name.includes('music') || name.includes('audio') || name.includes('sound') ||
                desc.includes('music') || desc.includes('audio') || desc.includes('sound')) {
        addToCategory(potentialCategories, 'Audio & Music', tool.name);
      } else if (name.includes('video') || desc.includes('video')) {
        addToCategory(potentialCategories, 'Video Tools', tool.name);
      } else if (name.includes('photo') || name.includes('image') || name.includes('picture') ||
                desc.includes('photo') || desc.includes('image') || desc.includes('picture')) {
        addToCategory(potentialCategories, 'Photo & Image Editing', tool.name);
      } else if (name.includes('marketing') || name.includes('seo') || name.includes('social') ||
                desc.includes('marketing') || desc.includes('seo') || desc.includes('social media')) {
        addToCategory(potentialCategories, 'Marketing & SEO', tool.name);
      } else if (name.includes('research') || name.includes('analysis') || name.includes('data') ||
                desc.includes('research') || desc.includes('analysis') || desc.includes('analyze')) {
        addToCategory(potentialCategories, 'Research & Analytics', tool.name);
      } else if (name.includes('game') || name.includes('gaming') || desc.includes('game')) {
        addToCategory(potentialCategories, 'Gaming & Entertainment', tool.name);
      } else if (name.includes('finance') || name.includes('money') || name.includes('investment') ||
                desc.includes('finance') || desc.includes('money') || desc.includes('investment')) {
        addToCategory(potentialCategories, 'Finance & Investment', tool.name);
      } else if (name.includes('health') || name.includes('medical') || name.includes('fitness') ||
                desc.includes('health') || desc.includes('medical') || desc.includes('fitness')) {
        addToCategory(potentialCategories, 'Healthcare & Fitness', tool.name);
      } else if (name.includes('travel') || desc.includes('travel')) {
        addToCategory(potentialCategories, 'Travel & Tourism', tool.name);
      } else if (name.includes('email') || name.includes('mail') || desc.includes('email')) {
        addToCategory(potentialCategories, 'Email & Communication', tool.name);
      } else if (name.includes('presentation') || name.includes('slide') || desc.includes('presentation')) {
        addToCategory(potentialCategories, 'Presentations', tool.name);
      } else if (name.includes('translation') || name.includes('translate') || desc.includes('translation')) {
        addToCategory(potentialCategories, 'Translation & Language', tool.name);
      } else if (name.includes('meeting') || desc.includes('meeting')) {
        addToCategory(potentialCategories, 'Meeting & Collaboration', tool.name);
      } else if (name.includes('automation') || desc.includes('automation') || desc.includes('workflow')) {
        addToCategory(potentialCategories, 'Automation & Workflow', tool.name);
      } else if (name.includes('hr') || name.includes('recruit') || name.includes('resume') ||
                desc.includes('hiring') || desc.includes('recruitment') || desc.includes('resume')) {
        addToCategory(potentialCategories, 'HR & Recruitment', tool.name);
      } else if (name.includes('customer') || name.includes('support') || desc.includes('customer service')) {
        addToCategory(potentialCategories, 'Customer Service', tool.name);
      } else if (name.includes('security') || desc.includes('security') || desc.includes('privacy')) {
        addToCategory(potentialCategories, 'Security & Privacy', tool.name);
      } else if (name.includes('3d') || name.includes('ar') || name.includes('vr') ||
                desc.includes('3d') || desc.includes('augmented reality') || desc.includes('virtual reality')) {
        addToCategory(potentialCategories, '3D & AR/VR', tool.name);
      } else if (name.includes('ecommerce') || name.includes('shop') || name.includes('store') ||
                desc.includes('ecommerce') || desc.includes('shopping') || desc.includes('store')) {
        addToCategory(potentialCategories, 'E-commerce & Shopping', tool.name);
      } else if (name.includes('legal') || desc.includes('legal') || desc.includes('law')) {
        addToCategory(potentialCategories, 'Legal & Compliance', tool.name);
      } else if (name.includes('real estate') || desc.includes('real estate') || desc.includes('property')) {
        addToCategory(potentialCategories, 'Real Estate', tool.name);
      } else if (name.includes('pdf') || desc.includes('pdf') || desc.includes('document')) {
        addToCategory(potentialCategories, 'Document Management', tool.name);
      } else {
        addToCategory(potentialCategories, 'Uncategorized', tool.name);
      }
    });
    
    // Display results
    console.log('Potential category breakdown:');
    console.log('=====================================');
    potentialCategories.forEach((tools, category) => {
      console.log(`\\n📁 ${category} (${tools.length} tools):`);
      tools.slice(0, 10).forEach(tool => console.log(`   • ${tool}`));
      if (tools.length > 10) {
        console.log(`   ... and ${tools.length - 10} more`);
      }
    });
    
    await mongoose.disconnect();
    console.log('\\n✅ Analysis complete');
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

function addToCategory(map: Map<string, string[]>, category: string, toolName: string) {
  if (!map.has(category)) {
    map.set(category, []);
  }
  map.get(category)!.push(toolName);
}

analyzeOtherCategory();
