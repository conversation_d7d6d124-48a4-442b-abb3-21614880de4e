'use client'

import { useState, useEffect } from 'react'

export function useClientToken() {
  const [token, setToken] = useState<string | null>(null)
  const [isReady, setIsReady] = useState(false)

  useEffect(() => {
    // Check if running in development mode
    const isDevelopment = typeof window !== 'undefined' && window.location.hostname === 'localhost';
    
    // Check if anti-scraping is enabled
    const antiScrapingEnabled = typeof window !== 'undefined' && 
      window.location.hostname !== 'localhost' && 
      process.env.NEXT_PUBLIC_ANTI_SCRAPING_ENABLED === 'true'
    
    // In development mode or when anti-scraping is disabled, set ready immediately
    if (isDevelopment || !antiScrapingEnabled) {
      setIsReady(true)
      return
    }

    // Check if token is already available
    const existingToken = typeof window !== 'undefined' ? 
      window.__CLIENT_TOKEN__ || localStorage.getItem('client-token') : null
    
    if (existingToken) {
      setToken(existingToken)
      setIsReady(true)
      return
    }

    // Listen for token ready event
    const handleTokenReady = (event: CustomEvent) => {
      const { token } = event.detail
      setToken(token)
      setIsReady(true)
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('clientTokenReady', handleTokenReady as EventListener)
    }

    // Cleanup
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('clientTokenReady', handleTokenReady as EventListener)
      }
    }
  }, [])

  return { token, isReady }
}

// Helper function to make API calls with proper headers
export async function apiCall(url: string, options: RequestInit = {}) {
  // Check if running in development mode
  const isDevelopment = typeof window !== 'undefined' && window.location.hostname === 'localhost';
  
  // In development mode, skip token requirement
  if (isDevelopment) {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
      ...(options.headers as Record<string, string> || {}),
    }

    return fetch(url, {
      ...options,
      headers,
    })
  }

  const token = typeof window !== 'undefined' ? 
    window.__CLIENT_TOKEN__ || localStorage.getItem('client-token') : null

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
    ...(options.headers as Record<string, string> || {}),
  }

  if (token) {
    headers['X-Client-Token'] = token
  }

  return fetch(url, {
    ...options,
    headers,
  })
}
