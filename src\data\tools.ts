import { Tool } from '@/types/tool'

export const tools: Tool[] = [
  {
    _id: '1',
    name: 'ChatGPT',
    description: 'AI-powered chatbot for conversation and assistance',
    imageUrl: '/images/chatgpt.jpg',
    rating: 4.8,
    ratingCount: 1000,
    category: 'Chatbot',
    pricing: 'Freemium',
    url: 'https://chat.openai.com'
  },
  {
    _id: '2',
    name: 'Midjourney',
    description: 'AI image generation tool for creating artwork',
    imageUrl: '/images/midjourney.jpg',
    rating: 4.7,
    ratingCount: 800,
    category: 'Image Generation',
    pricing: 'Paid',
    url: 'https://midjourney.com'
  },
  {
    _id: '3',
    name: 'GitHub Copilot',
    description: 'AI pair programmer that helps write better code',
    imageUrl: '/images/github-copilot.jpg',
    rating: 4.5,
    ratingCount: 500,
    category: 'Code Assistant',
    pricing: 'Paid',
    url: 'https://github.com/features/copilot'
  },
  {
    _id: '4',
    name: 'DALL-E',
    description: 'AI system that creates realistic images and art from natural language descriptions',
    imageUrl: '/images/dalle.jpg',
    rating: 4.6,
    ratingCount: 600,
    category: 'Image Generation',
    pricing: 'Paid',
    url: 'https://openai.com/dall-e-3'
  },
  {
    _id: '5',
    name: 'Claude',
    description: 'Advanced AI assistant by Anthropic for conversation and analysis',
    imageUrl: '/images/claude.jpg',
    rating: 4.7,
    ratingCount: 700,
    category: 'Chatbot',
    pricing: 'Freemium',
    url: 'https://anthropic.com/claude'
  },
  {
    _id: '6',
    name: 'Stable Diffusion',
    description: 'Open-source image generation model for creating artwork',
    imageUrl: '/images/stable-diffusion.jpg',
    rating: 4.4,
    category: 'Image Generation',
    pricing: 'Free',
    url: 'https://stability.ai'
  },
  {
    _id: '7',
    name: 'Jasper',
    description: 'AI content generator for marketing copy and blog posts',
    imageUrl: '/images/jasper.jpg',
    rating: 4.3,
    category: 'Content Creation',
    pricing: 'Paid',
    url: 'https://jasper.ai'
  },
  {
    _id: '8',
    name: 'Notion AI',
    description: 'AI assistant integrated into Notion for writing and brainstorming',
    imageUrl: '/images/notion-ai.jpg',
    rating: 4.2,
    category: 'Productivity',
    pricing: 'Paid',
    url: 'https://notion.so/product/ai'
  },
  {
    _id: '9',
    name: 'Grammarly',
    description: 'AI-powered writing assistant for grammar and style improvements',
    imageUrl: '/images/grammarly.jpg',
    rating: 4.5,
    category: 'Writing',
    pricing: 'Freemium',
    url: 'https://grammarly.com'
  },
  {
    _id: '10',
    name: 'Canva AI',
    description: 'AI-powered design tools for creating visual content',
    imageUrl: '/images/canva-ai.jpg',
    rating: 4.1,
    category: 'Design',
    pricing: 'Freemium',
    url: 'https://canva.com/ai'
  },
  {
    _id: '11',
    name: 'Otter.ai',
    description: 'AI transcription and meeting notes tool',
    imageUrl: '/images/otter.jpg',
    rating: 4.0,
    category: 'Transcription',
    pricing: 'Freemium',
    url: 'https://otter.ai'
  },
  {
    _id: '12',
    name: 'Descript',
    description: 'AI-powered video and audio editing platform',
    imageUrl: '/images/descript.jpg',
    rating: 4.3,
    category: 'Video Editing',
    pricing: 'Paid',
    url: 'https://descript.com'
  }
] 
