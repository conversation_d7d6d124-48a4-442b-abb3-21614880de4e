'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AdminLayout from '@/components/admin/AdminLayout';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';

const AdminSEOPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  
  const [homepageSEO, setHomepageSEO] = useState({
    title: 'AI Tools Directory - Discover the Best AI Tools',
    description: 'Explore our curated collection of AI tools to enhance your productivity, creativity, and workflow. Find the perfect AI solution for your needs.',
    keywords: 'AI tools, artificial intelligence, productivity tools, AI directory, machine learning tools',
    ogImage: '/images/og-homepage.jpg'
  });

  const [toolPagesSEO, setToolPagesSEO] = useState({
    titleTemplate: '{tool_name} - AI Tool Review | AI Tools Directory',
    descriptionTemplate: 'Discover {tool_name}, an innovative AI tool for {category}. Read reviews, pricing, and features. Try {tool_name} today.',
    keywordsTemplate: '{tool_name}, {category}, AI tool, artificial intelligence, {tool_features}',
    canonicalPattern: 'https://aitools.directory/tools/{tool_slug}'
  });

  const [seoMetrics, setSeoMetrics] = useState({
    homepage: {
      googleRanking: 12,
      organicTraffic: 15420,
      keywordRankings: 247,
      backlinks: 89,
      pagespeedScore: 94
    },
    toolPages: {
      averageRanking: 18,
      indexedPages: 1834,
      avgPagespeed: 91,
      structuredDataCoverage: 98
    }
  });

  useEffect(() => {
    // In a real app, load current SEO settings from API
  }, []);

  const handleHomepageSEOSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      // Simulate API call to update homepage SEO
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSuccessMessage('Homepage SEO settings updated successfully!');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      console.error('Error updating homepage SEO:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToolPagesSEOSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      // Simulate API call to update tool pages SEO
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSuccessMessage('Tool pages SEO templates updated successfully!');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      console.error('Error updating tool pages SEO:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const generateSitemap = () => {
    alert('Generating sitemap... This would create/update the sitemap.xml file with all pages.');
  };

  const submitToSearchConsole = () => {
    alert('Submitting to Google Search Console... This would ping Google about sitemap updates.');
  };

  const analyzeSEO = () => {
    alert('Running SEO analysis... This would check all pages for SEO issues and generate a report.');
  };

  return (
    <AdminLayout>
      <div className="container mx-auto px-4 py-8">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-2">
            SEO Management
          </h1>
          <p className="text-lg text-muted-foreground">
            Optimize SEO for homepage and tool pages to improve search rankings
          </p>
        </div>

        {/* Success Message */}
        {successMessage && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
            <p className="text-green-600">{successMessage}</p>
          </div>
        )}

        {/* SEO Metrics Overview */}
        <div className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">SEO Performance Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            
            {/* Homepage Metrics */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Homepage SEO</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">#{seoMetrics.homepage.googleRanking}</div>
                  <div className="text-sm text-muted-foreground">Google Ranking</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{seoMetrics.homepage.organicTraffic.toLocaleString()}</div>
                  <div className="text-sm text-muted-foreground">Organic Traffic</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{seoMetrics.homepage.keywordRankings}</div>
                  <div className="text-sm text-muted-foreground">Keywords Ranking</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{seoMetrics.homepage.pagespeedScore}</div>
                  <div className="text-sm text-muted-foreground">PageSpeed Score</div>
                </div>
              </div>
            </Card>

            {/* Tool Pages Metrics */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Tool Pages SEO</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">#{seoMetrics.toolPages.averageRanking}</div>
                  <div className="text-sm text-muted-foreground">Avg Ranking</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{seoMetrics.toolPages.indexedPages.toLocaleString()}</div>
                  <div className="text-sm text-muted-foreground">Indexed Pages</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{seoMetrics.toolPages.avgPagespeed}</div>
                  <div className="text-sm text-muted-foreground">Avg PageSpeed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{seoMetrics.toolPages.structuredDataCoverage}%</div>
                  <div className="text-sm text-muted-foreground">Structured Data</div>
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* Homepage SEO Settings */}
        <Card className="p-6 mb-8">
          <h2 className="text-xl font-semibold mb-6">Homepage SEO Settings</h2>
          <form onSubmit={handleHomepageSEOSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Page Title</label>
              <Input
                value={homepageSEO.title}
                onChange={(e) => setHomepageSEO({...homepageSEO, title: e.target.value})}
                placeholder="Page title (50-60 characters recommended)"
                maxLength={60}
              />
              <div className="text-xs text-muted-foreground mt-1">
                {homepageSEO.title.length}/60 characters
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Meta Description</label>
              <textarea
                className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                rows={3}
                value={homepageSEO.description}
                onChange={(e) => setHomepageSEO({...homepageSEO, description: e.target.value})}
                placeholder="Meta description (150-160 characters recommended)"
                maxLength={160}
              />
              <div className="text-xs text-muted-foreground mt-1">
                {homepageSEO.description.length}/160 characters
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Keywords</label>
              <Input
                value={homepageSEO.keywords}
                onChange={(e) => setHomepageSEO({...homepageSEO, keywords: e.target.value})}
                placeholder="Comma-separated keywords"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Open Graph Image</label>
              <Input
                value={homepageSEO.ogImage}
                onChange={(e) => setHomepageSEO({...homepageSEO, ogImage: e.target.value})}
                placeholder="/images/og-homepage.jpg"
              />
            </div>

            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Updating...' : 'Update Homepage SEO'}
            </Button>
          </form>
        </Card>

        {/* Tool Pages SEO Templates */}
        <Card className="p-6 mb-8">
          <h2 className="text-xl font-semibold mb-6">Tool Pages SEO Templates</h2>
          <form onSubmit={handleToolPagesSEOSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Title Template</label>
              <Input
                value={toolPagesSEO.titleTemplate}
                onChange={(e) => setToolPagesSEO({...toolPagesSEO, titleTemplate: e.target.value})}
                placeholder="Use {tool_name}, {category} placeholders"
              />
              <div className="text-xs text-muted-foreground mt-1">
                Available placeholders: {'{tool_name}'}, {'{category}'}, {'{price}'}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Description Template</label>
              <textarea
                className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                rows={3}
                value={toolPagesSEO.descriptionTemplate}
                onChange={(e) => setToolPagesSEO({...toolPagesSEO, descriptionTemplate: e.target.value})}
                placeholder="Use placeholders for dynamic descriptions"
              />
              <div className="text-xs text-muted-foreground mt-1">
                Available placeholders: {'{tool_name}'}, {'{category}'}, {'{tool_features}'}, {'{price}'}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Keywords Template</label>
              <Input
                value={toolPagesSEO.keywordsTemplate}
                onChange={(e) => setToolPagesSEO({...toolPagesSEO, keywordsTemplate: e.target.value})}
                placeholder="Comma-separated with placeholders"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Canonical URL Pattern</label>
              <Input
                value={toolPagesSEO.canonicalPattern}
                onChange={(e) => setToolPagesSEO({...toolPagesSEO, canonicalPattern: e.target.value})}
                placeholder="https://aitools.directory/tools/{tool_slug}"
              />
            </div>

            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Updating...' : 'Update Tool Pages SEO'}
            </Button>
          </form>
        </Card>

        {/* SEO Tools */}
        <Card className="p-6 mb-8">
          <h2 className="text-xl font-semibold mb-6">SEO Tools & Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button onClick={generateSitemap} variant="outline" className="w-full">
              Generate Sitemap
            </Button>
            <Button onClick={submitToSearchConsole} variant="outline" className="w-full">
              Submit to Search Console
            </Button>
            <Button onClick={analyzeSEO} variant="outline" className="w-full">
              Run SEO Analysis
            </Button>
          </div>
        </Card>

        {/* SEO Recommendations */}
        <Card className="p-6 mb-8">
          <h2 className="text-xl font-semibold mb-6">SEO Recommendations</h2>
          <div className="space-y-4">
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-800 mb-2">🔍 Improve Tool Page Loading Speed</h3>
              <p className="text-sm text-yellow-700">
                Some tool pages have PageSpeed scores below 90. Consider optimizing images and reducing JavaScript bundle size.
              </p>
            </div>
            
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-800 mb-2">📱 Mobile Optimization</h3>
              <p className="text-sm text-blue-700">
                Ensure all tool pages are mobile-friendly. Consider implementing AMP for faster mobile loading.
              </p>
            </div>
            
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-medium text-green-800 mb-2">🔗 Internal Linking</h3>
              <p className="text-sm text-green-700">
                Add more internal links between related AI tools to improve page authority distribution.
              </p>
            </div>
            
            <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
              <h3 className="font-medium text-purple-800 mb-2">📊 Structured Data</h3>
              <p className="text-sm text-purple-700">
                Implement Product schema for tool pages to enhance search result appearance.
              </p>
            </div>
          </div>
        </Card>

        {/* Back to Admin Dashboard */}
        <div className="text-center">
          <Link href="/admin">
            <Button variant="outline">← Back to Admin Dashboard</Button>
          </Link>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminSEOPage;
