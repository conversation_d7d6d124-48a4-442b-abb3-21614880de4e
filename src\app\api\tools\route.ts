import { NextResponse } from 'next/server'
import { NextRequest } from 'next/server'
import dbConnect from '@/lib/db'
import ToolModel from '@/models/Tool'
import CategoryModel from '@/models/Category'
import { generatePlaceholderImage } from '@/lib/generatePlaceholderImage'
import AntiScrapingService from '@/lib/antiScraping'

export async function GET(request: NextRequest) {
  try {
    // Anti-scraping checks (only if enabled)
    const isDevelopment = process.env.NODE_ENV === 'development'
    const antiScrapingEnabled = process.env.ANTI_SCRAPING_ENABLED === 'true'
    
    if (antiScrapingEnabled) {
      const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
      const antiScraping = AntiScrapingService.getInstance()
      
      // Check rate limiting
      const rateLimitCheck = antiScraping.checkRateLimit(ip)
      if (!rateLimitCheck.allowed) {
        return NextResponse.json(
          { error: 'Too many requests' }, 
          { 
            status: 429,
            headers: {
              'Retry-After': rateLimitCheck.retryAfter?.toString() || '60'
            }
          }
        )
      }
      
      // Check for suspicious behavior
      const suspiciousCheck = antiScraping.isSuspiciousRequest(request, ip)
      if (suspiciousCheck.isSuspicious) {
        console.log(`Blocked suspicious request from ${ip}: ${suspiciousCheck.reason}`)
        return NextResponse.json(
          { error: 'Access denied' }, 
          { status: 403 }
        )
      }
      
      // Validate client token for API requests
      const clientToken = request.headers.get('x-client-token')
      if (!isDevelopment && (!clientToken || !antiScraping.validateClientToken(clientToken))) {
        return NextResponse.json(
          { error: 'Invalid or missing client token' }, 
          { status: 401 }
        )
      }
      
      // Track this request
      const ip2 = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
      antiScraping.trackRequest(ip2, request.nextUrl.pathname + request.nextUrl.search)
    }

    // Check if database URI is configured
    if (!process.env.MONGODB_URI) {
      console.error('MONGODB_URI environment variable is not configured')
      return NextResponse.json(
        { 
          error: 'Database not configured',
          tools: [],
          total: 0,
          page: 1,
          totalPages: 0
        },
        { status: 503 }
      )
    }

    // Try to connect to database
    try {
      await dbConnect()
    } catch (dbError) {
      console.error('Database connection failed:', dbError)
      return NextResponse.json(
        { 
          error: 'Database connection failed',
          tools: [],
          total: 0,
          page: 1,
          totalPages: 0
        },
        { status: 503 }
      )
    }
    
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('query')?.toLowerCase() || ''
    const category = searchParams.get('category') || ''
    const sortBy = searchParams.get('sortBy') as 'rating' | 'name' || 'rating'
    const page = parseInt(searchParams.get('page') || '1')
    let limit = parseInt(searchParams.get('limit') || '12')
    
    // Limit maximum results to prevent bulk scraping
    if (limit > 50) {
      limit = 50
    }
    
    // Build the query
    let filterQuery: any = {}
    
    // Apply search filter if query exists
    if (query) {
      filterQuery.$or = [
        { name: { $regex: query, $options: 'i' } },
        { description: { $regex: query, $options: 'i' } },
        { tags: { $regex: query, $options: 'i' } }
      ]
    }
    
    // Apply category filter - need to lookup category by name first
    if (category) {
      const categoryDoc = await CategoryModel.findOne({ name: category });
      if (categoryDoc) {
        filterQuery.category = categoryDoc._id;
      } else {
        // If category doesn't exist, return empty results
        return NextResponse.json({
          tools: [],
          total: 0,
          page,
          totalPages: 0
        })
      }
    }
    
    // Build sort options
    const sortOptions: any = {}
    if (sortBy === 'rating') {
      sortOptions.rating = -1
    } else if (sortBy === 'name') {
      sortOptions.name = 1
    }
    
    // Count total documents for pagination
    const total = await ToolModel.countDocuments(filterQuery)
    
    // Fetch paginated results
    const tools = await ToolModel.find(filterQuery)
      .populate('category', 'name slug')
      .sort(sortOptions)
      .skip((page - 1) * limit)
      .limit(limit)
      .lean()

    return NextResponse.json({
      tools,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    })
  } catch (error) {
    console.error('Error fetching tools:', error)
    return NextResponse.json(
      { error: 'Failed to fetch tools' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    await dbConnect()
    
    const toolData = await request.json()
    
    // Validate required fields
    if (!toolData.name || !toolData.description || !toolData.category || !toolData.pricing) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }
    
    // Generate placeholder image if not provided
    if (!toolData.imageUrl) {
      toolData.imageUrl = generatePlaceholderImage(toolData.name)
    }
    
    // Create new tool
    const newTool = await ToolModel.create(toolData)
    
    return NextResponse.json(newTool, { status: 201 })
  } catch (error) {
    console.error('Error creating tool:', error)
    return NextResponse.json(
      { error: 'Failed to create tool' },
      { status: 500 }
    )
  }
} 