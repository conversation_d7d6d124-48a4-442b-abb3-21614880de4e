import fs from 'fs';
import path from 'path';
import Category from '../models/Category';
import Tool from '../models/Tool';
import dbConnect from '../lib/db';

// Define types
interface ToolData {
  name: string;
  description?: string;
  features?: string[];
  pricing?: string;
  url?: string;
  company?: string;
  apiAvailable?: boolean;
  apiUrl?: string;
  documentationUrl?: string;
}

// Tool categories mapping
const toolCategories: { [key: string]: string } = {
  'ChatGPT': 'Language Models',
  'Claude': 'Language Models',
  'Midjourney': 'Image Generation',
  'DALL-E': 'Image Generation',
  'Stable Diffusion': 'Image Generation',
  'Jasper': 'Content Creation',
  'Copy.ai': 'Content Creation',
  'Grammarly': 'Writing',
  'QuillBot': 'Writing',
  'Notion AI': 'Productivity',
  'Otter.ai': 'Transcription',
  'Descript': 'Video Editing',
  'Runway': 'Video Generation',
  'Adobe Firefly': 'Image Generation',
  'GitHub Copilot': 'Code Generation',
  'Amazon CodeWhisperer': 'Code Generation',
  'Anthropic': 'Language Models',
  'Coursera': 'Education',
  'Udemy': 'Education',
  'Duolingo': 'Education',
  'Pictory': 'Video Editing',
  'default': 'Other'
};

// Tool descriptions mapping
const toolDescriptions: { [key: string]: string } = {
  'ChatGPT': 'An advanced AI language model by OpenAI that can engage in natural conversations and assist with various tasks.',
  'Claude': 'An AI assistant by Anthropic known for its helpful, honest, and harmless approach to conversations.',
  'Midjourney': 'An AI art generator that creates stunning images from text descriptions.',
  'DALL-E': 'OpenAI\'s image generation model that creates unique images from textual descriptions.',
  'default': 'An AI-powered tool designed to enhance productivity and creativity.'
};

// Tool features mapping
const toolFeatures: { [key: string]: string[] } = {
  'ChatGPT': [
    'Natural language processing',
    'Content generation',
    'Code assistance',
    'Translation',
    'Question answering'
  ],
  'Claude': [
    'Natural language understanding',
    'Content creation',
    'Analysis and research',
    'Code generation',
    'Task assistance'
  ],
  'default': ['AI-powered functionality', 'User-friendly interface', 'Integration capabilities']
};

// Tool pricing mapping
const toolPricing: { [key: string]: string } = {
  'ChatGPT': 'Free / $20 per month for Plus',
  'Claude': 'Free / Premium plans available',
  'Midjourney': 'Starting at $10 per month',
  'DALL-E': 'Pay-per-use credits system',
  'default': 'Contact for pricing'
};

// Tool URLs mapping
const toolUrls: { [key: string]: string } = {
  'ChatGPT': 'https://chat.openai.com',
  'Claude': 'https://claude.ai',
  'Midjourney': 'https://www.midjourney.com',
  'DALL-E': 'https://labs.openai.com',
  'default': ''
};

// Company mapping
const toolCompanies: { [key: string]: string } = {
  'ChatGPT': 'OpenAI',
  'Claude': 'Anthropic',
  'Midjourney': 'Midjourney',
  'DALL-E': 'OpenAI',
  'default': ''
};

// API availability mapping
const toolApiAvailable: { [key: string]: boolean } = {
  'ChatGPT': true,
  'Claude': true,
  'DALL-E': true,
  'default': false
};

// API URLs mapping
const toolApiUrls: { [key: string]: string } = {
  'ChatGPT': 'https://platform.openai.com/docs/api-reference',
  'Claude': 'https://docs.anthropic.com/claude/reference',
  'DALL-E': 'https://platform.openai.com/docs/api-reference',
  'default': ''
};

// Documentation URLs mapping
const toolDocUrls: { [key: string]: string } = {
  'ChatGPT': 'https://platform.openai.com/docs',
  'Claude': 'https://docs.anthropic.com/claude',
  'DALL-E': 'https://platform.openai.com/docs/guides/images',
  'default': ''
};

async function importTools() {
  try {
    // Connect to database
    await dbConnect();
    console.log('Connected to database');

    // Read tools from file
    const toolsFile = path.join(process.cwd(), 'tools_list.txt');
    const tools = fs.readFileSync(toolsFile, 'utf8')
      .split('\n')
      .filter((line: string) => line.trim() !== '');

    console.log(`Found ${tools.length} tools to import`);

    // Get unique categories
    const uniqueCategories = [...new Set(Object.values(toolCategories))];
    const categoryMap = new Map<string, string>();

    // Create categories first
    for (const categoryName of uniqueCategories) {
      const slug = categoryName.toLowerCase().replace(/\s+/g, '-');
      let category = await Category.findOne({ slug });
      
      if (!category) {
        category = await Category.create({
          name: categoryName,
          slug,
          description: `Collection of ${categoryName} tools`,
          order: uniqueCategories.indexOf(categoryName)
        });
        console.log(`Created category: ${categoryName}`);
      }
      
      categoryMap.set(categoryName, category._id.toString());
    }

    // Import tools
    for (const toolName of tools) {
      const existingTool = await Tool.findOne({ name: toolName });
      
      if (!existingTool) {
        const categoryName = toolCategories[toolName] || toolCategories.default;
        const categoryId = categoryMap.get(categoryName);
        
        if (!categoryId) {
          console.log(`Category not found for tool: ${toolName}`);
          continue;
        }

        const toolData: ToolData = {
          name: toolName,
          description: toolDescriptions[toolName] || toolDescriptions.default,
          features: toolFeatures[toolName] || toolFeatures.default,
          pricing: toolPricing[toolName] || toolPricing.default,
          url: toolUrls[toolName] || toolUrls.default,
          company: toolCompanies[toolName] || toolCompanies.default,
          apiAvailable: toolApiAvailable[toolName] ?? toolApiAvailable.default,
          apiUrl: toolApiUrls[toolName] || toolApiUrls.default,
          documentationUrl: toolDocUrls[toolName] || toolDocUrls.default
        };

        const slug = toolName.toLowerCase().replace(/\s+/g, '-');
        
        const newTool = await Tool.create({
          ...toolData,
          slug,
          category: categoryId,
          rating: 0,
          ratingCount: 0,
          status: 'active'
        });

        console.log(`Created tool: ${toolName}`);
      } else {
        console.log(`Tool already exists: ${toolName}`);
      }
    }

    console.log('Import completed successfully');
  } catch (error) {
    console.error('Error importing tools:', error);
    process.exit(1);
  }
}

importTools();