const fs = require('fs');
const path = require('path');

console.log('🔍 Debugging environment setup...');

// Check if .env.local exists
const envPath = path.join(__dirname, '.env.local');
console.log(`📁 Looking for .env.local at: ${envPath}`);
console.log(`📄 .env.local exists: ${fs.existsSync(envPath)}`);

if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  console.log('📝 .env.local content:');
  console.log('---START---');
  console.log(envContent);
  console.log('---END---');
  
  // Check if MONGODB_URI is in the file
  console.log(`🔍 Contains MONGODB_URI: ${envContent.includes('MONGODB_URI')}`);
}

// Try loading with dotenv
try {
  require('dotenv').config({ path: '.env.local' });
  console.log(`🔧 After dotenv load:`);
  console.log(`   MONGODB_URI: ${process.env.MONGODB_URI ? 'Found' : 'Not found'}`);
  if (process.env.MONGODB_URI) {
    // Show partial URI for security
    const uri = process.env.MONGODB_URI;
    const masked = uri.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@');
    console.log(`   URI (masked): ${masked}`);
  }
} catch (error) {
  console.error('❌ Error loading .env.local:', error.message);
}
