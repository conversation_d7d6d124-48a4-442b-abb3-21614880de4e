import React from 'react';
import { Card } from '@/components/ui/Card';

export const metadata = {
  title: 'About Us - AI Tools Directory',
  description: 'Learn about our mission to help you discover and explore the best AI tools for your needs.',
};

const AboutPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
            About AI Tools Directory
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Your trusted companion in the ever-evolving world of artificial intelligence tools
          </p>
        </div>

        {/* Mission Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          <div>
            <h2 className="text-3xl font-bold text-foreground mb-6">Our Mission</h2>
            <p className="text-lg text-muted-foreground mb-4">
              At AI Tools Directory, we believe that artificial intelligence should be accessible, 
              understandable, and beneficial for everyone. Our mission is to democratize access to 
              AI tools by providing a comprehensive, curated directory that helps users discover 
              the perfect AI solutions for their specific needs.
            </p>
            <p className="text-lg text-muted-foreground mb-4">
              Whether you're a developer looking for machine learning frameworks, a content creator 
              seeking AI-powered design tools, or a business owner exploring automation solutions, 
              we're here to guide you through the vast landscape of AI technologies.
            </p>
            <p className="text-lg text-muted-foreground">
              We also provide a platform for AI tool creators and companies to showcase their 
              innovations to a targeted audience of potential users, fostering growth and 
              innovation in the AI ecosystem.
            </p>
          </div>
          <div>
            <Card className="p-8 bg-gradient-to-br from-primary/5 to-secondary/5">
              <h3 className="text-2xl font-semibold text-foreground mb-4">What We Offer</h3>
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  Comprehensive directory of AI tools across all categories
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  Detailed reviews and ratings from real users
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  Easy-to-use search and filtering capabilities
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  All-inclusive subscription with full access
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  Tool submission and promotion for subscribers
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  Regular updates with the latest AI innovations
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  Expert insights and comparisons
                </li>
              </ul>
            </Card>
          </div>
        </div>

        {/* Values Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-foreground mb-12">Our Values</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="p-6 text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Transparency</h3>
              <p className="text-muted-foreground">
                We provide honest, unbiased reviews and clear information about each AI tool's 
                capabilities, limitations, and pricing.
              </p>
            </Card>
            <Card className="p-6 text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Innovation</h3>
              <p className="text-muted-foreground">
                We stay at the forefront of AI technology, constantly updating our directory 
                with the latest and most innovative tools.
              </p>
            </Card>
            <Card className="p-6 text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Community</h3>
              <p className="text-muted-foreground">
                We foster a community of AI enthusiasts, developers, and users who share 
                knowledge and experiences.
              </p>
            </Card>
          </div>
        </div>

        {/* Team Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-foreground mb-12">Our Story</h2>
          <div className="max-w-4xl mx-auto">
            <Card className="p-8">
              <p className="text-lg text-muted-foreground mb-6">
                Founded in 2024, AI Tools Directory emerged from a simple observation: the AI tool 
                landscape was growing exponentially, but finding the right tool for specific needs 
                was becoming increasingly difficult. Our founders, experienced technologists and AI 
                enthusiasts, recognized the need for a centralized, reliable platform that could 
                help users navigate this complex ecosystem.
              </p>
              <p className="text-lg text-muted-foreground mb-6">
                What started as a small curated list has grown into a comprehensive directory 
                featuring over 1,000 AI tools across dozens of categories. Our team of researchers, 
                developers, and AI experts work tirelessly to evaluate, categorize, and review 
                each tool to ensure our users have access to the most accurate and up-to-date 
                information.
              </p>
              <p className="text-lg text-muted-foreground mb-6">
                Today, we serve thousands of users worldwide, from individual creators and 
                entrepreneurs to large enterprises and educational institutions. We've also built 
                a sustainable business model that allows us to maintain high-quality curation 
                while providing value to both users and AI tool creators.
              </p>
              <p className="text-lg text-muted-foreground">
                Our commitment remains the same: to make AI accessible, understandable, and useful 
                for everyone while supporting innovation in the AI ecosystem.
              </p>
            </Card>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <Card className="p-8 bg-gradient-to-r from-primary/5 to-secondary/5">
            <h2 className="text-3xl font-bold text-foreground mb-4">Join Our Community</h2>
            <p className="text-lg text-muted-foreground mb-6">
              Be part of the AI revolution. Discover new tools, share your experiences, 
              and help others find the perfect AI solutions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/pricing"
                className="inline-flex items-center justify-center px-6 py-3 bg-primary text-primary-foreground font-medium rounded-md hover:bg-primary/90 transition-colors"
              >
                Get Full Access
              </a>
              <a
                href="/"
                className="inline-flex items-center justify-center px-6 py-3 border border-primary text-primary font-medium rounded-md hover:bg-primary/5 transition-colors"
              >
                Preview Tools
              </a>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AboutPage;
