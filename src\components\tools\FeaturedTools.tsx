'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { fetchTools } from '@/services/toolsService';
import ToolCard from './ToolCard';
import { Button } from '../ui/Button';
import { useClientToken } from '@/hooks/useClientToken';

const FeaturedTools: React.FC = () => {
  const [tools, setTools] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isReady: tokenReady } = useClientToken();

  useEffect(() => {
    const loadFeaturedTools = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetchTools({
          sortBy: 'rating',
          limit: 6
        });
        setTools(response.tools || []);
      } catch (err) {
        console.error('Error loading featured tools:', err);
        // If it's a database connection error, show a generic message
        if (err instanceof Error && (err.message.includes('Database') || err.message.includes('503'))) {
          setError('Database is currently unavailable. Please check back later.');
        } else {
          setError('Failed to load featured tools');
        }
      } finally {
        setLoading(false);
      }
    };

    // Wait for client token to be ready (or immediately if anti-scraping is disabled)
    if (!tokenReady) return;
    loadFeaturedTools();
  }, [tokenReady]);

  if (loading) {
    return (
      <div className="py-12">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-6">Featured Tools</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-card rounded-lg shadow-md p-4 h-64 animate-pulse">
                <div className="bg-muted h-32 rounded-md mb-4"></div>
                <div className="h-6 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-muted rounded w-1/2 mb-4"></div>
                <div className="h-4 bg-muted rounded w-full"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-12">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-6">Featured Tools</h2>
          <div className="text-center py-8 bg-gray-50 rounded-lg">
            <div className="text-gray-600 mb-4">
              <h3 className="text-lg font-semibold mb-2">AI Tools Coming Soon!</h3>
              <p>We're setting up our comprehensive AI tools database.</p>
              <p className="text-sm mt-2 text-gray-500">{error}</p>
            </div>
            <Button variant="outline" onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="py-12">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Featured Tools</h2>
          <Link href="/tools">
            <Button variant="ghost">View All</Button>
          </Link>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tools.map((tool) => (
            <ToolCard 
              key={tool._id} 
              id={tool._id}
              name={tool.name}
              description={tool.description}
              imageUrl={tool.imageUrl || ''}
              rating={tool.rating || 0}
              category={tool.category}
              pricing={tool.pricing as 'Free' | 'Freemium' | 'Paid' || 'Free'}
              url={tool.url || ''}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default FeaturedTools; 