'use client'

import React, { useState, useEffect } from 'react';
import <PERSON> from 'next/link';
import { fetchTools } from '@/services/toolsService';
import ToolCard from './ToolCard';
import { Button } from '../ui/Button';
import { useClientToken } from '@/hooks/useClientToken';
import ComingSoon from '../ui/ComingSoon';
import LoadingState from '../ui/LoadingState';

const FeaturedTools: React.FC = () => {
  const [tools, setTools] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isReady: tokenReady } = useClientToken();

  useEffect(() => {
    const loadFeaturedTools = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetchTools({
          sortBy: 'rating',
          limit: 6
        });
        setTools(response.tools || []);
      } catch (err) {
        console.error('Error loading featured tools:', err);
        // If it's a database connection error, show a generic message
        if (err instanceof Error && (err.message.includes('Database') || err.message.includes('503'))) {
          setError('Database is currently unavailable. Please check back later.');
        } else {
          setError('Failed to load featured tools');
        }
      } finally {
        setLoading(false);
      }
    };

    // Wait for client token to be ready (or immediately if anti-scraping is disabled)
    if (!tokenReady) return;
    loadFeaturedTools();
  }, [tokenReady]);

  if (loading) {
    return <LoadingState title="Featured Tools" type="tools" count={6} />;
  }

  if (error) {
    return (
      <div className="py-12">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-6">Featured Tools</h2>
          <ComingSoon
            title="AI Tools Coming Soon!"
            message="We're setting up our comprehensive AI tools database."
            subMessage={error}
            type="tools"
            onRetry={() => window.location.reload()}
          />
        </div>
      </div>
    );
  }

  // Show empty state if no tools are available
  if (tools.length === 0) {
    return (
      <div className="py-12">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-6">Featured Tools</h2>
          <ComingSoon
            title="AI Tools Coming Soon!"
            message="We're setting up our comprehensive AI tools database."
            subMessage="Check back soon for amazing AI tools!"
            type="tools"
            showRetry={false}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="py-12">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Featured Tools</h2>
          <Link href="/tools">
            <Button variant="ghost">View All</Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tools.map((tool) => (
            <ToolCard
              key={tool._id}
              id={tool._id}
              name={tool.name}
              description={tool.description}
              imageUrl={tool.imageUrl || ''}
              rating={tool.rating || 0}
              category={tool.category}
              pricing={tool.pricing as 'Free' | 'Freemium' | 'Paid' || 'Free'}
              url={tool.url || ''}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default FeaturedTools; 