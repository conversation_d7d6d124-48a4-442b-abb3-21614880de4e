import { <PERSON>ada<PERSON> } from 'next'
import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import dbConnect from '@/lib/db'
import CategoryModel from '@/models/Category'
import ToolModel from '@/models/Tool'
import StructuredData from '@/components/seo/StructuredData'
import { generateBreadcrumbJsonLd, seoConfig } from '@/lib/seo'

interface Category {
  _id: string
  name: string
  slug: string
  description: string
  icon?: string
  toolCount: number
}

export const metadata: Metadata = {
  title: 'AI Tool Categories | Discover AI Tools by Category',
  description: 'Browse AI tools by categories including chatbots, image generation, code assistants, content creation, data analysis, and more. Find the perfect AI solution for your needs.',
  keywords: [
    'AI categories',
    'AI tool categories', 
    'chatbot tools',
    'image generation AI',
    'code assistant AI',
    'content creation tools',
    'data analysis AI',
    'productivity AI',
    'business AI tools',
    'creative AI tools'
  ],
  openGraph: {
    title: 'AI Tool Categories | Discover AI Tools by Category',
    description: 'Browse AI tools by categories including chatbots, image generation, code assistants, content creation, data analysis, and more.',
    type: 'website',
    url: `${seoConfig.siteUrl}/categories`,
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AI Tool Categories | Discover AI Tools by Category',
    description: 'Browse AI tools by categories including chatbots, image generation, code assistants, content creation, data analysis, and more.',
  },
  alternates: {
    canonical: `${seoConfig.siteUrl}/categories`,
  },
}

async function getCategories(): Promise<Category[]> {
  try {
    await dbConnect()
    
    const categories = await CategoryModel.find().sort({ name: 1 }).lean()
    
    // Get tool counts for each category
    const categoriesWithCounts = await Promise.all(
      categories.map(async (category) => {
        const toolCount = await ToolModel.countDocuments({ 
          category: category._id,
          status: 'active' 
        })
        
        return {
          _id: (category._id as any).toString(),
          name: category.name,
          slug: category.slug,
          description: category.description,
          icon: category.icon || '',
          toolCount
        } as Category
      })
    )
    
    return categoriesWithCounts
  } catch (error) {
    console.error('Error fetching categories:', error)
    return []
  }
}

export default async function CategoriesPage() {
  const categories = await getCategories()
  
  const breadcrumbs = [
    { name: "Home", url: seoConfig.siteUrl },
    { name: "Categories", url: `${seoConfig.siteUrl}/categories` }
  ]

  return (
    <>
      <StructuredData data={generateBreadcrumbJsonLd(breadcrumbs)} />
      
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="container mx-auto px-4 py-12">
          {/* Header Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              AI Tool Categories
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Explore AI tools organized by categories. From chatbots to image generation, 
              find the perfect AI solution for your specific needs and use cases.
            </p>
          </div>

          {/* Categories Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
            {categories.map((category) => (
              <Card key={category._id} className="hover:shadow-lg transition-shadow duration-300 h-full">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-xl font-semibold">
                        {category.icon && <span className="mr-2">{category.icon}</span>}
                        {category.name}
                      </CardTitle>
                      <Badge variant="secondary" className="mt-2">
                        {category.toolCount} tools
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 mb-4 line-clamp-3">
                    {category.description || `Discover the best ${category.name.toLowerCase()} AI tools to enhance your workflow and productivity.`}
                  </CardDescription>
                  <Link href={`/categories/${category.slug}`}>
                    <Button className="w-full">
                      Explore {category.name} Tools
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* SEO Content Section */}
          <section className="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 className="text-3xl font-bold mb-6 text-center">
              Why Choose AI Tools from Our Directory?
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🔍</span>
                </div>
                <h3 className="font-semibold mb-2">Curated Selection</h3>
                <p className="text-gray-600 text-sm">
                  Every AI tool is carefully reviewed and categorized for quality and effectiveness.
                </p>
              </div>
              <div className="text-center">
                <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">⚡</span>
                </div>
                <h3 className="font-semibold mb-2">Regular Updates</h3>
                <p className="text-gray-600 text-sm">
                  Our directory is updated daily with the latest AI tools and innovations.
                </p>
              </div>
              <div className="text-center">
                <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">⭐</span>
                </div>
                <h3 className="font-semibold mb-2">User Reviews</h3>
                <p className="text-gray-600 text-sm">
                  Real user ratings and reviews help you make informed decisions.
                </p>
              </div>
              <div className="text-center">
                <div className="bg-yellow-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🎯</span>
                </div>
                <h3 className="font-semibold mb-2">Easy Discovery</h3>
                <p className="text-gray-600 text-sm">
                  Find exactly what you need with our intuitive categorization system.
                </p>
              </div>
            </div>
          </section>

          {/* Popular Categories Section */}
          <section className="text-center">
            <h2 className="text-3xl font-bold mb-8">Most Popular AI Categories</h2>
            <div className="flex flex-wrap justify-center gap-4">
              {categories
                .sort((a, b) => b.toolCount - a.toolCount)
                .slice(0, 8)
                .map((category) => (
                  <Link key={category._id} href={`/categories/${category.slug}`}>
                    <Badge 
                      variant="outline" 
                      className="px-4 py-2 text-lg hover:bg-blue-50 transition-colors cursor-pointer"
                    >
                      {category.name} ({category.toolCount})
                    </Badge>
                  </Link>
                ))}
            </div>
          </section>
        </div>
      </div>
    </>
  )
}
