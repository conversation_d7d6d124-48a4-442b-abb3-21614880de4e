import dbConnect from '../lib/db'
import Tool from '../models/Tool'
import Category from '../models/Category'

async function debugCategoryAssociations() {
  try {
    console.log('🔍 Connecting to database...');
    await dbConnect();
    
    // Get the "Other" category
    const otherCategory = await Category.findOne({ slug: 'other' });
    console.log('Other category:', otherCategory);
    
    if (otherCategory) {
      // Count tools directly associated with this category ID
      const toolsInOther = await Tool.countDocuments({ category: otherCategory._id });
      console.log(`Tools with category ID ${otherCategory._id}:`, toolsInOther);
      
      // Get a few sample tools to see what their category field looks like
      const sampleTools = await Tool.find({ category: otherCategory._id }).limit(3);
      console.log('Sample tools in Other category:');
      sampleTools.forEach(tool => {
        console.log(`- ${tool.name}: category = ${tool.category}`);
      });
    }
    
    // Check if tools have category field at all
    const toolsWithCategory = await Tool.countDocuments({ category: { $exists: true, $ne: null } });
    const toolsWithoutCategory = await Tool.countDocuments({ $or: [{ category: { $exists: false } }, { category: null }] });
    
    console.log(`Tools with category field: ${toolsWithCategory}`);
    console.log(`Tools without category field: ${toolsWithoutCategory}`);
    
    // Get a few sample tools without categories
    const toolsWithoutCat = await Tool.find({ $or: [{ category: { $exists: false } }, { category: null }] }).limit(5);
    console.log('Sample tools without category:');
    toolsWithoutCat.forEach(tool => {
      console.log(`- ${tool.name}: category = ${tool.category}`);
    });
    
    console.log('🎉 Debug complete!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run the function
debugCategoryAssociations().then(() => {
  console.log('✅ Script completed');
}).catch(console.error);
