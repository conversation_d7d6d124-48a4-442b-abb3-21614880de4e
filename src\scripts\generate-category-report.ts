import mongoose from 'mongoose';
import Category from '../models/Category.js';
import Tool from '../models/Tool.js';

async function generateCategoryReport() {
  try {
    console.log('🔗 Connecting to database...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aitools');

    // Get all categories with their tool counts, sorted by order
    const categories = await Category.find({}).sort({ order: 1, name: 1 });
    
    console.log('\\n📊 AI TOOLS CATEGORIZATION REPORT');
    console.log('=====================================');
    
    let totalTools = 0;
    let categorizedTools = 0;
    let otherTools = 0;
    
    console.log('\\n🏷️  CATEGORY BREAKDOWN (sorted by relevance):');
    console.log('='.repeat(60));
    
    for (const category of categories) {
      const toolCount = await Tool.countDocuments({ category: category._id });
      totalTools += toolCount;
      
      if (category.slug === 'other') {
        otherTools = toolCount;
      } else {
        categorizedTools += toolCount;
      }
      
      const icon = category.icon || '📁';
      const percentage = totalTools > 0 ? ((toolCount / 1044) * 100).toFixed(1) : '0.0';
      
      console.log(`${icon} ${category.name.padEnd(35)} ${toolCount.toString().padStart(4)} tools (${percentage}%)`);
      
      if (category.description) {
        console.log(`   ${category.description}`);
      }
      console.log('');
    }
    
    // Summary statistics
    console.log('\\n📈 CATEGORIZATION STATISTICS');
    console.log('='.repeat(40));
    console.log(`Total Tools in Database: ${totalTools}`);
    console.log(`Properly Categorized: ${categorizedTools} (${((categorizedTools/totalTools)*100).toFixed(1)}%)`);
    console.log(`Remaining in Other: ${otherTools} (${((otherTools/totalTools)*100).toFixed(1)}%)`);
    console.log(`Categorization Success: ${((categorizedTools/totalTools)*100).toFixed(1)}%`);
    
    // Top categories by tool count
    console.log('\\n🔝 TOP 10 CATEGORIES BY TOOL COUNT');
    console.log('='.repeat(40));
    const sortedCategories = categories
      .map(cat => ({ name: cat.name, toolCount: cat.toolCount, icon: cat.icon || '📁' }))
      .sort((a, b) => b.toolCount - a.toolCount)
      .slice(0, 10);
    
    sortedCategories.forEach((cat, index) => {
      console.log(`${(index + 1).toString().padStart(2)}. ${cat.icon} ${cat.name.padEnd(30)} ${cat.toolCount} tools`);
    });
    
    // Categories that need attention (empty or very few tools)
    console.log('\\n⚠️  CATEGORIES NEEDING ATTENTION');
    console.log('='.repeat(40));
    const emptyCategories = categories.filter(cat => cat.toolCount === 0);
    const smallCategories = categories.filter(cat => cat.toolCount > 0 && cat.toolCount < 5 && cat.slug !== 'other');
    
    if (emptyCategories.length > 0) {
      console.log('Empty Categories:');
      emptyCategories.forEach(cat => {
        console.log(`  • ${cat.name} (${cat.slug})`);
      });
    }
    
    if (smallCategories.length > 0) {
      console.log('\\nCategories with < 5 tools:');
      smallCategories.forEach(cat => {
        console.log(`  • ${cat.name}: ${cat.toolCount} tools`);
      });
    }
    
    console.log('\\n✅ RECOMMENDATIONS');
    console.log('='.repeat(40));
    console.log('1. Consider merging empty categories with related ones');
    console.log('2. Review tools in "Other" category for further categorization');
    console.log('3. Consider creating subcategories for large categories (100+ tools)');
    console.log('4. Regularly audit and update tool categories as new tools are added');
    
    await mongoose.disconnect();
    console.log('\\n🎉 Category report generated successfully!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

generateCategoryReport();
