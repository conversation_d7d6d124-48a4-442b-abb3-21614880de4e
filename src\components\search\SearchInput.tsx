import React, { useState, useCallback } from 'react'
import { FaSearch, FaTimes } from 'react-icons/fa'
import { Input } from '@/components/ui/Input'
import { Button } from '@/components/ui/Button'

export interface SearchInputProps {
  placeholder?: string
  onSearch: (query: string) => void
  initialValue?: string
  className?: string
  autoFocus?: boolean
  debounceMs?: number
}

const SearchInput: React.FC<SearchInputProps> = ({
  placeholder = 'Search AI tools...',
  onSearch,
  initialValue = '',
  className,
  autoFocus = false,
  debounceMs = 300,
}) => {
  const [value, setValue] = useState(initialValue)
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null)

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setValue(newValue)

    // Clear previous timer
    if (debounceTimer) {
      clearTimeout(debounceTimer)
    }

    // Set new timer
    const timer = setTimeout(() => {
      onSearch(newValue)
    }, debounceMs)

    setDebounceTimer(timer)
  }, [debounceMs, debounceTimer, onSearch])

  const handleClear = useCallback(() => {
    setValue('')
    onSearch('')
  }, [onSearch])

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault()
    onSearch(value)
  }, [onSearch, value])

  return (
    <form onSubmit={handleSubmit} className={className}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <FaSearch className="h-4 w-4 text-gray-400" />
        </div>
        <Input
          type="text"
          value={value}
          onChange={handleChange}
          placeholder={placeholder}
          className="pl-10 pr-10"
          autoFocus={autoFocus}
        />
        {value && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={handleClear}
              aria-label="Clear search"
            >
              <FaTimes className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    </form>
  )
}

export default SearchInput 