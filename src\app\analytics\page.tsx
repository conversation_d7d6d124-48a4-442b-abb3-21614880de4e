'use client'

import React from 'react';
import Link from 'next/link';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

const AnalyticsPage: React.FC = () => {
  const stats = {
    totalViews: 1247,
    monthlyViews: 342,
    topTool: 'AI Content Generator Pro',
    conversionRate: '12.5%'
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-12">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-2">
            Analytics Dashboard
          </h1>
          <p className="text-lg text-muted-foreground">
            Track the performance of your submitted AI tools
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="p-6 text-center">
            <div className="text-3xl font-bold text-blue-500 mb-2">{stats.totalViews}</div>
            <div className="text-sm text-muted-foreground">Total Views</div>
          </Card>
          
          <Card className="p-6 text-center">
            <div className="text-3xl font-bold text-green-500 mb-2">{stats.monthlyViews}</div>
            <div className="text-sm text-muted-foreground">This Month</div>
          </Card>
          
          <Card className="p-6 text-center">
            <div className="text-3xl font-bold text-purple-500 mb-2">{stats.conversionRate}</div>
            <div className="text-sm text-muted-foreground">Click Rate</div>
          </Card>
          
          <Card className="p-6 text-center">
            <div className="text-3xl font-bold text-orange-500 mb-2">3</div>
            <div className="text-sm text-muted-foreground">Active Tools</div>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-foreground mb-6">Views Over Time</h2>
            <div className="h-64 bg-muted/30 rounded-lg flex items-center justify-center">
              <p className="text-muted-foreground">Chart visualization coming soon</p>
            </div>
          </Card>
          
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-foreground mb-6">Top Performing Tools</h2>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                <span className="font-medium">AI Content Generator Pro</span>
                <span className="text-blue-500 font-semibold">847 views</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                <span className="font-medium">Smart Image Enhancer</span>
                <span className="text-green-500 font-semibold">234 views</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                <span className="font-medium">Voice Recognition API</span>
                <span className="text-purple-500 font-semibold">166 views</span>
              </div>
            </div>
          </Card>
        </div>

        {/* Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-foreground mb-4">Export Data</h2>
            <p className="text-muted-foreground mb-4">
              Download your analytics data in various formats for further analysis.
            </p>
            <div className="space-y-2">
              <Button className="w-full" onClick={() => alert('CSV export coming soon!')}>
                Export as CSV
              </Button>
              <Button variant="outline" className="w-full" onClick={() => alert('PDF export coming soon!')}>
                Export as PDF
              </Button>
            </div>
          </Card>
          
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-foreground mb-4">Quick Actions</h2>
            <p className="text-muted-foreground mb-4">
              Manage your tools and submissions from here.
            </p>
            <div className="space-y-2">
              <Link href="/submit-tool">
                <Button className="w-full">Submit New Tool</Button>
              </Link>
              <Link href="/dashboard">
                <Button variant="outline" className="w-full">Back to Dashboard</Button>
              </Link>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsPage;
