// Simple test to check what's happening on Vercel
console.log('Environment check:')
console.log('NODE_ENV:', process.env.NODE_ENV)
console.log('MONGODB_URI exists:', !!process.env.MONGODB_URI)
console.log('MONGODB_URI length:', process.env.MONGODB_URI?.length || 0)
console.log('ANTI_SCRAPING_ENABLED:', process.env.ANTI_SCRAPING_ENABLED)

// Test if we can at least import mongoose
try {
  const mongoose = require('mongoose')
  console.log('Mongoose imported successfully')
  console.log('Mongoose connection state:', mongoose.connection.readyState)
  console.log('Connection states: 0=disconnected, 1=connected, 2=connecting, 3=disconnecting')
} catch (error) {
  console.log('Mongoose import error:', error.message)
}
