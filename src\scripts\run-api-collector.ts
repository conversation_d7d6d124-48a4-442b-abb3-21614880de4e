import { runAPICollector } from '../services/apiToolCollector';

async function main() {
  try {
    console.log('🔗 AI Tools API Collector - Starting Data Collection');
    console.log('='.repeat(60));
    
    await runAPICollector();
    
    console.log('='.repeat(60));
    console.log('✅ API collection completed successfully!');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ API collection failed:', error);
    process.exit(1);
  }
}

main();
