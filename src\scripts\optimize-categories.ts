import mongoose from 'mongoose';
import Category from '../models/Category.js';
import Tool from '../models/Tool.js';

async function optimizeCategories() {
  try {
    console.log('🔗 Connecting to database...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aitools');

    console.log('🧹 Optimizing category structure...');

    // 1. Merge empty categories into related ones
    const emptyCategories = [
      'video-editing',
      'video-generation', 
      'audio-ai',
      'data-analysis',
      'business',
      'security-privacy'
    ];

    // 2. Category merging rules
    const categoryMergeRules = new Map([
      // Merge Video Editing & Video Generation into Video Tools
      ['video-editing', 'video-tools'],
      ['video-generation', 'video-tools'],
      
      // Merge Audio AI into Music & Audio Creation
      ['audio-ai', 'music-audio-creation'],
      
      // Merge Data Analysis into Research & Analytics
      ['data-analysis', 'research-analytics'],
      
      // Keep Business and Security & Privacy for future use (don't delete, just mark as inactive)
    ]);

    // 3. Update category descriptions and consolidate
    const categoryUpdates = [
      {
        slug: 'video-tools',
        name: 'Video & Video Creation',
        description: 'AI tools for video editing, generation, processing, and manipulation'
      },
      {
        slug: 'music-audio-creation', 
        name: 'Music & Audio',
        description: 'AI tools for generating, editing, processing music, audio, and speech'
      },
      {
        slug: 'research-analytics',
        name: 'Research & Data Analysis', 
        description: 'AI tools for research, data analysis, analytics, and insights'
      }
    ];

    // Apply category updates
    for (const update of categoryUpdates) {
      await Category.findOneAndUpdate(
        { slug: update.slug },
        { 
          name: update.name,
          description: update.description,
          updatedAt: new Date()
        }
      );
      console.log(`✅ Updated category: ${update.name}`);
    }

    // Mark empty categories as inactive instead of deleting (preserve data integrity)
    for (const categorySlug of emptyCategories) {
      const category = await Category.findOne({ slug: categorySlug });
      if (category) {
        // Add a flag to mark as inactive
        await Category.findByIdAndUpdate(category._id, { 
          order: 999, // Move to end
          name: `${category.name} (Inactive)`,
          updatedAt: new Date()
        });
        console.log(`⏸️ Marked inactive: ${category.name}`);
      }
    }

    // 4. Update tool counts for all categories
    console.log('\\n📊 Updating all category tool counts...');
    const allCategories = await Category.find({});
    
    for (const category of allCategories) {
      const toolCount = await Tool.countDocuments({ category: category._id });
      await Category.findByIdAndUpdate(category._id, { toolCount });
      
      if (toolCount > 0) {
        console.log(`📊 ${category.name}: ${toolCount} tools`);
      }
    }

    // 5. Generate final summary
    const totalTools = await Tool.countDocuments({});
    const otherCategory = await Category.findOne({ slug: 'other' });
    const otherToolCount = otherCategory ? await Tool.countDocuments({ category: otherCategory._id }) : 0;
    const categorizedTools = totalTools - otherToolCount;
    const categorizationRate = ((categorizedTools / totalTools) * 100).toFixed(1);

    console.log('\\n🎉 FINAL CATEGORIZATION SUMMARY');
    console.log('=====================================');
    console.log(`Total Tools: ${totalTools}`);
    console.log(`Properly Categorized: ${categorizedTools} (${categorizationRate}%)`);
    console.log(`Remaining in Other: ${otherToolCount} (${((otherToolCount/totalTools)*100).toFixed(1)}%)`);
    console.log(`Improvement from start: ${((971-otherToolCount)/971*100).toFixed(1)}% reduction in uncategorized tools`);

    await mongoose.disconnect();
    console.log('\\n✅ Category optimization complete!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

optimizeCategories();
