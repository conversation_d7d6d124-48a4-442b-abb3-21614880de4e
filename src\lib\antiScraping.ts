import { NextRequest, NextResponse } from 'next/server'
import crypto from 'crypto'

interface RateLimitData {
  count: number
  resetTime: number
  suspiciousScore: number
}

// Enhanced rate limiting with Redis-like behavior (in-memory for demo)
const apiRequestCounts = new Map<string, RateLimitData>()

// Track request patterns
const requestPatterns = new Map<string, Array<{ timestamp: number, endpoint: string }>>()

export class AntiScrapingService {
  private static instance: AntiScrapingService
  
  public static getInstance(): AntiScrapingService {
    if (!AntiScrapingService.instance) {
      AntiScrapingService.instance = new AntiScrapingService()
    }
    return AntiScrapingService.instance
  }
  
  // Check if request is suspicious
  public isSuspiciousRequest(request: NextRequest, ip: string): { 
    isSuspicious: boolean, 
    reason?: string,
    score: number 
  } {
    let suspiciousScore = 0
    let reason = ''
    
    const userAgent = request.headers.get('user-agent') || ''
    const referer = request.headers.get('referer') || ''
    const acceptHeader = request.headers.get('accept') || ''
    
    // Check 1: Missing or suspicious user agent
    if (!userAgent || userAgent.length < 10) {
      suspiciousScore += 30
      reason += 'Missing/short user agent. '
    }
    
    // Check 2: No referer for API calls (legitimate users usually have referer)
    if (request.nextUrl.pathname.startsWith('/api/') && !referer) {
      suspiciousScore += 20
      reason += 'API call without referer. '
    }
    
    // Check 3: Programmatic accept headers
    if (acceptHeader.includes('application/json') && !acceptHeader.includes('text/html')) {
      suspiciousScore += 15
      reason += 'JSON-only accept header. '
    }
    
    // Check 4: Request pattern analysis
    const patterns = requestPatterns.get(ip) || []
    if (patterns.length > 5) {
      const recentRequests = patterns.filter(p => Date.now() - p.timestamp < 60000) // Last minute
      
      // Too many requests to different endpoints
      const uniqueEndpoints = new Set(recentRequests.map(p => p.endpoint)).size
      if (uniqueEndpoints > 10) {
        suspiciousScore += 25
        reason += 'Too many different endpoints. '
      }
      
      // Sequential pattern (typical of scrapers)
      const isSequential = this.isSequentialPattern(recentRequests.map(p => p.endpoint))
      if (isSequential) {
        suspiciousScore += 35
        reason += 'Sequential access pattern. '
      }
    }
    
    // Check 5: No JavaScript execution indicators
    const hasJsHeaders = request.headers.get('x-requested-with') || 
                        request.headers.get('x-csrf-token') ||
                        request.headers.get('x-client-id')
    if (request.nextUrl.pathname.startsWith('/api/') && !hasJsHeaders) {
      suspiciousScore += 10
      reason += 'No JS execution indicators. '
    }
    
    return {
      isSuspicious: suspiciousScore >= 50,
      reason: reason.trim(),
      score: suspiciousScore
    }
  }
  
  private isSequentialPattern(endpoints: string[]): boolean {
    if (endpoints.length < 5) return false
    
    // Check for patterns like /api/tools?page=1, /api/tools?page=2, etc.
    let sequentialCount = 0
    for (let i = 1; i < endpoints.length; i++) {
      const current = endpoints[i]
      const previous = endpoints[i - 1]
      
      if (current.includes('page=') && previous.includes('page=')) {
        const currentPage = this.extractPageNumber(current)
        const previousPage = this.extractPageNumber(previous)
        
        if (currentPage === previousPage + 1) {
          sequentialCount++
        }
      }
    }
    
    return sequentialCount >= 3
  }
  
  private extractPageNumber(url: string): number {
    const match = url.match(/page=(\d+)/)
    return match ? parseInt(match[1]) : 0
  }
  
  // Track request patterns
  public trackRequest(ip: string, endpoint: string) {
    const patterns = requestPatterns.get(ip) || []
    patterns.push({ timestamp: Date.now(), endpoint })
    
    // Keep only last 50 requests per IP
    if (patterns.length > 50) {
      patterns.splice(0, patterns.length - 50)
    }
    
    requestPatterns.set(ip, patterns)
  }
  
  // Enhanced rate limiting
  public checkRateLimit(ip: string): { allowed: boolean, retryAfter?: number } {
    const now = Date.now()
    const windowMs = 60 * 1000 // 1 minute window
    const maxRequests = 20 // Reduced from 30
    
    const currentData = apiRequestCounts.get(ip)
    
    if (!currentData || now > currentData.resetTime) {
      apiRequestCounts.set(ip, { 
        count: 1, 
        resetTime: now + windowMs,
        suspiciousScore: 0
      })
      return { allowed: true }
    }
    
    currentData.count++
    
    if (currentData.count > maxRequests) {
      const retryAfter = Math.ceil((currentData.resetTime - now) / 1000)
      return { allowed: false, retryAfter }
    }
    
    return { allowed: true }
  }
  
  // Generate dynamic tokens for legitimate users
  public generateClientToken(): string {
    const timestamp = Date.now().toString()
    const random = crypto.randomBytes(16).toString('hex')
    const hash = crypto.createHash('sha256').update(timestamp + random).digest('hex')
    return Buffer.from(`${timestamp}:${hash}`).toString('base64')
  }
  
  // Validate client token
  public validateClientToken(token: string): boolean {
    try {
      const decoded = Buffer.from(token, 'base64').toString()
      const [timestamp, hash] = decoded.split(':')
      
      const now = Date.now()
      const tokenTime = parseInt(timestamp)
      
      // Token valid for 1 hour
      if (now - tokenTime > 3600000) {
        return false
      }
      
      // Additional validation could be added here
      return true
    } catch {
      return false
    }
  }
}

export default AntiScrapingService
