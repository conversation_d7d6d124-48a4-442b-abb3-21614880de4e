import dbConnect from './src/lib/db.js';
import Tool from './src/models/Tool.js';

async function fixPlaceholderImages() {
  try {
    console.log('🔍 Connecting to database...');
    await dbConnect();
    
    console.log('🔍 Checking for tools with via.placeholder.com URLs...');
    
    const toolsWithPlaceholder = await Tool.find({ 
      imageUrl: { $regex: 'via.placeholder.com', $options: 'i' } 
    });
    
    console.log(`Found ${toolsWithPlaceholder.length} tools with via.placeholder.com URLs`);
    
    if (toolsWithPlaceholder.length > 0) {
      console.log('🔧 Updating tools to use local placeholder...');
      
      const result = await Tool.updateMany(
        { imageUrl: { $regex: 'via.placeholder.com', $options: 'i' } },
        { $set: { imageUrl: '/images/tool-placeholder.svg' } }
      );
      
      console.log(`✅ Updated ${result.modifiedCount} tools`);
    } else {
      console.log('✅ No tools found with via.placeholder.com URLs');
    }
    
    // Also check for any empty or null imageUrls
    const toolsWithNoImage = await Tool.find({ 
      $or: [
        { imageUrl: { $exists: false } },
        { imageUrl: null },
        { imageUrl: '' }
      ]
    });
    
    console.log(`Found ${toolsWithNoImage.length} tools with no image URL`);
    
    if (toolsWithNoImage.length > 0) {
      const result2 = await Tool.updateMany(
        { $or: [
          { imageUrl: { $exists: false } },
          { imageUrl: null },
          { imageUrl: '' }
        ]},
        { $set: { imageUrl: '/images/tool-placeholder.svg' } }
      );
      
      console.log(`✅ Updated ${result2.modifiedCount} tools with empty image URLs`);
    }
    
    console.log('🎉 All done!');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

fixPlaceholderImages();
