import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Navigation from "@/components/ui/Navigation";
import Footer from "@/components/ui/Footer";
import AntiScrapingWrapper from "@/components/ui/AntiScrapingWrapper";
import AuthProvider from "@/components/providers/AuthProvider";
import StructuredData from "@/components/seo/StructuredData";
import Analytics from "@/components/seo/Analytics";
import WebVitals from "@/components/seo/WebVitals";
import { generateHomePageMetadata, generateWebsiteJsonLd, generateOrganizationJsonLd } from "@/lib/seo";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = generateHomePageMetadata();

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        {/* Advanced SEO Meta Tags */}
        <meta name="robots" content="index, follow, max-snippet:150, max-image-preview:large" />
        <meta name="googlebot" content="index, follow" />
        <meta name="bingbot" content="index, follow" />
        <meta name="theme-color" content="#3B82F6" />
        <meta name="msapplication-TileColor" content="#3B82F6" />
        <link rel="canonical" href={process.env.NEXT_PUBLIC_SITE_URL || 'https://aitools.directory'} />
        
        {/* Security Headers */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        
        {/* Structured Data for Homepage */}
        <StructuredData data={[generateWebsiteJsonLd(), generateOrganizationJsonLd()]} />
        
        {/* Analytics */}
        <Analytics 
          googleAnalyticsId={process.env.GOOGLE_ANALYTICS_ID}
          googleTagManagerId={process.env.GOOGLE_TAG_MANAGER_ID}
        />
        
        {/* Web Vitals Tracking */}
        <WebVitals />
        
        {/* Honeypot for scrapers - only when anti-scraping is enabled */}
        <link rel="stylesheet" href="/css/honeypot.css" />
        {process.env.NODE_ENV !== 'development' && process.env.ANTI_SCRAPING_ENABLED === 'true' && (
          <script
            dangerouslySetInnerHTML={{
              __html: `
                // Basic bot detection
                if (navigator.webdriver || window.navigator.webdriver) {
                  window.location.href = '/blocked';
                }
                
                // Create honeypot elements
                const honeypot = document.createElement('div');
                honeypot.className = 'honeypot-trap';
                honeypot.innerHTML = '<a href="/admin/secret">Admin Panel</a>';
                honeypot.style.display = 'none';
                document.body?.appendChild(honeypot);
              `
            }}
          />
        )}
      </head>
      <body className={inter.className}>
        <AuthProvider>
          <AntiScrapingWrapper>
            <div className="min-h-screen flex flex-col">
              <Navigation />
              <main className="flex-grow">
                {children}
              </main>
              <Footer />
            </div>
          </AntiScrapingWrapper>
        </AuthProvider>
      </body>
    </html>
  );
}
