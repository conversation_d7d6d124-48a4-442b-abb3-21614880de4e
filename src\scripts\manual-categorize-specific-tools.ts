import mongoose from 'mongoose';
import Tool from '../models/Tool.js';
import Category from '../models/Category.js';

async function manualCategorizeSpecificTools() {
  try {
    console.log('🔗 Connecting to database...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aitools');

    // Get all categories
    const categories = await Category.find({});
    const categoryMap = new Map();
    categories.forEach(cat => {
      categoryMap.set(cat.slug, cat._id);
    });

    // Get "Other" category
    const otherCategory = await Category.findOne({ slug: 'other' });
    if (!otherCategory) {
      console.log('❌ Other category not found');
      return;
    }

    // Get all tools in "Other" category
    const otherTools = await Tool.find({ category: otherCategory._id });
    console.log(`🔍 Found ${otherTools.length} tools in Other category`);

    // Manual categorization rules for specific tools
    const specificCategorizations = new Map([
      // Sports & Fitness
      ['Mysports AI', 'healthcare-fitness'],
      ['HoopsAI', 'healthcare-fitness'],
      
      // Fashion & Style
      ['AISuitUP', 'design-graphics'],
      ['Westidol', 'design-graphics'],
      
      // Social & Dating
      ['Fakehn', 'gaming-entertainment'],
      
      // Language Models (specific models)
      ['moonshotai/Kimi-K2-Instruct', 'language-models'],
      ['moonshotai/Kimi-K2-Base', 'language-models'],
      ['THUDM/GLM-4.1V-9B-Thinking', 'language-models'],
      ['LiquidAI/LFM2-1.2B', 'language-models'],
      ['LiquidAI/LFM2-350M', 'language-models'],
      ['LiquidAI/LFM2-700M', 'language-models'],
      ['HuggingFaceTB/SmolLM3-3B', 'language-models'],
      ['HuggingFaceTB/SmolLM3-3B-Base', 'language-models'],
      ['microsoft/Phi-4-mini-flash-reasoning', 'language-models'],
      ['RekaAI/reka-flash-3.1', 'language-models'],
      ['Skywork/Skywork-R1V3-38B', 'language-models'],
      ['tencent/Hunyuan-A13B-Instruct', 'language-models'],
      ['deepseek-ai/DeepSeek-R1', 'language-models'],
      
      // OCR and Document Processing
      ['nanonets/Nanonets-OCR-s', 'document-management'],
      
      // Embeddings and Vector Tools
      ['jinaai/jina-embeddings-v4', 'api-developer-tools'],
      
      // TTS and Audio
      ['kyutai/tts-1.6b-en_fr', 'music-audio-creation'],
      
      // Video Generation
      ['vrgamedevgirl84/Wan14BT2VFusioniX', 'video-tools'],
      
      // Chatbots
      ['Mebot', 'chatbots-ai-assistants'],
      ['Hexabot', 'chatbots-ai-assistants'],
      
      // Scheduling and Time
      ['WhenX', 'productivity'],
      
      // Analytics and Monitoring
      ['Reclaim.ai', 'productivity'],
      ['Pallyy', 'marketing-seo'],
      
      // Development Tools
      ['TensorFlow.js', 'api-developer-tools'],
      ['Brain.js', 'api-developer-tools'],
      ['ML5.js', 'api-developer-tools'],
      ['Algorithmia', 'api-developer-tools'],
      
      // Image Tools
      ['PicWish', 'photo-image-editing'],
      ['Clipdrop', 'photo-image-editing'],
      
      // Email Tools
      ['Mailr', 'email-communication'],
      
      // Meeting Tools
      ['SaneBox', 'email-communication'],
      ['MeetGeek', 'meeting-collaboration'],
      
      // Research Tools
      ['Gopher', 'research-analytics'],
      ['Bloom', 'research-analytics'],
      
      // Developer APIs
      ['OpenAI API', 'api-developer-tools'],
      
      // Specific AI Models
      ['Gemini', 'language-models'],
      ['Character.AI', 'chatbots-ai-assistants'],
      
      // Productivity Tools
      ['Nekton AI', 'ai-automation-workflow'],
      ['Elephas', 'productivity'],
      ['Lemmy', 'productivity'],
      
      // Content Tools
      ['CreateEasily', 'content-creation'],
      ['Summary With AI', 'content-creation'],
      ['Recall', 'research-analytics'],
      
      // HR Tools
      ['Talently AI', 'hr-recruitment'],
      
      // Business Intelligence
      ['Elicit', 'research-analytics'],
      ['genei', 'research-analytics'],
      ['Sourcely', 'research-analytics'],
      ['SciSpace', 'research-analytics'],
      
      // Customer Support
      ['SiteGPT', 'sales-customer-service'],
      ['SiteSpeakAI', 'sales-customer-service'],
      ['Aidbase', 'sales-customer-service'],
      
      // Writing Tools
      ['AI Poem Generator', 'content-creation'],
      
      // Development Infrastructure
      ['Haystack', 'api-developer-tools'],
      ['Keploy', 'api-developer-tools'],
      ['gpt4all', 'api-developer-tools'],
      ['LMQL', 'api-developer-tools'],
      ['Langfuse', 'api-developer-tools'],
      ['Phoenix', 'api-developer-tools'],
      ['Portkey', 'api-developer-tools'],
      
      // Cloud Services
      ['SinglebaseCloud', 'api-developer-tools'],
      ['Maxim AI', 'api-developer-tools'],
      
      // Code Tools
      ['Wordware', 'code-generation'],
      ['Pagerly', 'code-generation'],
      ['Plandex', 'code-generation'],
      ['Stenography', 'code-generation'],
      ['Mintlify', 'code-generation'],
      ['Debuild', 'code-generation'],
      ['CodiumAI', 'code-generation'],
      ['MutableAI', 'code-generation'],
      ['TurboPilot', 'code-generation'],
      ['AI Kernel Explorer', 'code-generation'],
      ['WhoDB', 'database-data-management'],
      
      // Image Generation
      ['Make-A-Scene', 'image-generation'],
      ['DragGAN', 'image-generation'],
      ['Craiyon', 'image-generation'],
      ['DreamStudio', 'image-generation'],
      ['GauGAN2', 'image-generation'],
      ['Magic Eraser', 'photo-image-editing'],
      ['Imagine by Magic Studio', 'image-generation'],
      
      // Productivity and Organization
      ['Alpaca', 'productivity'],
      ['Patience.ai', 'productivity'],
      ['modyfi', 'design-graphics'],
      ['Ponzu', 'design-graphics'],
      ['PhotoRoom', 'photo-image-editing'],
      ['Lensa', 'photo-image-editing'],
      ['Human Generator', 'image-generation'],
      ['StockPhotoAI.net', 'image-generation'],
      ['Room Reinvented', 'real-estate'],
      ['Gensbot', 'image-generation'],
      ['PlantPhotoAI', 'image-generation'],
      ['RepublicLabs.AI', 'image-generation'],
      ['Black Headshots', 'photo-image-editing'],
      ['Pixvify AI', 'photo-image-editing'],
      ['Pawtrait', 'photo-image-editing'],
      ['iColoring', 'gaming-entertainment'],
      ['Suit me Up', 'design-graphics'],
      ['AI Photo Forge', 'photo-image-editing'],
      
      // Logo and Branding
      ['SVGStud.io', 'design-graphics'],
      ['Lexica', 'image-generation'],
      ['Libraire', 'image-generation'],
      ['KREA', 'image-generation'],
      ['Phygital', 'design-graphics'],
      ['Civitai', 'image-generation'],
      ['Stable Horde', 'image-generation'],
      ['Stableboost', 'image-generation'],
      
      // Video Tools
      ['Hour One', 'video-tools'],
      ['D-ID', 'video-tools'],
      ['ShortVideoGen', 'video-tools'],
      ['Recast Studio', 'video-tools'],
      ['Based AI', 'video-tools'],
      ['Descript Overdub', 'video-tools'],
      
      // Audio Tools
      ['WellSaid Labs', 'music-audio-creation'],
      ['Lovo.ai', 'music-audio-creation'],
      ['Zenmic.com', 'music-audio-creation'],
      ['Splash Pro', 'music-audio-creation'],
      ['AIVA', 'music-audio-creation'],
      ['Beatoven.ai', 'music-audio-creation'],
      ['Boomy', 'music-audio-creation'],
      ['Loudly', 'music-audio-creation'],
      
      // Marketing Tools
      ['Mutiny', 'marketing-seo'],
      ['Seventh Sense', 'marketing-seo'],
      ['Adzooma', 'marketing-seo'],
      ['Phrasee', 'marketing-seo'],
      ['Crimson Hexagon', 'marketing-seo'],
      
      // Customer Service
      ['LogicBalls', 'sales-customer-service'],
      ['Rupert AI', 'sales-customer-service'],
      ['PersonaForce', 'sales-customer-service'],
      ['AICaller.io', 'sales-customer-service'],
      ['Cald.ai', 'sales-customer-service'],
      ['Rosie', 'sales-customer-service'],
      
      // Audio Content
      ['Coqui', 'music-audio-creation'],
      ['podcast.ai', 'music-audio-creation'],
      ['VALL-E X', 'music-audio-creation'],
      ['TorToiSe', 'music-audio-creation'],
      ['CustomPod.io', 'music-audio-creation'],
      
      // Productivity and Organization
      ['Clickable', 'marketing-seo'],
      ['Scale Spellbook', 'productivity'],
      ['Teleprompter', 'video-tools'],
      
      // Prompting and Content
      ['PromptBase', 'content-creation'],
      ['This Image Does Not Exist', 'image-generation'],
      ['Have I Been Trained?', 'research-analytics'],
      
      // Gaming
      ['AI Dungeon', 'gaming-entertainment'],
      
      // Financial Tools
      ['Scenario', 'finance-investment'],
      
      // Social Tools
      ['Aispect', 'marketing-seo'],
      ['PressPulse AI', 'marketing-seo'],
      ['Taplio', 'marketing-seo'],
      
      // AI Content Detection
      ['PromptPal', 'content-creation'],
      ['FairyTailAI', 'gaming-entertainment'],
      ['Myriad', 'design-graphics'],
      
      // Educational
      ['GradGPT', 'education'],
      ['Architecture Helper', 'education'],
      
      // Voice and Audio
      ['VocalReplica', 'music-audio-creation'],
      ['AI Wedding Toast', 'content-creation'],
      
      // Data and Context
      ['Context Data', 'research-analytics'],
      ['ezJobs', 'hr-recruitment'],
      ['Compass', 'research-analytics'],
      ['Adon AI', 'marketing-seo'],
      ['Persuva', 'marketing-seo'],
      ['Socialsonic', 'marketing-seo'],
      ['Napkin', 'design-graphics'],
      ['Exam Samurai', 'education'],
      
      // Utilities
      ['AISaver', 'productivity'],
      ['Harbor', 'productivity'],
      ['LangMagic', 'translation-language'],
      ['fynk', 'productivity'],
      ['LooksMax AI', 'photo-image-editing']
    ]);

    let updatedCount = 0;
    let errors = 0;

    // Apply specific categorizations
    for (const tool of otherTools) {
      try {
        if (specificCategorizations.has(tool.name)) {
          const newCategorySlug = specificCategorizations.get(tool.name);
          
          if (categoryMap.has(newCategorySlug)) {
            const newCategoryId = categoryMap.get(newCategorySlug);
            await Tool.findByIdAndUpdate(tool._id, { category: newCategoryId });
            console.log(`✅ Updated ${tool.name}: ${newCategorySlug}`);
            updatedCount++;
          }
        }
      } catch (error) {
        console.error(`❌ Error processing tool ${tool.name}:`, error);
        errors++;
      }
    }

    // Update category tool counts
    console.log('\\n📊 Updating category tool counts...');
    for (const category of categories) {
      const toolCount = await Tool.countDocuments({ category: category._id });
      await Category.findByIdAndUpdate(category._id, { toolCount });
    }

    await mongoose.disconnect();
    
    console.log('\\n📊 Manual Categorization Summary:');
    console.log(`✅ Updated: ${updatedCount} tools`);
    console.log(`❌ Errors: ${errors} tools`);
    console.log('\\n🎉 Manual categorization complete!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

manualCategorizeSpecificTools();
