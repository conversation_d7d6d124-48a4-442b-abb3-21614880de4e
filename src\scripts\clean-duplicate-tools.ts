import dbConnect from '../lib/db';
import Tool from '../models/Tool';

interface ToolDoc {
  _id: string;
  name: string;
  url?: string;
  description?: string;
  createdAt: Date;
}

async function cleanDuplicateTools() {
  try {
    console.log('🔍 Connecting to database...');
    await dbConnect();

    console.log('📊 Analyzing tools for duplicates...');
    
    // Find all tools
    const allTools = await Tool.find({}).lean();
    console.log(`📈 Total tools found: ${allTools.length}`);

    // Group tools by URL (primary identifier)
    const toolsByUrl = new Map<string, any[]>();
    const toolsByName = new Map<string, any[]>();
    
    for (const tool of allTools) {
      // Group by URL if exists
      if (tool.url) {
        const normalizedUrl = tool.url.toLowerCase().replace(/\/$/, '');
        if (!toolsByUrl.has(normalizedUrl)) {
          toolsByUrl.set(normalizedUrl, []);
        }
        toolsByUrl.get(normalizedUrl)!.push(tool);
      }
      
      // Group by name as fallback
      const normalizedName = tool.name.toLowerCase().trim();
      if (!toolsByName.has(normalizedName)) {
        toolsByName.set(normalizedName, []);
      }
      toolsByName.get(normalizedName)!.push(tool);
    }

    // Find duplicates by URL
    const urlDuplicates = Array.from(toolsByUrl.entries())
      .filter(([_, tools]) => tools.length > 1);

    // Find duplicates by name (excluding those already found by URL)
    const urlDuplicateIds = new Set(
      urlDuplicates.flatMap(([_, tools]) => tools.map(t => t._id.toString()))
    );
    
    const nameDuplicates = Array.from(toolsByName.entries())
      .filter(([_, tools]) => tools.length > 1)
      .filter(([_, tools]) => !tools.every(t => urlDuplicateIds.has(t._id.toString())));

    console.log(`🔍 Found ${urlDuplicates.length} URL-based duplicate groups`);
    console.log(`🔍 Found ${nameDuplicates.length} name-based duplicate groups`);

    let totalRemoved = 0;
    const idsToRemove: string[] = [];

    // Process URL duplicates
    for (const [url, duplicateTools] of urlDuplicates) {
      console.log(`\n📝 Processing ${duplicateTools.length} duplicates for URL: ${url}`);
      
      // Sort by creation date (keep oldest)
      duplicateTools.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
      
      const keepTool = duplicateTools[0];
      const removeDuplicates = duplicateTools.slice(1);
      
      console.log(`  ✅ Keeping: ${keepTool.name} (ID: ${keepTool._id})`);
      
      for (const duplicate of removeDuplicates) {
        console.log(`  ❌ Removing: ${duplicate.name} (ID: ${duplicate._id})`);
        idsToRemove.push(duplicate._id.toString());
      }
      
      totalRemoved += removeDuplicates.length;
    }

    // Process name duplicates (more careful approach)
    for (const [name, duplicateTools] of nameDuplicates) {
      console.log(`\n📝 Processing ${duplicateTools.length} name duplicates for: ${name}`);
      
      // Check if they have different URLs - if so, they might be legitimate separate tools
      const uniqueUrls = new Set(duplicateTools.map(t => t.url).filter(Boolean));
      
      if (uniqueUrls.size > 1) {
        console.log(`  ⚠️  Skipping - tools have different URLs, likely different services`);
        continue;
      }
      
      // Sort by creation date (keep oldest)
      duplicateTools.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
      
      const keepTool = duplicateTools[0];
      const removeDuplicates = duplicateTools.slice(1);
      
      console.log(`  ✅ Keeping: ${keepTool.name} (ID: ${keepTool._id})`);
      
      for (const duplicate of removeDuplicates) {
        console.log(`  ❌ Removing: ${duplicate.name} (ID: ${duplicate._id})`);
        idsToRemove.push(duplicate._id.toString());
      }
      
      totalRemoved += removeDuplicates.length;
    }

    // Perform batch deletion
    if (idsToRemove.length > 0) {
      console.log(`\n🗑️  Removing ${idsToRemove.length} duplicate tools...`);
      
      // Remove in batches to avoid overwhelming the database
      const batchSize = 100;
      for (let i = 0; i < idsToRemove.length; i += batchSize) {
        const batch = idsToRemove.slice(i, i + batchSize);
        await Tool.deleteMany({ _id: { $in: batch } });
        console.log(`  ✅ Removed batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(idsToRemove.length / batchSize)}`);
      }
    }

    // Verify final count
    const finalCount = await Tool.countDocuments();
    console.log(`\n📊 Cleanup Summary:`);
    console.log(`  • Initial tools: ${allTools.length}`);
    console.log(`  • Duplicates removed: ${totalRemoved}`);
    console.log(`  • Final tools: ${finalCount}`);
    console.log(`  • Expected final count: ${allTools.length - totalRemoved}`);

    if (finalCount === allTools.length - totalRemoved) {
      console.log(`✅ Cleanup completed successfully!`);
    } else {
      console.log(`⚠️  Warning: Final count mismatch!`);
    }

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    process.exit(1);
  }
}

// Run the cleanup
cleanDuplicateTools()
  .then(() => {
    console.log('🎉 Duplicate cleanup completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Cleanup failed:', error);
    process.exit(1);
  });
