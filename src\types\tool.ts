export interface Tool {
  _id: string;
  name: string;
  description: string;
  slug?: string;
  category: string | { _id: string; name: string; slug: string };
  rating: number;
  ratingCount?: number;
  imageUrl?: string;
  pricing?: string;
  url?: string;
  features?: string[];
  tags?: string[];
  apiAvailable?: boolean;
  status?: 'active' | 'inactive';
  createdAt?: string;
  updatedAt?: string;
  company?: string;
  source?: string;
}
