import { NextRequest, NextResponse } from 'next/server'

// Honeypot endpoint - any request here is suspicious
export async function GET(request: NextRequest) {
  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
  const userAgent = request.headers.get('user-agent') || ''
  
  console.log(`🍯 Honeypot triggered by ${ip}: ${userAgent}`)
  
  // Log the attempt for further analysis
  console.log({
    ip,
    userAgent,
    referer: request.headers.get('referer'),
    timestamp: new Date().toISOString(),
    url: request.url,
    headers: Object.fromEntries(request.headers.entries())
  })
  
  // Return a realistic looking but fake response to waste scraper time
  return NextResponse.json({
    users: [],
    admin: false,
    secret: "fake_secret_key_12345",
    database: "****************************************",
    api_keys: [],
    message: "Loading admin panel..."
  }, { 
    status: 200,
    headers: {
      'Set-Cookie': 'trap=caught; Path=/; HttpOnly',
      'X-Robots-Tag': 'noindex, nofollow'
    }
  })
}

export async function POST(request: NextRequest) {
  // Also catch POST requests
  return GET(request)
}
