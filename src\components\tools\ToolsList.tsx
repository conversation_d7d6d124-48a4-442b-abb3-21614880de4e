'use client'

import React, { useState, useCallback, useMemo, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import ToolCard from './ToolCard'
import { Button } from '@/components/ui/Button'
import { fetchTools } from '@/services/toolsService'
import { useDebounce } from '@/hooks/useDebounce'
import { Tool } from '@/types/tool'

interface ToolsListProps {
  initialQuery?: string
  initialCategory?: string
  initialSortBy?: 'rating' | 'name'
  initialPage?: number
}

const ToolsList: React.FC<ToolsListProps> = ({ 
  initialQuery = '', 
  initialCategory = '', 
  initialSortBy = 'rating',
  initialPage = 1
}) => {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [tools, setTools] = useState<Tool[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState(initialQuery)
  const [selectedCategory, setSelectedCategory] = useState<string>(initialCategory)
  const [sortBy, setSortBy] = useState<'rating' | 'name'>(initialSortBy)
  const [page, setPage] = useState(initialPage)
  const [totalPages, setTotalPages] = useState(1)
  const [totalTools, setTotalTools] = useState(0)

  const debouncedSearch = useDebounce(searchQuery, 300)

  const updateURL = useCallback((newPage: number) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set('page', newPage.toString())
    router.push(`/tools?${params.toString()}`, { scroll: false })
  }, [router, searchParams])

  const loadTools = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetchTools({
        query: debouncedSearch,
        category: selectedCategory,
        sortBy,
        page,
        limit: 12
      })
      setTools(response.tools)
      setTotalPages(response.totalPages)
      setTotalTools(response.total)
    } catch (err) {
      setError('Failed to load tools. Please try again.')
      console.error('Error loading tools:', err)
    } finally {
      setLoading(false)
    }
  }, [debouncedSearch, selectedCategory, sortBy, page])

  useEffect(() => {
    loadTools()
  }, [loadTools])

  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage)
    updateURL(newPage)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }, [updateURL])

  if (error) {
    return (
      <div className="text-center py-12">
        <h3 className="text-xl font-medium text-red-600">{error}</h3>
        <Button 
          onClick={loadTools}
          variant="outline"
          className="mt-4"
        >
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div>
      {loading && tools.length === 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-card rounded-lg shadow-md p-4 h-64 animate-pulse">
              <div className="bg-muted h-32 rounded-md mb-4"></div>
              <div className="h-6 bg-muted rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-muted rounded w-1/2 mb-4"></div>
              <div className="h-4 bg-muted rounded w-full"></div>
            </div>
          ))}
        </div>
      ) : tools.length === 0 ? (
        <div className="text-center py-12">
          <h3 className="text-xl font-medium mb-2">No tools found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your search criteria or browse all tools.
          </p>
          <Button 
            onClick={() => {
              setSearchQuery('')
              setSelectedCategory('')
              setPage(1)
            }}
            variant="outline"
          >
            Clear Filters
          </Button>
        </div>
      ) : (
        <>
          <div className="mb-4 text-sm text-muted-foreground">
            Showing {tools.length} of {totalTools} tools
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {tools.map((tool) => (
              <ToolCard 
                key={tool._id} 
                id={tool._id}
                name={tool.name}
                description={tool.description}
                imageUrl={tool.imageUrl || ''}
                rating={tool.rating || 0}
                category={tool.category}
                pricing={tool.pricing as 'Free' | 'Freemium' | 'Paid' || 'Free'}
                url={tool.url || ''}
              />
            ))}
          </div>
          
          {totalPages > 1 && (
            <div className="flex justify-center mt-8 gap-2 flex-wrap">
              <Button
                variant="outline"
                onClick={() => handlePageChange(page - 1)}
                disabled={page === 1}
              >
                Previous
              </Button>
              
              {/* First page */}
              {page > 3 && (
                <>
                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(1)}
                  >
                    1
                  </Button>
                  {page > 4 && <span className="px-2 py-2">...</span>}
                </>
              )}
              
              {/* Pages around current page */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (page <= 3) {
                  pageNum = i + 1;
                } else if (page >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = page - 2 + i;
                }
                
                if (pageNum > 0 && pageNum <= totalPages) {
                  return (
                    <Button
                      key={pageNum}
                      variant={page === pageNum ? 'default' : 'outline'}
                      onClick={() => handlePageChange(pageNum)}
                    >
                      {pageNum}
                    </Button>
                  );
                }
                return null;
              })}
              
              {/* Last page */}
              {page < totalPages - 2 && (
                <>
                  {page < totalPages - 3 && <span className="px-2 py-2">...</span>}
                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(totalPages)}
                  >
                    {totalPages}
                  </Button>
                </>
              )}
              
              <Button
                variant="outline"
                onClick={() => handlePageChange(page + 1)}
                disabled={page === totalPages}
              >
                Next
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  )
}

export default ToolsList 