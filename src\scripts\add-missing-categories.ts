import mongoose from 'mongoose';
import Category from '../models/Category.js';

async function addMissingCategories() {
  try {
    console.log('🔗 Connecting to database...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aitools');

    const newCategories = [
      // AI Core Categories
      {
        name: 'Chatbots & AI Assistants',
        description: 'AI-powered conversational agents and virtual assistants',
        slug: 'chatbots-ai-assistants',
        icon: '🤖',
        order: 1
      },
      {
        name: 'AI Automation & Workflow',
        description: 'Tools for automating tasks and optimizing workflows',
        slug: 'ai-automation-workflow',
        icon: '⚡',
        order: 2
      },
      
      // Content & Media
      {
        name: 'Design & Graphics',
        description: 'AI tools for graphic design, logos, and visual content creation',
        slug: 'design-graphics',
        icon: '🎨',
        order: 10
      },
      {
        name: 'Photo & Image Editing',
        description: 'AI-powered photo editing, enhancement, and manipulation tools',
        slug: 'photo-image-editing',
        icon: '📸',
        order: 11
      },
      {
        name: 'Video Tools',
        description: 'AI tools for video editing, processing, and manipulation',
        slug: 'video-tools',
        icon: '🎬',
        order: 12
      },
      {
        name: 'Music & Audio Creation',
        description: 'AI tools for generating, editing, and producing music and audio',
        slug: 'music-audio-creation',
        icon: '🎵',
        order: 13
      },
      {
        name: 'Presentations & Slides',
        description: 'AI-powered presentation creation and slide design tools',
        slug: 'presentations-slides',
        icon: '📊',
        order: 14
      },
      
      // Business & Professional
      {
        name: 'Marketing & SEO',
        description: 'AI tools for digital marketing, SEO, and advertising',
        slug: 'marketing-seo',
        icon: '📈',
        order: 20
      },
      {
        name: 'Sales & Customer Service',
        description: 'AI tools for sales automation and customer support',
        slug: 'sales-customer-service',
        icon: '💼',
        order: 21
      },
      {
        name: 'HR & Recruitment',
        description: 'AI tools for human resources, hiring, and recruitment',
        slug: 'hr-recruitment',
        icon: '👥',
        order: 22
      },
      {
        name: 'Finance & Investment',
        description: 'AI tools for financial analysis, investment, and accounting',
        slug: 'finance-investment',
        icon: '💰',
        order: 23
      },
      {
        name: 'Legal & Compliance',
        description: 'AI tools for legal research, document review, and compliance',
        slug: 'legal-compliance',
        icon: '⚖️',
        order: 24
      },
      {
        name: 'E-commerce & Shopping',
        description: 'AI tools for online retail, product recommendations, and shopping',
        slug: 'ecommerce-shopping',
        icon: '🛒',
        order: 25
      },
      
      // Technical & Development
      {
        name: 'No-Code & Website Builders',
        description: 'AI-powered website and app building platforms',
        slug: 'nocode-website-builders',
        icon: '🔧',
        order: 30
      },
      {
        name: 'API & Developer Tools',
        description: 'AI APIs, SDKs, and development frameworks',
        slug: 'api-developer-tools',
        icon: '⚙️',
        order: 31
      },
      {
        name: 'Database & Data Management',
        description: 'AI tools for database management and data processing',
        slug: 'database-data-management',
        icon: '🗄️',
        order: 32
      },
      
      // Communication & Collaboration
      {
        name: 'Translation & Language',
        description: 'AI translation, language learning, and multilingual tools',
        slug: 'translation-language',
        icon: '🌐',
        order: 40
      },
      {
        name: 'Email & Communication',
        description: 'AI tools for email management and communication',
        slug: 'email-communication',
        icon: '📧',
        order: 41
      },
      {
        name: 'Meeting & Collaboration',
        description: 'AI tools for meetings, team collaboration, and project management',
        slug: 'meeting-collaboration',
        icon: '👫',
        order: 42
      },
      
      // Research & Analysis
      {
        name: 'Research & Analytics',
        description: 'AI tools for research, data analysis, and insights',
        slug: 'research-analytics',
        icon: '🔍',
        order: 50
      },
      {
        name: 'Document Management',
        description: 'AI tools for document processing, OCR, and file management',
        slug: 'document-management',
        icon: '📄',
        order: 51
      },
      
      // Specialized Categories
      {
        name: 'Gaming & Entertainment',
        description: 'AI tools for gaming, entertainment, and interactive experiences',
        slug: 'gaming-entertainment',
        icon: '🎮',
        order: 60
      },
      {
        name: 'Healthcare & Fitness',
        description: 'AI tools for health monitoring, medical analysis, and fitness',
        slug: 'healthcare-fitness',
        icon: '🏥',
        order: 61
      },
      {
        name: 'Travel & Tourism',
        description: 'AI tools for travel planning, booking, and tourism',
        slug: 'travel-tourism',
        icon: '✈️',
        order: 62
      },
      {
        name: 'Real Estate',
        description: 'AI tools for real estate analysis, property management, and valuation',
        slug: 'real-estate',
        icon: '🏠',
        order: 63
      },
      {
        name: '3D & AR/VR',
        description: 'AI tools for 3D modeling, augmented reality, and virtual reality',
        slug: '3d-ar-vr',
        icon: '🥽',
        order: 64
      },
      {
        name: 'Security & Privacy',
        description: 'AI tools for cybersecurity, privacy protection, and threat detection',
        slug: 'security-privacy',
        icon: '🔐',
        order: 65
      },
      
      // Adult/NSFW Category (keeping separate)
      {
        name: 'Adult & NSFW',
        description: 'AI tools for adult content (18+ only)',
        slug: 'adult-nsfw',
        icon: '🔞',
        order: 100
      }
    ];

    console.log(`🆕 Adding ${newCategories.length} new categories...`);

    for (const categoryData of newCategories) {
      try {
        // Check if category already exists
        const existingCategory = await Category.findOne({ slug: categoryData.slug });
        
        if (!existingCategory) {
          const category = new Category(categoryData);
          await category.save();
          console.log(`✅ Created category: ${categoryData.name}`);
        } else {
          console.log(`⏭️ Category already exists: ${categoryData.name}`);
        }
      } catch (error) {
        console.error(`❌ Error creating category ${categoryData.name}:`, error instanceof Error ? error.message : error);
      }
    }

    await mongoose.disconnect();
    console.log('\\n🎉 Missing categories added successfully!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

addMissingCategories();
