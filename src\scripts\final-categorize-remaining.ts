import mongoose from 'mongoose';
import Tool from '../models/Tool.js';
import Category from '../models/Category.js';

async function finalCategorizeRemaining() {
  try {
    console.log('🔗 Connecting to database...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aitools');

    // Get all categories
    const categories = await Category.find({});
    const categoryMap = new Map();
    categories.forEach(cat => {
      categoryMap.set(cat.slug, cat._id);
    });

    // Get "Other" category
    const otherCategory = await Category.findOne({ slug: 'other' });
    if (!otherCategory) {
      console.log('❌ Other category not found');
      return;
    }

    // Get all tools in "Other" category
    const otherTools = await Tool.find({ category: otherCategory._id });
    console.log(`🔍 Found ${otherTools.length} tools in Other category to recategorize`);

    let updatedCount = 0;
    let errors = 0;

    for (const tool of otherTools) {
      try {
        const name = tool.name.toLowerCase();
        const desc = (tool.description || '').toLowerCase();
        let newCategorySlug = null;

        // Enhanced categorization with more specific rules
        if (name.includes('llama') || name.includes('mistral') || name.includes('qwen') || 
            name.includes('gemma') || name.includes('gpt') || name.includes('t5') || 
            name.includes('opus') || name.includes('bert') || name.includes('falcon') ||
            name.includes('vicuna') || name.includes('beluga') || name.includes('deepseek') ||
            desc.includes('text generation') || desc.includes('language model')) {
          newCategorySlug = 'language-models';
        } else if (name.includes('stable-diffusion') || name.includes('flux') || name.includes('sdxl') ||
                   name.includes('diffusion') || name.includes('dall') || name.includes('imagen') ||
                   name.includes('playground') || name.includes('art') || name.includes('craiyon') ||
                   desc.includes('text-to-image') || desc.includes('image generation')) {
          newCategorySlug = 'image-generation';
        } else if (name.includes('trocr') || name.includes('ocr') || name.includes('donut') ||
                   name.includes('kosmos') || name.includes('blip') || name.includes('git-base') ||
                   desc.includes('image-text') || desc.includes('ocr') || desc.includes('captioning')) {
          newCategorySlug = 'document-management';
        } else if (name.includes('helsinki') || name.includes('nllb') || name.includes('madlad') ||
                   name.includes('indictrans') || desc.includes('translation') || desc.includes('mt-')) {
          newCategorySlug = 'translation-language';
        } else if (name.includes('tts') || name.includes('speech') || name.includes('audio') ||
                   name.includes('voice') || desc.includes('text-to-speech') || desc.includes('audio')) {
          newCategorySlug = 'music-audio-creation';
        } else if (name.includes('video') || desc.includes('video') || desc.includes('text-to-video')) {
          newCategorySlug = 'video-tools';
        } else if (name.includes('embedding') || name.includes('retrieval') || desc.includes('embedding')) {
          newCategorySlug = 'api-developer-tools';
        } else if (name.includes('tensorflow') || name.includes('ml5') || name.includes('brain.js') ||
                   name.includes('hugging') || name.includes('modal') || name.includes('gradio') ||
                   name.includes('streamlit') || name.includes('replicate') || name.includes('together')) {
          newCategorySlug = 'api-developer-tools';
        } else if (name.includes('pinecone') || name.includes('weaviate') || name.includes('chroma') ||
                   name.includes('llamaindex') || desc.includes('vector') || desc.includes('database')) {
          newCategorySlug = 'database-data-management';
        } else if (name.includes('weights') || name.includes('comet') || name.includes('langsmith') ||
                   desc.includes('experiment') || desc.includes('monitoring')) {
          newCategorySlug = 'api-developer-tools';
        } else if (name.includes('langchain') || name.includes('autogpt') || name.includes('agent') ||
                   desc.includes('workflow') || desc.includes('automation')) {
          newCategorySlug = 'ai-automation-workflow';
        } else if (name.includes('chat') || name.includes('gpt') || name.includes('claude') ||
                   name.includes('gemini') || name.includes('bard') || desc.includes('chat') ||
                   desc.includes('assistant') || desc.includes('conversation')) {
          newCategorySlug = 'chatbots-ai-assistants';
        } else if (name.includes('code') || name.includes('programming') || name.includes('github') ||
                   name.includes('copilot') || desc.includes('code') || desc.includes('programming')) {
          newCategorySlug = 'code-generation';
        } else if (name.includes('write') || name.includes('content') || name.includes('copy') ||
                   name.includes('jasper') || name.includes('blog') || desc.includes('writing') ||
                   desc.includes('content')) {
          newCategorySlug = 'content-creation';
        } else if (name.includes('grammarly') || name.includes('wordtune') || name.includes('quillbot') ||
                   name.includes('hemingway') || name.includes('prowriting') || desc.includes('grammar')) {
          newCategorySlug = 'writing';
        } else if (name.includes('slide') || name.includes('presentation') || name.includes('gamma') ||
                   name.includes('tome') || name.includes('beautiful') || desc.includes('presentation')) {
          newCategorySlug = 'presentations-slides';
        } else if (name.includes('video') || name.includes('runway') || name.includes('synthesia') ||
                   name.includes('lumen') || name.includes('pictory') || desc.includes('video')) {
          newCategorySlug = 'video-tools';
        } else if (name.includes('transcrib') || name.includes('whisper') || name.includes('assembly') ||
                   name.includes('otter') || desc.includes('transcript') || desc.includes('speech to text')) {
          newCategorySlug = 'transcription';
        } else if (name.includes('eleven') || name.includes('murf') || name.includes('play.ht') ||
                   name.includes('resemble') || name.includes('music') || name.includes('sound') ||
                   desc.includes('voice') || desc.includes('audio') || desc.includes('music')) {
          newCategorySlug = 'music-audio-creation';
        } else if (name.includes('learn') || name.includes('education') || name.includes('course') ||
                   name.includes('duolingo') || name.includes('coursera') || name.includes('udemy') ||
                   desc.includes('education') || desc.includes('learning')) {
          newCategorySlug = 'education';
        } else if (name.includes('market') || name.includes('seo') || name.includes('advertis') ||
                   name.includes('social') || desc.includes('marketing') || desc.includes('seo')) {
          newCategorySlug = 'marketing-seo';
        } else if (name.includes('design') || name.includes('logo') || name.includes('graphic') ||
                   name.includes('canva') || name.includes('figma') || desc.includes('design')) {
          newCategorySlug = 'design-graphics';
        } else if (name.includes('productivity') || name.includes('notion') || name.includes('task') ||
                   desc.includes('productivity') || desc.includes('organize')) {
          newCategorySlug = 'productivity';
        } else if (name.includes('business') || desc.includes('business')) {
          newCategorySlug = 'business';
        } else if (name.includes('research') || name.includes('analysis') || name.includes('data') ||
                   desc.includes('research') || desc.includes('analysis')) {
          newCategorySlug = 'research-analytics';
        } else {
          // Keep in Other if no match found
          continue;
        }

        // Update the tool if we found a better category
        if (newCategorySlug && categoryMap.has(newCategorySlug)) {
          const newCategoryId = categoryMap.get(newCategorySlug);
          await Tool.findByIdAndUpdate(tool._id, { category: newCategoryId });
          console.log(`✅ Updated ${tool.name}: ${newCategorySlug}`);
          updatedCount++;
        }
        
      } catch (error) {
        console.error(`❌ Error processing tool ${tool.name}:`, error instanceof Error ? error.message : error);
        errors++;
      }
    }

    // Update category tool counts
    console.log('\\n📊 Updating category tool counts...');
    for (const category of categories) {
      const toolCount = await Tool.countDocuments({ category: category._id });
      await Category.findByIdAndUpdate(category._id, { toolCount });
    }

    await mongoose.disconnect();
    
    console.log('\\n📊 Final Categorization Summary:');
    console.log(`✅ Updated: ${updatedCount} tools`);
    console.log(`❌ Errors: ${errors} tools`);
    console.log(`📝 Total processed: ${otherTools.length} tools`);
    console.log('\\n🎉 Final categorization complete!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

finalCategorizeRemaining();
