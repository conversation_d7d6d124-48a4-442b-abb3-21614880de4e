export default function CategoriesLoading() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header skeleton */}
        <div className="mb-8 text-center">
          <div className="h-10 bg-gray-200 rounded-md w-80 mx-auto mb-4 animate-pulse"></div>
          <div className="h-5 bg-gray-200 rounded-md w-96 mx-auto animate-pulse"></div>
        </div>
        
        {/* Search bar skeleton */}
        <div className="mb-8 max-w-md mx-auto">
          <div className="h-12 bg-gray-200 rounded-lg w-full animate-pulse"></div>
        </div>
        
        {/* Categories grid skeleton */}
        <div className="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {Array.from({ length: 12 }).map((_, index) => (
            <div key={index} className="bg-white rounded-lg border p-6 text-center animate-pulse hover:shadow-lg transition-shadow">
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-gray-200 rounded-full"></div>
              </div>
              <div className="space-y-2">
                <div className="h-6 bg-gray-200 rounded w-32 mx-auto"></div>
                <div className="h-4 bg-gray-200 rounded w-16 mx-auto"></div>
                <div className="space-y-1 mt-3">
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4 mx-auto"></div>
                </div>
              </div>
              <div className="mt-4">
                <div className="h-9 bg-gray-200 rounded-md w-full"></div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Stats section skeleton */}
        <div className="mt-12 text-center">
          <div className="h-6 bg-gray-200 rounded w-64 mx-auto animate-pulse"></div>
        </div>
      </div>
    </div>
  )
}