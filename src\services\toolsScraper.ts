import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer';
import cheerio from 'cheerio';
import dbConnect from '../lib/db';
import Tool from '../models/Tool';
import Category from '../models/Category';

interface ScrapedTool {
  name: string;
  description: string;
  url?: string;
  pricing?: string;
  category?: string;
  features?: string[];
  imageUrl?: string;
  company?: string;
}

interface ScraperTarget {
  name: string;
  url: string;
  enabled: boolean;
  selectors: {
    toolContainer: string;
    name: string;
    description: string;
    url?: string;
    pricing?: string;
    category?: string;
    image?: string;
  };
  rateLimit: number; // milliseconds between requests
}

// Configuration for different AI tool websites to scrape
const SCRAPER_TARGETS: ScraperTarget[] = [
  {
    name: 'AI Tool Hunt',
    url: 'https://aitoolhunt.com/',
    enabled: true,
    selectors: {
      toolContainer: '.tool-card, .product-item',
      name: '.tool-name, .product-name, h3, h4',
      description: '.tool-description, .product-description, p',
      url: 'a'
    },
    rateLimit: 3000
  },
  {
    name: 'Future Tools',
    url: 'https://www.futuretools.io/',
    enabled: true,
    selectors: {
      toolContainer: '.tool-item, .card',
      name: '.tool-title, .title, h3',
      description: '.description, p',
      category: '.category, .tag'
    },
    rateLimit: 3000
  },
  {
    name: 'AI Tools Directory',
    url: 'https://www.marktechpost.com/ai-tool-directory/',
    enabled: true,
    selectors: {
      toolContainer: '.tool-card, .post-item',
      name: '.tool-name, .post-title, h3',
      description: '.tool-description, .excerpt, p',
      category: '.category, .tag'
    },
    rateLimit: 4000
  }
];

class AIToolScraper {
  private browser: Browser | null = null;
  private categoryMap: Map<string, string> = new Map();

  async initialize() {
    await dbConnect();
    await this.loadCategoryMap();
    
    this.browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    });
  }

  private async loadCategoryMap() {
    const categories = await Category.find({});
    categories.forEach(category => {
      this.categoryMap.set(category.name.toLowerCase(), category._id.toString());
      this.categoryMap.set(category.slug, category._id.toString());
    });
  }

  private async checkRobotsTxt(baseUrl: string): Promise<boolean> {
    try {
      const robotsUrl = new URL('/robots.txt', baseUrl).toString();
      const response = await fetch(robotsUrl);
      
      if (!response.ok) return true; // If no robots.txt, assume allowed
      
      const robotsText = await response.text();
      const userAgentSection = robotsText
        .split('User-agent: *')[1]
        ?.split('User-agent:')[0] || '';
      
      // Simple check for common disallow patterns
      const disallowed = userAgentSection.includes('Disallow: /') || 
                        userAgentSection.includes('Disallow: *');
      
      return !disallowed;
    } catch {
      return true; // If error checking robots.txt, assume allowed
    }
  }

  private normalizeCategory(category: string): string | null {
    const normalized = category.toLowerCase().trim();
    
    // Category mapping rules
    const categoryMappings: { [key: string]: string } = {
      'ai': 'other',
      'artificial intelligence': 'other',
      'machine learning': 'other',
      'nlp': 'language-models',
      'natural language': 'language-models',
      'text generation': 'language-models',
      'chatbot': 'language-models',
      'conversation': 'language-models',
      'image': 'image-generation',
      'photo': 'image-generation',
      'art': 'image-generation',
      'design': 'content-creation',
      'writing': 'writing',
      'content': 'content-creation',
      'video': 'video-generation',
      'audio': 'audio-ai',
      'voice': 'audio-ai',
      'speech': 'transcription',
      'code': 'code-generation',
      'programming': 'code-generation',
      'developer': 'code-generation',
      'education': 'education',
      'learning': 'education',
      'productivity': 'productivity',
      'business': 'business',
      'data': 'data-analysis',
      'analytics': 'data-analysis'
    };

    for (const [key, value] of Object.entries(categoryMappings)) {
      if (normalized.includes(key)) {
        return this.categoryMap.get(value) || null;
      }
    }

    return this.categoryMap.get('other') || null;
  }

  private async scrapePage(target: ScraperTarget): Promise<ScrapedTool[]> {
    if (!this.browser) throw new Error('Browser not initialized');

    console.log(`🔍 Scraping ${target.name}...`);

    // Check robots.txt
    const canScrape = await this.checkRobotsTxt(target.url);
    if (!canScrape) {
      console.log(`🚫 Robots.txt disallows scraping ${target.name}`);
      return [];
    }

    const page = await this.browser.newPage();
    
    try {
      // Set user agent and configure page
      await page.setUserAgent('Mozilla/5.0 (compatible; AITools-Bot/1.0; +https://aitools.example.com/bot)');
      await page.setViewport({ width: 1280, height: 720 });
      
      // Navigate with timeout
      await page.goto(target.url, { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      });

      // Wait for content to load
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Extract data using cheerio for better parsing
      const content = await page.content();
      const $ = cheerio.load(content);
      const tools: ScrapedTool[] = [];

      $(target.selectors.toolContainer).each((_, element) => {
        try {
          const $el = $(element);
          
          // Try multiple selectors for name
          let name = '';
          const nameSelectors = target.selectors.name.split(', ');
          for (const selector of nameSelectors) {
            name = $el.find(selector).first().text().trim();
            if (name) break;
          }
          
          // Try multiple selectors for description
          let description = '';
          const descSelectors = target.selectors.description.split(', ');
          for (const selector of descSelectors) {
            description = $el.find(selector).first().text().trim();
            if (description && description.length > 20) break;
          }
          
          if (!name || !description || description.length < 10) return;

          const tool: ScrapedTool = {
            name,
            description
          };

          // Extract optional fields
          if (target.selectors.url) {
            const urlEl = $el.find(target.selectors.url).first();
            tool.url = urlEl.attr('href') || urlEl.text().trim();
            
            // Make relative URLs absolute
            if (tool.url && !tool.url.startsWith('http')) {
              const baseUrl = new URL(target.url);
              tool.url = new URL(tool.url, baseUrl.origin).toString();
            }
          }

          if (target.selectors.pricing) {
            tool.pricing = $el.find(target.selectors.pricing).text().trim();
          }

          if (target.selectors.category) {
            tool.category = $el.find(target.selectors.category).text().trim();
          }

          if (target.selectors.image) {
            tool.imageUrl = $el.find(target.selectors.image).attr('src') || 
                           $el.find(target.selectors.image).attr('data-src');
          }

          tools.push(tool);
        } catch (error) {
          console.log(`⚠️ Error parsing tool element:`, error);
        }
      });

      console.log(`✅ Found ${tools.length} tools from ${target.name}`);
      return tools;

    } catch (error) {
      console.error(`❌ Error scraping ${target.name}:`, error);
      return [];
    } finally {
      await page.close();
    }
  }

  private async saveTool(scrapedTool: ScrapedTool): Promise<boolean> {
    try {
      // Check if tool already exists
      const existingTool = await Tool.findOne({ 
        name: { $regex: new RegExp(`^${scrapedTool.name}$`, 'i') }
      });

      if (existingTool) {
        console.log(`⚪ Tool already exists: ${scrapedTool.name}`);
        return false;
      }

      // Determine category
      let categoryId = this.categoryMap.get('other');
      if (scrapedTool.category) {
        categoryId = this.normalizeCategory(scrapedTool.category) || categoryId;
      }

      // Create slug
      const slug = scrapedTool.name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();

      // Prepare tool data
      const toolData = {
        name: scrapedTool.name,
        slug,
        description: scrapedTool.description,
        category: categoryId,
        url: scrapedTool.url || '',
        pricing: this.normalizePricing(scrapedTool.pricing),
        imageUrl: scrapedTool.imageUrl || this.generatePlaceholderImage(scrapedTool.name),
        features: scrapedTool.features || ['AI-powered functionality', 'User-friendly interface'],
        rating: 0,
        ratingCount: 0,
        status: 'active',
        apiAvailable: false
      };

      await Tool.create(toolData);
      console.log(`✅ Created new tool: ${scrapedTool.name}`);
      return true;

    } catch (error) {
      console.error(`❌ Error saving tool ${scrapedTool.name}:`, error);
      return false;
    }
  }

  private normalizePricing(pricing?: string): string {
    if (!pricing) return 'Contact for pricing';
    
    const p = pricing.toLowerCase();
    if (p.includes('free') && !p.includes('premium')) return 'Free';
    if (p.includes('free') && p.includes('premium')) return 'Freemium';
    if (p.includes('paid') || p.includes('$') || p.includes('subscription')) return 'Paid';
    
    return pricing;
  }

  private generatePlaceholderImage(toolName: string): string {
    return `https://api.dicebear.com/7.x/shapes/svg?seed=${encodeURIComponent(toolName)}&backgroundColor=b6e3f4,c0aede,d1f4d9,ffdfbf,ffd5dc`;
  }

  async scrapeAllSources(): Promise<{ success: number; skipped: number; errors: number }> {
    const stats = { success: 0, skipped: 0, errors: 0 };
    
    for (const target of SCRAPER_TARGETS) {
      if (!target.enabled) {
        console.log(`⏭️ Skipping disabled target: ${target.name}`);
        continue;
      }

      try {
        const tools = await this.scrapePage(target);
        
        for (const tool of tools) {
          const saved = await this.saveTool(tool);
          if (saved) {
            stats.success++;
          } else {
            stats.skipped++;
          }
          
          // Rate limiting
          await new Promise(resolve => setTimeout(resolve, target.rateLimit));
        }

      } catch (error) {
        console.error(`❌ Error processing ${target.name}:`, error);
        stats.errors++;
      }
    }

    return stats;
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// Main scraping function
export async function runToolsScraper(): Promise<void> {
  const scraper = new AIToolScraper();
  
  try {
    console.log('🚀 Starting AI Tools Scraper...');
    await scraper.initialize();
    
    const stats = await scraper.scrapeAllSources();
    
    console.log('\n📊 Scraping Results:');
    console.log(`✅ New tools added: ${stats.success}`);
    console.log(`⚪ Tools skipped (already exist): ${stats.skipped}`);
    console.log(`❌ Errors: ${stats.errors}`);
    
  } catch (error) {
    console.error('❌ Scraper failed:', error);
  } finally {
    await scraper.close();
  }
}

// Export the scraper class for other uses
export { AIToolScraper };
