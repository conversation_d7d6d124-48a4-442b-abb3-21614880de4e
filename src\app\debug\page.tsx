'use client'

import { useState, useEffect } from 'react'
import { fetchTools } from '@/services/toolsService'

export default function DebugPage() {
  const [results, setResults] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  const testAPI = async () => {
    setLoading(true)
    setResults([])
    try {
      // Test 1: Get category info for "other"
      const categoryRes = await fetch('/api/categories/other')
      const categoryData = await categoryRes.json()
      setResults(prev => [...prev, { test: 'Category API (other)', data: categoryData }])

      // Test 2: Get tools with fetchTools service using "Other" (capital O)
      const toolsResponse = await fetchTools({ category: 'Other', limit: 5 })
      setResults(prev => [...prev, { test: 'Tools Service (Other)', data: toolsResponse }])

      // Test 3: Direct API call with "Other"
      const directRes = await fetch('/api/tools?category=Other&limit=5')
      const directData = await directRes.json()
      setResults(prev => [...prev, { test: 'Direct API (Other)', data: directData }])

      // Test 4: Try without any category to see all tools
      const allToolsRes = await fetch('/api/tools?limit=5')
      const allToolsData = await allToolsRes.json()
      setResults(prev => [...prev, { test: 'All Tools API', data: allToolsData }])

    } catch (error) {
      setResults(prev => [...prev, { test: 'Error', data: error instanceof Error ? error.message : String(error) }])
    }
    setLoading(false)
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">API Debug Page</h1>
      
      <button 
        onClick={testAPI}
        disabled={loading}
        className="bg-blue-500 text-white px-4 py-2 rounded mb-4"
      >
        {loading ? 'Testing...' : 'Test APIs'}
      </button>

      <div className="space-y-4">
        {results.map((result, index) => (
          <div key={index} className="border p-4 rounded">
            <h3 className="font-bold">{result.test}:</h3>
            <pre className="bg-gray-100 p-2 rounded mt-2 text-sm overflow-auto">
              {JSON.stringify(result.data, null, 2)}
            </pre>
          </div>
        ))}
      </div>
    </div>
  )
}
