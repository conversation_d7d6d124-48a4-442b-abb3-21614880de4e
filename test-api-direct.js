const { createServer } = require('http')
const { parse } = require('url')
const next = require('next')

const dev = process.env.NODE_ENV !== 'production'
const hostname = 'localhost'
const port = 3002

// Create a simple test to check API endpoints
async function testAPI() {
  try {
    // Test category endpoint
    const categoryRes = await fetch('http://localhost:3001/api/categories/other')
    const categoryData = await categoryRes.json()
    console.log('Category API response:', categoryData)
    
    // Test tools endpoint with Other category
    const toolsRes = await fetch('http://localhost:3001/api/tools?category=Other&limit=5')
    const toolsData = await toolsRes.json()
    console.log('Tools API response:', toolsData)
    
  } catch (error) {
    console.error('API test error:', error)
  }
}

testAPI()
