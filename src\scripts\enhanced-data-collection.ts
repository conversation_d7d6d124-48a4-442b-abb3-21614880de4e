import { runAPICollector } from '../services/apiToolCollector';
import dbConnect from '../lib/db';
import Tool from '../models/Tool';
import Category from '../models/Category';

interface ManualToolEntry {
  name: string;
  description: string;
  url: string;
  category: string;
  pricing: string;
  features: string[];
  company?: string;
}

// High-quality AI tools to manually add to our database
const CURATED_TOOLS: ManualToolEntry[] = [
  {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Framework for developing applications powered by language models with memory, data connections, and agent capabilities.',
    url: 'https://langchain.com',
    category: 'code-generation',
    pricing: 'Free',
    features: ['Language Model Framework', 'Agent Support', 'Memory Management', 'Data Connectors'],
    company: '<PERSON><PERSON>hain'
  },
  {
    name: 'Ollama',
    description: 'Get up and running with large language models locally. Run Llama, Mistral, and other models on your own machine.',
    url: 'https://ollama.ai',
    category: 'language-models',
    pricing: 'Free',
    features: ['Local LLM Hosting', 'Multiple Models', 'Easy Setup', 'Cross-platform'],
    company: 'Ollama'
  },
  {
    name: 'ComfyUI',
    description: 'A powerful and modular stable diffusion GUI and backend with graph/nodes interface for advanced AI image generation.',
    url: 'https://github.com/comfyanonymous/ComfyUI',
    category: 'image-generation',
    pricing: 'Free',
    features: ['Node-based Interface', 'Stable Diffusion', 'Advanced Workflows', 'Plugin System'],
    company: 'ComfyAnonymous'
  },
  {
    name: 'Pinecone',
    description: 'Vector database that makes it easy to build and scale AI applications with high-performance vector search.',
    url: 'https://pinecone.io',
    category: 'data-analysis',
    pricing: 'Freemium',
    features: ['Vector Database', 'Real-time Search', 'Auto-scaling', 'Enterprise Grade'],
    company: 'Pinecone'
  },
  {
    name: 'Weights & Biases',
    description: 'MLOps platform for experiment tracking, dataset versioning, and model management for machine learning teams.',
    url: 'https://wandb.ai',
    category: 'data-analysis',
    pricing: 'Freemium',
    features: ['Experiment Tracking', 'Model Versioning', 'Dataset Management', 'Team Collaboration'],
    company: 'Weights & Biases'
  },
  {
    name: 'Comet ML',
    description: 'Platform for tracking, comparing, explaining and optimizing ML experiments and models in production.',
    url: 'https://comet.ml',
    category: 'data-analysis',
    pricing: 'Freemium',
    features: ['Experiment Management', 'Model Monitoring', 'Team Collaboration', 'Production Tracking'],
    company: 'Comet'
  },
  {
    name: 'Streamlit',
    description: 'Open-source Python library that makes it easy to create and share beautiful, custom web apps for machine learning and data science.',
    url: 'https://streamlit.io',
    category: 'code-generation',
    pricing: 'Free',
    features: ['Python Web Apps', 'Data Visualization', 'ML Model Deployment', 'Easy Sharing'],
    company: 'Snowflake'
  },
  {
    name: 'Gradio',
    description: 'Build and share delightful machine learning apps quickly with Python. Create web interfaces for ML models.',
    url: 'https://gradio.app',
    category: 'code-generation',
    pricing: 'Free',
    features: ['ML Web Interfaces', 'Python Integration', 'Easy Deployment', 'Collaborative Sharing'],
    company: 'Hugging Face'
  },
  {
    name: 'LlamaIndex',
    description: 'Data framework for LLM applications to ingest, structure, and access private or domain-specific data.',
    url: 'https://llamaindex.ai',
    category: 'data-analysis',
    pricing: 'Free',
    features: ['Data Ingestion', 'LLM Integration', 'Query Engine', 'Knowledge Graphs'],
    company: 'LlamaIndex'
  },
  {
    name: 'Weaviate',
    description: 'Open-source vector database that stores both objects and vectors, allowing for combining vector search with structured filtering.',
    url: 'https://weaviate.io',
    category: 'data-analysis',
    pricing: 'Freemium',
    features: ['Vector Database', 'Hybrid Search', 'GraphQL API', 'Cloud & On-premise'],
    company: 'Weaviate'
  },
  {
    name: 'AutoGPT',
    description: 'An experimental open-source attempt to make GPT-4 autonomous, capable of performing tasks independently.',
    url: 'https://github.com/Significant-Gravitas/AutoGPT',
    category: 'productivity',
    pricing: 'Free',
    features: ['Autonomous AI', 'Task Automation', 'Goal-oriented', 'Self-improving'],
    company: 'Significant Gravitas'
  },
  {
    name: 'LangSmith',
    description: 'Platform for building production-grade LLM applications with debugging, testing, evaluation, and monitoring.',
    url: 'https://langsmith.langchain.com',
    category: 'code-generation',
    pricing: 'Freemium',
    features: ['LLM Debugging', 'Testing Framework', 'Performance Monitoring', 'Production Ready'],
    company: 'LangChain'
  },
  {
    name: 'Chroma',
    description: 'Open-source embedding database designed for AI applications, making it easy to build LLM apps with memory.',
    url: 'https://trychroma.com',
    category: 'data-analysis',
    pricing: 'Free',
    features: ['Embedding Database', 'LLM Memory', 'Simple API', 'Python & JS Support'],
    company: 'Chroma'
  },
  {
    name: 'Modal',
    description: 'Cloud platform for running generative AI models, large-scale batch jobs, and everything in between.',
    url: 'https://modal.com',
    category: 'code-generation',
    pricing: 'Freemium',
    features: ['Serverless Computing', 'GPU Support', 'Easy Scaling', 'Python Native'],
    company: 'Modal Labs'
  },
  {
    name: 'Together AI',
    description: 'Cloud platform for building and running generative AI applications with open-source models at scale.',
    url: 'https://together.ai',
    category: 'language-models',
    pricing: 'Paid',
    features: ['Open Source Models', 'Inference API', 'Fine-tuning', 'Enterprise Scale'],
    company: 'Together AI'
  }
];

async function addCuratedTools(): Promise<{ added: number; skipped: number }> {
  await dbConnect();
  
  // Load category map
  const categories = await Category.find({});
  const categoryMap = new Map();
  categories.forEach(cat => {
    categoryMap.set(cat.slug, cat._id.toString());
  });

  let added = 0;
  let skipped = 0;

  for (const toolData of CURATED_TOOLS) {
    try {
      // Check if tool already exists
      const existingTool = await Tool.findOne({
        name: { $regex: new RegExp(`^${toolData.name}$`, 'i') }
      });

      if (existingTool) {
        console.log(`⚪ Skipping existing tool: ${toolData.name}`);
        skipped++;
        continue;
      }

      // Get category ID
      const categoryId = categoryMap.get(toolData.category) || categoryMap.get('other');

      // Create slug
      const slug = toolData.name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();

      // Generate placeholder image
      const imageUrl = `https://api.dicebear.com/7.x/shapes/svg?seed=${encodeURIComponent(toolData.name)}&backgroundColor=b6e3f4,c0aede,d1f4d9,ffdfbf,ffd5dc`;

      const newTool = await Tool.create({
        name: toolData.name,
        slug,
        description: toolData.description,
        category: categoryId,
        url: toolData.url,
        pricing: toolData.pricing,
        imageUrl,
        features: toolData.features,
        company: toolData.company,
        rating: 0,
        ratingCount: 0,
        status: 'active',
        apiAvailable: false,
        source: 'manual-curation'
      });

      console.log(`✅ Added: ${toolData.name}`);
      added++;

    } catch (error) {
      console.error(`❌ Error adding ${toolData.name}:`, error);
    }
  }

  return { added, skipped };
}

async function main() {
  console.log('🔄 ENHANCED DATA COLLECTION - Starting...');
  console.log('=' .repeat(50));

  try {
    // 1. Run API collection (Hugging Face)
    console.log('\n🤗 Step 1: API Collection (Hugging Face)');
    await runAPICollector();

    // 2. Add curated high-quality tools
    console.log('\n📚 Step 2: Adding Curated AI Tools');
    const curationResult = await addCuratedTools();
    console.log(`✅ Curated tools added: ${curationResult.added}`);
    console.log(`⚪ Already existed: ${curationResult.skipped}`);

    // 3. Summary
    console.log('\n📊 COLLECTION SUMMARY');
    const totalTools = await Tool.countDocuments();
    console.log(`📁 Total tools in database: ${totalTools}`);
    
    const recentTools = await Tool.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    });
    console.log(`🆕 Tools added today: ${recentTools}`);

    console.log('\n💡 NEXT RECOMMENDATIONS:');
    console.log('• Add API keys for GitHub and Product Hunt in .env.local');
    console.log('• Run the scheduler for automated daily updates');
    console.log('• Monitor tool quality and user feedback');
    console.log('• Consider partnerships with AI tool directories');

  } catch (error) {
    console.error('❌ Data collection failed:', error);
  }
}

main();
