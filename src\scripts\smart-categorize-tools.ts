import mongoose from 'mongoose';
import Tool from '../models/Tool.js';
import Category from '../models/Category.js';

async function smartCategorizeTools() {
  try {
    console.log('🔗 Connecting to database...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aitools');

    // Get all categories
    const categories = await Category.find({});
    const categoryMap = new Map();
    categories.forEach(cat => {
      categoryMap.set(cat.slug, cat._id);
    });

    // Get all tools that need categorization
    const tools = await Tool.find({}).populate('category');
    console.log(`🔍 Found ${tools.length} tools to categorize`);

    let updatedCount = 0;
    let errors = 0;

    for (const tool of tools) {
      try {
        const name = tool.name.toLowerCase();
        const desc = (tool.description || '').toLowerCase();
        let newCategorySlug = null;

        // Smart categorization logic
        if (isMatch(name, desc, ['chatgpt', 'claude', 'bard', 'gemini', 'chat', 'chatbot', 'assistant', 'conversation', 'dialogue'])) {
          newCategorySlug = 'chatbots-ai-assistants';
        } else if (isMatch(name, desc, ['automation', 'workflow', 'zapier', 'agent', 'autonomous', 'agentgpt', 'auto'])) {
          newCategorySlug = 'ai-automation-workflow';
        } else if (isMatch(name, desc, ['stable diffusion', 'midjourney', 'dall-e', 'dalle', 'image generation', 'art', 'generate image', 'create image', 'flux', 'imagen'])) {
          newCategorySlug = 'image-generation';
        } else if (isMatch(name, desc, ['photo edit', 'image edit', 'photo enhancer', 'upscale', 'remove background', 'face swap', 'deepfake', 'photoshop'])) {
          newCategorySlug = 'photo-image-editing';
        } else if (isMatch(name, desc, ['video edit', 'video generation', 'video create', 'runway', 'synthesia', 'luma', 'video ai', 'clip'])) {
          newCategorySlug = 'video-tools';
        } else if (isMatch(name, desc, ['music', 'audio', 'sound', 'voice', 'speech', 'elevenlabs', 'murf', 'tts', 'text to speech', 'voice clone'])) {
          newCategorySlug = 'music-audio-creation';
        } else if (isMatch(name, desc, ['presentation', 'slide', 'pitch', 'gamma', 'tome', 'beautiful.ai', 'slidebean'])) {
          newCategorySlug = 'presentations-slides';
        } else if (isMatch(name, desc, ['marketing', 'seo', 'advertising', 'social media', 'campaign', 'ad creative', 'surfer', 'semrush'])) {
          newCategorySlug = 'marketing-seo';
        } else if (isMatch(name, desc, ['sales', 'customer service', 'support', 'crm', 'lead', 'customer'])) {
          newCategorySlug = 'sales-customer-service';
        } else if (isMatch(name, desc, ['hr', 'recruitment', 'hiring', 'resume', 'job', 'interview', 'talent'])) {
          newCategorySlug = 'hr-recruitment';
        } else if (isMatch(name, desc, ['finance', 'investment', 'money', 'trading', 'financial', 'accounting', 'budget', 'tax'])) {
          newCategorySlug = 'finance-investment';
        } else if (isMatch(name, desc, ['legal', 'law', 'compliance', 'contract', 'attorney', 'lawyer'])) {
          newCategorySlug = 'legal-compliance';
        } else if (isMatch(name, desc, ['ecommerce', 'shopping', 'store', 'retail', 'product', 'commerce', 'shopify'])) {
          newCategorySlug = 'ecommerce-shopping';
        } else if (isMatch(name, desc, ['website builder', 'web builder', 'no-code', 'nocode', 'site builder', 'wix', 'framer'])) {
          newCategorySlug = 'nocode-website-builders';
        } else if (isMatch(name, desc, ['api', 'sdk', 'developer', 'framework', 'library', 'hugging face', 'openai api', 'ml framework'])) {
          newCategorySlug = 'api-developer-tools';
        } else if (isMatch(name, desc, ['database', 'data management', 'sql', 'query', 'data storage', 'pinecone', 'weaviate', 'chroma'])) {
          newCategorySlug = 'database-data-management';
        } else if (isMatch(name, desc, ['translation', 'translate', 'language', 'multilingual', 'deepl', 'google translate'])) {
          newCategorySlug = 'translation-language';
        } else if (isMatch(name, desc, ['email', 'mail', 'communication', 'messaging', 'newsletter'])) {
          newCategorySlug = 'email-communication';
        } else if (isMatch(name, desc, ['meeting', 'collaboration', 'team', 'project management', 'notion', 'slack'])) {
          newCategorySlug = 'meeting-collaboration';
        } else if (isMatch(name, desc, ['research', 'analysis', 'analytics', 'insights', 'data analysis', 'survey', 'consensus'])) {
          newCategorySlug = 'research-analytics';
        } else if (isMatch(name, desc, ['document', 'pdf', 'ocr', 'file', 'paper', 'doc', 'text extraction'])) {
          newCategorySlug = 'document-management';
        } else if (isMatch(name, desc, ['game', 'gaming', 'entertainment', 'character ai', 'roleplay', 'dungeon'])) {
          newCategorySlug = 'gaming-entertainment';
        } else if (isMatch(name, desc, ['health', 'medical', 'fitness', 'healthcare', 'medicine', 'doctor', 'diagnosis'])) {
          newCategorySlug = 'healthcare-fitness';
        } else if (isMatch(name, desc, ['travel', 'trip', 'tourism', 'hotel', 'flight', 'vacation', 'expedia'])) {
          newCategorySlug = 'travel-tourism';
        } else if (isMatch(name, desc, ['real estate', 'property', 'house', 'home', 'realty'])) {
          newCategorySlug = 'real-estate';
        } else if (isMatch(name, desc, ['3d', 'ar', 'vr', 'augmented reality', 'virtual reality', 'metaverse'])) {
          newCategorySlug = '3d-ar-vr';
        } else if (isMatch(name, desc, ['security', 'privacy', 'cybersecurity', 'protection', 'threat', 'encryption'])) {
          newCategorySlug = 'security-privacy';
        } else if (isMatch(name, desc, ['nsfw', 'adult', 'nude', 'sex', 'dating', 'girlfriend', 'boyfriend', 'lover'])) {
          newCategorySlug = 'adult-nsfw';
        } else if (isMatch(name, desc, ['code', 'programming', 'github', 'copilot', 'codegen', 'codeium', 'tabnine'])) {
          newCategorySlug = 'code-generation';
        } else if (isMatch(name, desc, ['education', 'learning', 'course', 'tutorial', 'teach', 'study', 'duolingo', 'coursera'])) {
          newCategorySlug = 'education';
        } else if (isMatch(name, desc, ['transcription', 'transcript', 'whisper', 'assembly', 'speech to text'])) {
          newCategorySlug = 'transcription';
        } else if (isMatch(name, desc, ['write', 'writing', 'content', 'copy', 'blog', 'article', 'grammarly', 'jasper'])) {
          newCategorySlug = 'content-creation';
        } else if (isMatch(name, desc, ['productivity', 'task', 'organiz', 'calendar', 'notes', 'reminder'])) {
          newCategorySlug = 'productivity';
        } else if (isMatch(name, desc, ['design', 'graphic', 'logo', 'icon', 'banner', 'canva', 'figma'])) {
          newCategorySlug = 'design-graphics';
        }

        // If we found a better category, update the tool
        if (newCategorySlug && categoryMap.has(newCategorySlug)) {
          const newCategoryId = categoryMap.get(newCategorySlug);
          
          // Only update if it's different from current category
          if (!tool.category || tool.category._id.toString() !== newCategoryId.toString()) {
            await Tool.findByIdAndUpdate(tool._id, { category: newCategoryId });
            console.log(`✅ Updated ${tool.name}: ${newCategorySlug}`);
            updatedCount++;
          }
        }
        
      } catch (error) {
        console.error(`❌ Error processing tool ${tool.name}:`, error instanceof Error ? error.message : error);
        errors++;
      }
    }

    // Update category tool counts
    console.log('\\n📊 Updating category tool counts...');
    for (const category of categories) {
      const toolCount = await Tool.countDocuments({ category: category._id });
      await Category.findByIdAndUpdate(category._id, { toolCount });
    }

    await mongoose.disconnect();
    
    console.log('\\n📊 Categorization Summary:');
    console.log(`✅ Updated: ${updatedCount} tools`);
    console.log(`❌ Errors: ${errors} tools`);
    console.log(`📝 Total processed: ${tools.length} tools`);
    console.log('\\n🎉 Smart categorization complete!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

function isMatch(name: string, description: string, keywords: string[]): boolean {
  const text = `${name} ${description}`.toLowerCase();
  return keywords.some(keyword => text.includes(keyword.toLowerCase()));
}

smartCategorizeTools();
