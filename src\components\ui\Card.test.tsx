import React from 'react'
import { render, screen } from '@testing-library/react'
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from './Card'

describe('Card Component', () => {
  it('renders Card with default props', () => {
    render(<Card>Card Content</Card>)
    expect(screen.getByText('Card Content')).toBeInTheDocument()
    expect(screen.getByText('Card Content')).toHaveClass('rounded-lg')
  })

  it('renders Card with different variants', () => {
    const { rerender } = render(<Card variant="outline">Outline Card</Card>)
    expect(screen.getByText('Outline Card')).toHaveClass('border')

    rerender(<Card variant="ghost">Ghost Card</Card>)
    expect(screen.getByText('Ghost Card')).toHaveClass('border-transparent')
  })

  it('renders Card with different sizes', () => {
    const { rerender } = render(<Card size="sm">Small Card</Card>)
    expect(screen.getByText('Small Card')).toHaveClass('p-4')

    rerender(<Card size="lg">Large Card</Card>)
    expect(screen.getByText('Large Card')).toHaveClass('p-8')
  })

  it('renders Card subcomponents correctly', () => {
    render(
      <Card>
        <CardHeader>
          <CardTitle>Test Title</CardTitle>
          <CardDescription>Test Description</CardDescription>
        </CardHeader>
        <CardContent>Test Content</CardContent>
        <CardFooter>Test Footer</CardFooter>
      </Card>
    )

    expect(screen.getByText('Test Title')).toBeInTheDocument()
    expect(screen.getByText('Test Description')).toBeInTheDocument()
    expect(screen.getByText('Test Content')).toBeInTheDocument()
    expect(screen.getByText('Test Footer')).toBeInTheDocument()

    expect(screen.getByText('Test Title')).toHaveClass('text-2xl')
    expect(screen.getByText('Test Description')).toHaveClass('text-sm')
    expect(screen.getByText('Test Content')).toHaveClass('pt-0')
    expect(screen.getByText('Test Footer')).toHaveClass('flex')
  })
}) 