'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '../ui/Button';
import ComingSoon from '../ui/ComingSoon';
import LoadingState from '../ui/LoadingState';
import { useClientToken, apiCall } from '@/hooks/useClientToken';

// Mock data for when database is unavailable
const mockTools = [
  {
    _id: 'mock-1',
    name: 'ChatGPT',
    description: 'Advanced AI chatbot for conversations, writing, and problem-solving',
    imageUrl: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=300&h=200&fit=crop&crop=center',
    rating: 4.8,
    category: 'AI Chatbots',
    pricing: 'Freemium' as const,
    url: 'https://chat.openai.com'
  },
  {
    _id: 'mock-2',
    name: 'Midjourney',
    description: 'AI-powered image generation tool for creating stunning artwork',
    imageUrl: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=300&h=200&fit=crop&crop=center',
    rating: 4.7,
    category: 'Image Generation',
    pricing: 'Paid' as const,
    url: 'https://midjourney.com'
  },
  {
    _id: 'mock-3',
    name: 'GitHub Copilot',
    description: 'AI pair programmer that helps you write code faster',
    imageUrl: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=300&h=200&fit=crop&crop=center',
    rating: 4.6,
    category: 'Code Generation',
    pricing: 'Paid' as const,
    url: 'https://github.com/features/copilot'
  },
  {
    _id: 'mock-4',
    name: 'Grammarly',
    description: 'AI writing assistant for grammar, spelling, and style',
    imageUrl: 'https://images.unsplash.com/photo-1455390582262-044cdead277a?w=300&h=200&fit=crop&crop=center',
    rating: 4.5,
    category: 'Writing',
    pricing: 'Freemium' as const,
    url: 'https://grammarly.com'
  },
  {
    _id: 'mock-5',
    name: 'Notion AI',
    description: 'AI-powered workspace for notes, docs, and project management',
    imageUrl: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=300&h=200&fit=crop&crop=center',
    rating: 4.4,
    category: 'Productivity',
    pricing: 'Freemium' as const,
    url: 'https://notion.so'
  },
  {
    _id: 'mock-6',
    name: 'Canva AI',
    description: 'AI-enhanced design platform for creating graphics and presentations',
    imageUrl: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=300&h=200&fit=crop&crop=center',
    rating: 4.3,
    category: 'Design',
    pricing: 'Freemium' as const,
    url: 'https://canva.com'
  }
];

interface Tool {
  _id: string;
  name: string;
  description: string;
  imageUrl: string;
  rating: number;
  category: string;
  pricing: 'Free' | 'Freemium' | 'Paid';
  url: string;
}

const PublicFeaturedTools: React.FC = () => {
  // Initialize with mock data to prevent flashing
  const [tools, setTools] = useState<Tool[]>(mockTools);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [useMockData, setUseMockData] = useState(true);
  const { isReady: tokenReady } = useClientToken();

  useEffect(() => {
    // Don't show loading state initially since we already have mock data
    let isMounted = true;

    const loadFeaturedTools = async () => {
      try {
        // Try to fetch from API first
        const response = await apiCall('/api/tools?limit=6&sortBy=rating');

        if (!isMounted) return;

        if (!response.ok) {
          throw new Error('Failed to fetch tools');
        }

        const data = await response.json();
        if (data.tools && data.tools.length > 0) {
          setTools(data.tools.slice(0, 6));
          setUseMockData(false);
        } else {
          // If no tools from API, keep using mock data
          setTools(mockTools);
          setUseMockData(true);
        }
      } catch (err) {
        console.error('Error loading featured tools:', err);
        // Keep using mock data when API fails
        setTools(mockTools);
        setUseMockData(true);
        setError('Showing sample tools - database temporarily unavailable');
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // Wait for client token to be ready (or immediately if anti-scraping is disabled)
    if (tokenReady) {
      loadFeaturedTools();
    }

    return () => {
      isMounted = false;
    };
  }, [tokenReady]);

  // We don't need a loading state since we initialize with mock data
  // This prevents flashing

  return (
    <div className="py-12">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Featured Tools</h2>
          <Link href="/pricing">
            <Button variant="ghost">View All Tools</Button>
          </Link>
        </div>
        
        {useMockData && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-700">
              <span className="font-medium">Preview Mode:</span> These are sample tools to showcase our directory. 
              <Link href="/pricing" className="underline hover:no-underline ml-1">
                Subscribe to access 1000+ real AI tools
              </Link>
            </p>
          </div>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tools.map((tool) => (
            <div key={tool._id} className="bg-card rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
              <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center relative overflow-hidden">
                <img
                  src={tool.imageUrl}
                  alt={tool.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const parent = target.parentElement;
                    if (parent && !parent.querySelector('.fallback-content')) {
                      const fallback = document.createElement('div');
                      fallback.className = 'fallback-content flex flex-col items-center justify-center text-center p-4';
                      fallback.innerHTML = `
                        <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mb-2">
                          <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                          </svg>
                        </div>
                        <div class="text-sm font-medium text-gray-700">${tool.name}</div>
                      `;
                      parent.appendChild(fallback);
                    }
                  }}
                  onLoad={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.opacity = '1';
                  }}
                  style={{ opacity: 0, transition: 'opacity 0.3s ease-in-out' }}
                />
              </div>
              <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-lg font-semibold line-clamp-1">{tool.name}</h3>
                  <div className="flex items-center text-sm text-yellow-600">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    {tool.rating}
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mb-3 line-clamp-2">{tool.description}</p>
                <div className="flex justify-between items-center">
                  <span className="text-xs px-2 py-1 bg-primary/10 text-primary rounded-full">
                    {tool.category}
                  </span>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    tool.pricing === 'Free' ? 'bg-green-100 text-green-700' :
                    tool.pricing === 'Freemium' ? 'bg-blue-100 text-blue-700' :
                    'bg-orange-100 text-orange-700'
                  }`}>
                    {tool.pricing}
                  </span>
                </div>
                <div className="mt-3">
                  <Link href="/pricing">
                    <Button size="sm" className="w-full">
                      View Details
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center mt-8">
          <Link href="/pricing">
            <Button size="lg">
              Access 1000+ AI Tools - Subscribe Now
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PublicFeaturedTools;
