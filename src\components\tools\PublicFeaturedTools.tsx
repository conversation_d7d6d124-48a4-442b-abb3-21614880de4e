'use client'

import React, { useState, useEffect } from 'react';
import <PERSON> from 'next/link';
import { Button } from '../ui/Button';
import ComingSoon from '../ui/ComingSoon';
import LoadingState from '../ui/LoadingState';
import { useClientToken, apiCall } from '@/hooks/useClientToken';

// Mock data for when database is unavailable
const mockTools = [
  {
    _id: 'mock-1',
    name: 'ChatGPT',
    description: 'Advanced AI chatbot for conversations, writing, and problem-solving',
    imageUrl: 'https://via.placeholder.com/300x200?text=ChatGPT',
    rating: 4.8,
    category: 'AI Chatbots',
    pricing: 'Freemium' as const,
    url: 'https://chat.openai.com'
  },
  {
    _id: 'mock-2',
    name: 'Midjourney',
    description: 'AI-powered image generation tool for creating stunning artwork',
    imageUrl: 'https://via.placeholder.com/300x200?text=Midjourney',
    rating: 4.7,
    category: 'Image Generation',
    pricing: 'Paid' as const,
    url: 'https://midjourney.com'
  },
  {
    _id: 'mock-3',
    name: 'GitHub Copilot',
    description: 'AI pair programmer that helps you write code faster',
    imageUrl: 'https://via.placeholder.com/300x200?text=GitHub+Copilot',
    rating: 4.6,
    category: 'Code Generation',
    pricing: 'Paid' as const,
    url: 'https://github.com/features/copilot'
  },
  {
    _id: 'mock-4',
    name: 'Grammarly',
    description: 'AI writing assistant for grammar, spelling, and style',
    imageUrl: 'https://via.placeholder.com/300x200?text=Grammarly',
    rating: 4.5,
    category: 'Writing',
    pricing: 'Freemium' as const,
    url: 'https://grammarly.com'
  },
  {
    _id: 'mock-5',
    name: 'Notion AI',
    description: 'AI-powered workspace for notes, docs, and project management',
    imageUrl: 'https://via.placeholder.com/300x200?text=Notion+AI',
    rating: 4.4,
    category: 'Productivity',
    pricing: 'Freemium' as const,
    url: 'https://notion.so'
  },
  {
    _id: 'mock-6',
    name: 'Canva AI',
    description: 'AI-enhanced design platform for creating graphics and presentations',
    imageUrl: 'https://via.placeholder.com/300x200?text=Canva+AI',
    rating: 4.3,
    category: 'Design',
    pricing: 'Freemium' as const,
    url: 'https://canva.com'
  }
];

interface Tool {
  _id: string;
  name: string;
  description: string;
  imageUrl: string;
  rating: number;
  category: string;
  pricing: 'Free' | 'Freemium' | 'Paid';
  url: string;
}

const PublicFeaturedTools: React.FC = () => {
  const [tools, setTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [useMockData, setUseMockData] = useState(false);
  const { isReady: tokenReady } = useClientToken();

  useEffect(() => {
    const loadFeaturedTools = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Try to fetch from API first
        const response = await apiCall('/api/tools?limit=6&sortBy=rating');
        
        if (!response.ok) {
          throw new Error('Failed to fetch tools');
        }
        
        const data = await response.json();
        if (data.tools && data.tools.length > 0) {
          setTools(data.tools.slice(0, 6));
        } else {
          // If no tools from API, use mock data
          setUseMockData(true);
          setTools(mockTools);
        }
      } catch (err) {
        console.error('Error loading featured tools:', err);
        // Fall back to mock data when API fails
        setUseMockData(true);
        setTools(mockTools);
        setError('Showing sample tools - database temporarily unavailable');
      } finally {
        setLoading(false);
      }
    };

    // Wait for client token to be ready (or immediately if anti-scraping is disabled)
    if (!tokenReady) return;
    loadFeaturedTools();
  }, [tokenReady]);

  if (loading) {
    return <LoadingState title="Featured Tools" type="tools" count={6} />;
  }

  return (
    <div className="py-12">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Featured Tools</h2>
          <Link href="/pricing">
            <Button variant="ghost">View All Tools</Button>
          </Link>
        </div>
        
        {useMockData && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-700">
              <span className="font-medium">Preview Mode:</span> These are sample tools to showcase our directory. 
              <Link href="/pricing" className="underline hover:no-underline ml-1">
                Subscribe to access 1000+ real AI tools
              </Link>
            </p>
          </div>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tools.map((tool) => (
            <div key={tool._id} className="bg-card rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
              <div className="aspect-video bg-muted flex items-center justify-center">
                <img 
                  src={tool.imageUrl} 
                  alt={tool.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = `https://via.placeholder.com/300x200?text=${encodeURIComponent(tool.name)}`;
                  }}
                />
              </div>
              <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-lg font-semibold line-clamp-1">{tool.name}</h3>
                  <div className="flex items-center text-sm text-yellow-600">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    {tool.rating}
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mb-3 line-clamp-2">{tool.description}</p>
                <div className="flex justify-between items-center">
                  <span className="text-xs px-2 py-1 bg-primary/10 text-primary rounded-full">
                    {tool.category}
                  </span>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    tool.pricing === 'Free' ? 'bg-green-100 text-green-700' :
                    tool.pricing === 'Freemium' ? 'bg-blue-100 text-blue-700' :
                    'bg-orange-100 text-orange-700'
                  }`}>
                    {tool.pricing}
                  </span>
                </div>
                <div className="mt-3">
                  <Link href="/pricing">
                    <Button size="sm" className="w-full">
                      View Details
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center mt-8">
          <Link href="/pricing">
            <Button size="lg">
              Access 1000+ AI Tools - Subscribe Now
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PublicFeaturedTools;
