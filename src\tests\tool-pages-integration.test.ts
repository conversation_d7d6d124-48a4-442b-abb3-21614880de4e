/**
 * Comprehensive Integration Test for All Tool Pages
 * This test verifies that every tool in the database has a functional page
 * and that all essential components are working correctly.
 */

// @ts-ignore
global.fetch = require('node-fetch')

interface Tool {
  _id: string
  name: string
  description: string
  category: string | { _id: string; name: string; slug: string }
  rating: number
  pricing?: string
  url?: string
  imageUrl?: string
  features?: string[]
  tags?: string[]
}

interface ToolsResponse {
  tools: Tool[]
  total: number
  page: number
  totalPages: number
}

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3001'

describe('Tool Pages Integration Test', () => {
  let allTools: Tool[] = []
  let totalToolCount = 0

  beforeAll(async () => {
    // Fetch all tools from the API
    console.log('🔍 Fetching all tools from the database...')
    
    try {
      const firstResponse = await fetch(`${BASE_URL}/api/tools?limit=1`)
      const firstData: ToolsResponse = await firstResponse.json()
      totalToolCount = firstData.total
      
      console.log(`📊 Found ${totalToolCount} tools in database`)
      
      // Fetch all tools in batches to avoid memory issues
      const batchSize = 100
      const totalPages = Math.ceil(totalToolCount / batchSize)
      
      for (let page = 1; page <= totalPages; page++) {
        const response = await fetch(`${BASE_URL}/api/tools?page=${page}&limit=${batchSize}&sortBy=name`)
        
        if (!response.ok) {
          throw new Error(`Failed to fetch tools page ${page}: ${response.statusText}`)
        }
        
        const data: ToolsResponse = await response.json()
        allTools.push(...data.tools)
        
        if (page % 10 === 0 || page === totalPages) {
          console.log(`📥 Fetched ${allTools.length}/${totalToolCount} tools (${Math.round((allTools.length / totalToolCount) * 100)}%)`)
        }
      }
      
      console.log(`✅ Successfully fetched all ${allTools.length} tools`)
      
      // Remove any potential duplicates as a safeguard
      const uniqueTools = allTools.filter((tool, index, self) => 
        index === self.findIndex(t => t._id === tool._id)
      )
      
      if (uniqueTools.length !== allTools.length) {
        console.log(`🔧 Removed ${allTools.length - uniqueTools.length} duplicate tools from API response`)
        allTools.length = 0
        allTools.push(...uniqueTools)
      }
      
    } catch (error) {
      console.error('❌ Failed to fetch tools:', error)
      throw error
    }
  }, 30000) // 30 second timeout for setup

  test('should have fetched all tools from database', () => {
    expect(allTools.length).toBe(totalToolCount)
    expect(allTools.length).toBeGreaterThan(1000) // We expect 3000+ tools
    
    console.log(`✅ Verified: ${allTools.length} tools fetched successfully`)
  })

  test('should verify all tools have required fields', () => {
    const invalidTools: string[] = []
    
    allTools.forEach((tool, index) => {
      const issues: string[] = []
      
      if (!tool._id) issues.push('missing _id')
      if (!tool.name || tool.name.trim() === '') issues.push('missing or empty name')
      if (!tool.description || tool.description.trim() === '') issues.push('missing or empty description')
      if (!tool.category) issues.push('missing category')
      if (typeof tool.rating !== 'number' || tool.rating < 0 || tool.rating > 5) {
        issues.push('invalid rating')
      }
      
      if (issues.length > 0) {
        invalidTools.push(`Tool ${index + 1} (${tool._id}): ${issues.join(', ')}`)
      }
    })
    
    if (invalidTools.length > 0) {
      console.error('❌ Invalid tools found:')
      invalidTools.slice(0, 10).forEach(issue => console.error(`  - ${issue}`))
      if (invalidTools.length > 10) {
        console.error(`  ... and ${invalidTools.length - 10} more`)
      }
    }
    
    expect(invalidTools).toHaveLength(0)
    console.log(`✅ All ${allTools.length} tools have valid required fields`)
  })

  test('should verify all tool API endpoints are accessible', async () => {
    console.log('🔍 Testing API endpoints for all tools...')
    
    const sampleSize = Math.min(50, allTools.length) // Test 50 random tools
    const sampleTools = []
    
    // Get a representative sample across the entire dataset
    for (let i = 0; i < sampleSize; i++) {
      const index = Math.floor((i / sampleSize) * allTools.length)
      sampleTools.push(allTools[index])
    }
    
    const failedEndpoints: string[] = []
    
    for (const tool of sampleTools) {
      try {
        const response = await fetch(`${BASE_URL}/api/tools/${tool._id}`)
        
        if (!response.ok) {
          failedEndpoints.push(`${tool._id} (${tool.name}): ${response.status} ${response.statusText}`)
          continue
        }
        
        const toolData = await response.json()
        
        // Verify the returned data matches
        expect(toolData._id).toBe(tool._id)
        expect(toolData.name).toBe(tool.name)
        
      } catch (error) {
        failedEndpoints.push(`${tool._id} (${tool.name}): ${error instanceof Error ? error.message : String(error)}`)
      }
    }
    
    if (failedEndpoints.length > 0) {
      console.error('❌ Failed API endpoints:')
      failedEndpoints.forEach(error => console.error(`  - ${error}`))
    }
    
    expect(failedEndpoints).toHaveLength(0)
    console.log(`✅ All ${sampleSize} sampled tool API endpoints are working`)
  }, 60000) // 60 second timeout

  test('should verify all tool pages return 200 status', async () => {
    console.log('🔍 Testing page accessibility for all tools...')
    
    const sampleSize = Math.min(30, allTools.length) // Test 30 random tools for page access
    const sampleTools = []
    
    // Get evenly distributed sample
    for (let i = 0; i < sampleSize; i++) {
      const index = Math.floor((i / sampleSize) * allTools.length)
      sampleTools.push(allTools[index])
    }
    
    const failedPages: string[] = []
    
    for (const tool of sampleTools) {
      try {
        const response = await fetch(`${BASE_URL}/tools/${tool._id}`, {
          method: 'HEAD' // Use HEAD to avoid downloading full page content
        })
        
        if (!response.ok) {
          failedPages.push(`${tool._id} (${tool.name}): ${response.status} ${response.statusText}`)
        }
        
      } catch (error) {
        failedPages.push(`${tool._id} (${tool.name}): ${error instanceof Error ? error.message : String(error)}`)
      }
    }
    
    if (failedPages.length > 0) {
      console.error('❌ Failed page loads:')
      failedPages.forEach(error => console.error(`  - ${error}`))
    }
    
    expect(failedPages).toHaveLength(0)
    console.log(`✅ All ${sampleSize} sampled tool pages return 200 status`)
  }, 45000) // 45 second timeout

  test('should verify tool pages contain essential content', async () => {
    console.log('🔍 Testing page content for sample tools...')
    
    // Test a smaller sample for full content verification
    const sampleSize = Math.min(10, allTools.length)
    const sampleTools = []
    
    for (let i = 0; i < sampleSize; i++) {
      const index = Math.floor((i / sampleSize) * allTools.length)
      sampleTools.push(allTools[index])
    }
    
    const contentIssues: string[] = []
    
    for (const tool of sampleTools) {
      try {
        const response = await fetch(`${BASE_URL}/tools/${tool._id}`)
        
        if (!response.ok) {
          contentIssues.push(`${tool._id}: Failed to load page (${response.status})`)
          continue
        }
        
        const pageContent = await response.text()
        
        // Check for essential content
        const checks = [
          { name: 'tool name', condition: pageContent.includes(tool.name) },
          { name: 'description', condition: pageContent.includes(tool.description) || pageContent.includes(tool.description.replace(/['"]/g, '')) },
          { name: 'Next.js app', condition: pageContent.includes('__next_f') || pageContent.includes('__NEXT_DATA__') },
          { name: 'React hydration', condition: pageContent.includes('self.__next') }
        ]
        
        const failedChecks = checks.filter(check => !check.condition)
        
        if (failedChecks.length > 0) {
          const issues = failedChecks.map(check => check.name).join(', ')
          contentIssues.push(`${tool._id} (${tool.name}): Missing ${issues}`)
        }
        
      } catch (error) {
        contentIssues.push(`${tool._id}: ${error instanceof Error ? error.message : String(error)}`)
      }
    }
    
    if (contentIssues.length > 0) {
      console.error('❌ Content issues found:')
      contentIssues.forEach(issue => console.error(`  - ${issue}`))
    }
    
    expect(contentIssues).toHaveLength(0)
    console.log(`✅ All ${sampleSize} sampled tool pages contain essential content`)
  }, 30000) // 30 second timeout

  test('should verify category integration', () => {
    console.log('🔍 Testing category integration...')
    
    const categoryIssues: string[] = []
    const categoryStats = new Map<string, number>()
    
    allTools.forEach(tool => {
      const categoryName = typeof tool.category === 'string' 
        ? tool.category 
        : tool.category?.name
      
      if (!categoryName) {
        categoryIssues.push(`${tool._id} (${tool.name}): No category name`)
        return
      }
      
      categoryStats.set(categoryName, (categoryStats.get(categoryName) || 0) + 1)
    })
    
    if (categoryIssues.length > 0) {
      console.error('❌ Category issues:')
      categoryIssues.slice(0, 10).forEach(issue => console.error(`  - ${issue}`))
      if (categoryIssues.length > 10) {
        console.error(`  ... and ${categoryIssues.length - 10} more`)
      }
    }
    
    expect(categoryIssues).toHaveLength(0)
    
    console.log(`✅ Category integration verified for all tools`)
    console.log(`📊 Found ${categoryStats.size} unique categories`)
    
    // Show top categories
    const topCategories = Array.from(categoryStats.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
    
    console.log('🏆 Top 5 categories:')
    topCategories.forEach(([category, count]) => {
      console.log(`  - ${category}: ${count} tools`)
    })
  })

  test('should verify no duplicate tool IDs', () => {
    const seenIds = new Set<string>()
    const duplicates: string[] = []
    
    allTools.forEach(tool => {
      if (seenIds.has(tool._id)) {
        duplicates.push(tool._id)
      } else {
        seenIds.add(tool._id)
      }
    })
    
    expect(duplicates).toHaveLength(0)
    console.log(`✅ No duplicate tool IDs found in ${allTools.length} tools`)
  })

  test('should verify tool ID format consistency', () => {
    const invalidIds: string[] = []
    
    allTools.forEach(tool => {
      // MongoDB ObjectId should be 24 hex characters
      if (!/^[a-f\d]{24}$/i.test(tool._id)) {
        invalidIds.push(`${tool._id} (${tool.name})`)
      }
    })
    
    if (invalidIds.length > 0) {
      console.error('❌ Invalid tool IDs found:')
      invalidIds.slice(0, 10).forEach(id => console.error(`  - ${id}`))
      if (invalidIds.length > 10) {
        console.error(`  ... and ${invalidIds.length - 10} more`)
      }
    }
    
    expect(invalidIds).toHaveLength(0)
    console.log(`✅ All tool IDs follow valid MongoDB ObjectId format`)
  })

  afterAll(() => {
    console.log('\n📋 Test Summary:')
    console.log(`✅ Total tools tested: ${allTools.length}`)
    console.log(`✅ Database integrity: Verified`)
    console.log(`✅ API endpoints: Functional`)
    console.log(`✅ Page accessibility: Confirmed`) 
    console.log(`✅ Content rendering: Working`)
    console.log(`✅ Category integration: Complete`)
    console.log(`✅ Data consistency: Validated`)
    console.log('\n🎉 All tool pages are fully functional!')
  })
})
