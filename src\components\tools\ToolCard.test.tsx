import React from 'react'
import { render, screen } from '@testing-library/react'
import ToolCard from './ToolCard'

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    return <img {...props} />
  },
}))

const mockTool = {
  id: '1',
  name: 'Test AI Tool',
  description: 'A test AI tool description',
  imageUrl: '/test-image.jpg',
  rating: 4.5,
  category: 'Test Category',
  pricing: 'Free' as const,
  url: 'https://test-tool.com',
}

describe('ToolCard', () => {
  it('renders tool information correctly', () => {
    render(<ToolCard {...mockTool} />)
    
    expect(screen.getByText(mockTool.name)).toBeInTheDocument()
    expect(screen.getByText(mockTool.description)).toBeInTheDocument()
    expect(screen.getByText(mockTool.category)).toBeInTheDocument()
    expect(screen.getByText(mockTool.pricing)).toBeInTheDocument()
    expect(screen.getByText('4.5')).toBeInTheDocument()
  })

  it('renders correct pricing badge color', () => {
    const { rerender } = render(<ToolCard {...mockTool} />)
    expect(screen.getByText('Free')).toHaveClass('bg-green-100', 'text-green-800')

    rerender(<ToolCard {...mockTool} pricing="Freemium" />)
    expect(screen.getByText('Freemium')).toHaveClass('bg-blue-100', 'text-blue-800')

    rerender(<ToolCard {...mockTool} pricing="Paid" />)
    expect(screen.getByText('Paid')).toHaveClass('bg-purple-100', 'text-purple-800')
  })

  it('renders correct number of stars based on rating', () => {
    render(<ToolCard {...mockTool} />)
    
    // For rating 4.5, we should have 4 full stars and 1 empty star
    const stars = screen.getAllByTestId(/star/i)
    expect(stars).toHaveLength(5)
  })

  it('renders view details link correctly', () => {
    render(<ToolCard {...mockTool} />)
    
    const link = screen.getByRole('link', { name: /view details/i })
    expect(link).toHaveAttribute('href', '/tools/1')
  })
}) 