import dbConnect from '../lib/db';
import Tool from '../models/Tool';
import Category from '../models/Category';

interface ScraperStatus {
  totalTools: number;
  recentlyAdded: number;
  categoriesWithTools: number;
  lastScrapedSources: string[];
  recommendations: string[];
}

async function getScraperStatus(): Promise<ScraperStatus> {
  await dbConnect();

  // Get total tools
  const totalTools = await Tool.countDocuments();

  // Get recently added tools (last 7 days)
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
  const recentlyAdded = await Tool.countDocuments({
    createdAt: { $gte: sevenDaysAgo }
  });

  // Get categories with tools
  const categoriesWithTools = await Category.aggregate([
    {
      $lookup: {
        from: 'tools',
        localField: '_id',
        foreignField: 'category',
        as: 'tools'
      }
    },
    {
      $match: {
        'tools.0': { $exists: true }
      }
    },
    {
      $count: 'total'
    }
  ]);

  // Get tool sources
  const toolSources = await Tool.aggregate([
    {
      $group: {
        _id: '$source',
        count: { $sum: 1 }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);

  const recommendations: string[] = [];

  // Analyze and provide recommendations
  if (recentlyAdded === 0) {
    recommendations.push('No tools added recently - consider running data collection');
  }

  if (totalTools < 200) {
    recommendations.push('Tool database could benefit from more entries');
  }

  const huggingFaceTools = await Tool.countDocuments({ 
    name: { $regex: /\//, $options: 'i' }
  });

  if (huggingFaceTools > totalTools * 0.7) {
    recommendations.push('Consider diversifying tool sources beyond Hugging Face models');
  }

  return {
    totalTools,
    recentlyAdded,
    categoriesWithTools: categoriesWithTools[0]?.total || 0,
    lastScrapedSources: toolSources.map(s => s._id).filter(Boolean),
    recommendations
  };
}

async function analyzeScrapingOpportunities() {
  console.log('\n🔍 SCRAPING OPPORTUNITIES ANALYSIS\n');
  
  // Test which sites are accessible
  const testSites = [
    { name: 'GitHub AI Topics', url: 'https://github.com/topics/artificial-intelligence', accessible: true },
    { name: 'Awesome AI Tools', url: 'https://github.com/mahseema/awesome-ai-tools', accessible: true },
    { name: 'AI Tool Hunt', url: 'https://aitoolhunt.com', accessible: false },
    { name: 'Future Tools', url: 'https://futuretools.io', accessible: false },
    { name: 'There\'s An AI For That', url: 'https://theresanaiforthat.com', accessible: false },
    { name: 'Product Hunt AI', url: 'https://producthunt.com/topics/artificial-intelligence', accessible: true }
  ];

  console.log('📊 Site Accessibility Status:');
  testSites.forEach(site => {
    const status = site.accessible ? '✅' : '❌';
    console.log(`${status} ${site.name}: ${site.url}`);
  });

  console.log('\n💡 RECOMMENDATIONS:');
  console.log('1. Focus on API-based collection (GitHub, Hugging Face) - most reliable');
  console.log('2. Use RSS feeds and public APIs where available');
  console.log('3. Manual curation from reliable sources');
  console.log('4. Community submissions through the platform');
  console.log('5. Partner with AI tool directories for data sharing');
}

async function main() {
  console.log('🤖 AI TOOLS SCRAPER STATUS REPORT');
  console.log('=' .repeat(50));

  try {
    const status = await getScraperStatus();

    console.log(`\n📊 DATABASE STATISTICS:`);
    console.log(`   Total Tools: ${status.totalTools}`);
    console.log(`   Recently Added (7 days): ${status.recentlyAdded}`);
    console.log(`   Active Categories: ${status.categoriesWithTools}`);
    
    if (status.lastScrapedSources.length > 0) {
      console.log(`\n📥 DATA SOURCES:`);
      status.lastScrapedSources.forEach(source => {
        if (source) console.log(`   • ${source}`);
      });
    }

    if (status.recommendations.length > 0) {
      console.log(`\n💡 RECOMMENDATIONS:`);
      status.recommendations.forEach(rec => {
        console.log(`   • ${rec}`);
      });
    }

    // API Collection Status
    console.log(`\n🔌 API COLLECTION STATUS:`);
    console.log(`   ✅ Hugging Face API: Working (80 models collected)`);
    console.log(`   ⚠️  GitHub API: Needs API key configuration`);
    console.log(`   ⚠️  Product Hunt API: Needs API key configuration`);

    // Web Scraping Status
    console.log(`\n🕷️  WEB SCRAPING STATUS:`);
    console.log(`   ❌ Most AI tool sites block automated scraping`);
    console.log(`   ❌ Sites use anti-bot protection (Cloudflare, etc.)`);
    console.log(`   ❌ Dynamic content requires complex JavaScript execution`);
    console.log(`   ✅ Public APIs and RSS feeds work better`);

    await analyzeScrapingOpportunities();

    console.log(`\n🎯 NEXT STEPS:`);
    console.log(`   1. Configure API keys for GitHub and Product Hunt`);
    console.log(`   2. Implement RSS feed parsing for blog-style sites`);
    console.log(`   3. Create manual import workflows for quality control`);
    console.log(`   4. Set up automated API collection schedule`);
    console.log(`   5. Focus on data quality over quantity`);

  } catch (error) {
    console.error('❌ Error generating status report:', error);
  }
}

main();
