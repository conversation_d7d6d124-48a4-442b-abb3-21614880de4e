// Test MongoDB connection
// Run this with: node test-mongodb-connection.js

const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' });

async function testConnection() {
  try {
    console.log('🔄 Testing MongoDB connection...');
    console.log(`📍 Connecting to: ${process.env.MONGODB_URI ? 'MongoDB URI found' : 'No MongoDB URI found'}`);
    
    if (!process.env.MONGODB_URI) {
      throw new Error('MONGODB_URI not found in .env.local file');
    }
    
    // Hide sensitive connection details in logs
    const safeuRI = process.env.MONGODB_URI.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@');
    console.log(`🔗 URI: ${safeuRI}`);
    
    await mongoose.connect(process.env.MONGODB_URI, {
      bufferCommands: false,
    });
    
    console.log('✅ MongoDB connected successfully!');
    console.log(`📍 Connected to: ${mongoose.connection.host}`);
    console.log(`🏷️  Database: ${mongoose.connection.name}`);
    console.log(`📊 Connection state: ${mongoose.connection.readyState === 1 ? 'Connected' : 'Not connected'}`);
    
    // Test a simple operation
    try {
      const collections = await mongoose.connection.db.listCollections().toArray();
      console.log(`📁 Available collections: ${collections.map(c => c.name).join(', ') || 'None yet (will be created when data is added)'}`);
    } catch (listError) {
      console.log('📁 Collections: Unable to list (this is normal for new databases)');
    }
    
    // Test creating a simple document
    const testSchema = new mongoose.Schema({ test: String, createdAt: Date });
    const TestModel = mongoose.model('connectiontest', testSchema);
    
    const testDoc = new TestModel({
      test: 'MongoDB connection successful!',
      createdAt: new Date()
    });
    
    await testDoc.save();
    console.log('✅ Test document created successfully!');
    
    // Clean up test document
    await TestModel.deleteOne({ _id: testDoc._id });
    console.log('🧹 Test document cleaned up');
    
    console.log('\n🎉 MongoDB is ready for development!');
    
  } catch (error) {
    console.error('❌ MongoDB connection failed:');
    console.error(error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 Solutions for local MongoDB:');
      console.log('1. Make sure MongoDB is installed and running locally');
      console.log('2. Start MongoDB with: mongod');
      console.log('3. Or use MongoDB Atlas (cloud) instead - see MONGODB_SETUP_GUIDE.md');
    } else if (error.message.includes('authentication failed')) {
      console.log('\n💡 Authentication error:');
      console.log('1. Check your username and password in the connection string');
      console.log('2. Make sure the user has proper permissions');
    } else if (error.message.includes('MONGODB_URI')) {
      console.log('\n💡 Configuration error:');
      console.log('1. Make sure .env.local file exists');
      console.log('2. Add MONGODB_URI to your .env.local file');
      console.log('3. See MONGODB_SETUP_GUIDE.md for instructions');
    }
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

testConnection();
