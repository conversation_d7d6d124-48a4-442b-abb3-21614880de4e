import cheerio from 'cheerio';
import dbConnect from '../lib/db';
import Tool from '../models/Tool';
import Category from '../models/Category';

interface SimpleTool {
  name: string;
  description: string;
  url?: string;
  category?: string;
}

async function scrapeFromHTML(html: string, url: string): Promise<SimpleTool[]> {
  const $ = cheerio.load(html);
  const tools: SimpleTool[] = [];

  // Generic selectors that work on many sites
  const containerSelectors = [
    'article', '.card', '.tool', '.product', 
    '.item', '.post', '[class*="tool"]', '[class*="product"]'
  ];

  const titleSelectors = [
    'h1', 'h2', 'h3', 'h4', '.title', '.name', 
    '[class*="title"]', '[class*="name"]'
  ];

  const descSelectors = [
    'p', '.description', '.excerpt', '.summary',
    '[class*="description"]', '[class*="excerpt"]'
  ];

  for (const containerSel of containerSelectors) {
    $(containerSel).each((_, element) => {
      const $el = $(element);
      
      let name = '';
      let description = '';

      // Try to find title
      for (const titleSel of titleSelectors) {
        const title = $el.find(titleSel).first().text().trim();
        if (title && title.length > 2 && title.length < 100) {
          name = title;
          break;
        }
      }

      // Try to find description
      for (const descSel of descSelectors) {
        const desc = $el.find(descSel).first().text().trim();
        if (desc && desc.length > 20 && desc.length < 500) {
          description = desc;
          break;
        }
      }

      if (name && description) {
        // Check for duplicates in current batch
        const isDuplicate = tools.some(tool => 
          tool.name.toLowerCase() === name.toLowerCase()
        );

        if (!isDuplicate) {
          tools.push({
            name,
            description,
            url: $el.find('a').first().attr('href') || url
          });
        }
      }
    });

    // If we found tools with this selector, stop trying others
    if (tools.length > 0) break;
  }

  return tools;
}

async function testScrapeWebsite(url: string): Promise<void> {
  try {
    console.log(`🔍 Testing scrape of: ${url}`);
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    if (!response.ok) {
      console.log(`❌ Failed to fetch ${url}: ${response.status}`);
      return;
    }

    const html = await response.text();
    const tools = await scrapeFromHTML(html, url);

    console.log(`✅ Found ${tools.length} potential tools from ${url}`);
    
    tools.slice(0, 5).forEach((tool, index) => {
      console.log(`${index + 1}. ${tool.name}`);
      console.log(`   ${tool.description.substring(0, 100)}...`);
      console.log(`   URL: ${tool.url}`);
      console.log('');
    });

  } catch (error) {
    console.error(`❌ Error scraping ${url}:`, error);
  }
}

async function main() {
  const testUrls = [
    'https://www.futuretools.io/',
    'https://toolscout.ai/',
    'https://topai.tools/',
    'https://www.marktechpost.com/ai-tool-directory/'
  ];

  for (const url of testUrls) {
    await testScrapeWebsite(url);
    console.log('-'.repeat(50));
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
}

main();
