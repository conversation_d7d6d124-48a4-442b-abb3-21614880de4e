import { MetadataRoute } from 'next'
import { seoConfig } from '@/lib/seo'

export default function robots(): MetadataRoute.Robots {
  const { siteUrl } = seoConfig
  
  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/api/',
          '/admin/',
          '/private/',
          '/blocked',
          '/login',
          '/register',
          '/_next/',
          '/static/',
        ],
      },
      // Block specific bots that might scrape aggressively
      {
        userAgent: [
          'GPTBot',
          'ChatGPT-User',
          'CCBot',
          'anthropic-ai',
          'Claude-Web',
          'scrapy',
          'python-requests',
          'curl',
          'wget',
        ],
        disallow: '/api/',
      },
      // Allow search engine bots full access
      {
        userAgent: [
          'Googlebot',
          'Bingbot',
          'Slurp',
          'DuckDuckBot',
          'Baiduspider',
          'YandexBot',
          'facebookexternalhit',
          'Twitterbot',
          'LinkedInBot',
          'WhatsApp',
          'Applebot',
        ],
        allow: '/',
        disallow: [
          '/api/',
          '/admin/',
          '/private/',
          '/blocked',
        ],
      },
    ],
    sitemap: `${siteUrl}/sitemap.xml`,
    host: siteUrl,
  }
}
