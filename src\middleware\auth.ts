import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import dbConnect from '@/lib/db';
import User from '@/models/User';

export async function authMiddleware(request: NextRequest) {
  try {
    // Get token from header
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return NextResponse.json(
        { error: 'Not authorized, no token' },
        { status: 401 }
      );
    }
    
    // Verify token
    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || 'your-secret-key'
    ) as { id: string };
    
    // Connect to database
    await dbConnect();
    
    // Get user from token
    const user = await User.findById(decoded.id).select('-password');
    
    if (!user) {
      return NextResponse.json(
        { error: 'Not authorized, user not found' },
        { status: 401 }
      );
    }
    
    // Add user to request
    const requestWithUser = request.clone();
    const headers = new Headers(requestWithUser.headers);
    headers.set('x-user-id', user._id.toString());
    headers.set('x-user-role', user.role);
    
    return NextResponse.next({
      request: {
        headers,
      },
    });
  } catch (error) {
    console.error('Auth middleware error:', error);
    return NextResponse.json(
      { error: 'Not authorized, token failed' },
      { status: 401 }
    );
  }
} 