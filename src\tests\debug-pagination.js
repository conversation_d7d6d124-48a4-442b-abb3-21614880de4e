/**
 * Debug script to test API pagination and find duplicate sources
 */

const fetch = require('node-fetch');
const BASE_URL = 'http://localhost:3001';

async function testPagination() {
  console.log('🔍 Testing API pagination for duplicate IDs...\n');
  
  try {
    // Test first few pages to see if there's overlap
    const pages = [1, 2, 3];
    const allIds = new Set();
    const duplicateIds = [];
    
    for (const page of pages) {
      console.log(`📄 Fetching page ${page}...`);
      const response = await fetch(`${BASE_URL}/api/tools?page=${page}&limit=10`);
      const data = await response.json();
      
      console.log(`  - Page ${page}: ${data.tools.length} tools`);
      
      data.tools.forEach((tool, index) => {
        if (allIds.has(tool._id)) {
          duplicateIds.push({
            id: tool._id,
            name: tool.name,
            page: page,
            indexInPage: index
          });
        } else {
          allIds.add(tool._id);
        }
      });
      
      // Show first few IDs from this page
      console.log(`    First 3 IDs: ${data.tools.slice(0, 3).map(t => t._id).join(', ')}`);
    }
    
    console.log(`\n📊 Results:`);
    console.log(`  - Total unique IDs: ${allIds.size}`);
    console.log(`  - Duplicates found: ${duplicateIds.length}`);
    
    if (duplicateIds.length > 0) {
      console.log('\n❌ Duplicate IDs found:');
      duplicateIds.forEach(dup => {
        console.log(`  - ${dup.id} (${dup.name}) on page ${dup.page}, index ${dup.indexInPage}`);
      });
    } else {
      console.log('\n✅ No duplicates in the first 3 pages');
    }
    
    // Test with different sort orders
    console.log('\n🔍 Testing different sort orders...');
    
    const sortOrders = ['rating', 'name'];
    for (const sortBy of sortOrders) {
      console.log(`\n📄 Testing sortBy=${sortBy}:`);
      const response = await fetch(`${BASE_URL}/api/tools?page=1&limit=5&sortBy=${sortBy}`);
      const data = await response.json();
      
      console.log(`  First 5 IDs: ${data.tools.map(t => t._id).join(', ')}`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testPagination()
  .then(() => console.log('\n🎉 Pagination test completed!'))
  .catch(error => console.error('💥 Test failed:', error));
