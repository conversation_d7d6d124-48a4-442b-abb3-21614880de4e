import React from 'react'
import { render, screen, fireEvent, act } from '@testing-library/react'
import ToolsList from './ToolsList'

// Mock setTimeout and clearTimeout
jest.useFakeTimers()

const mockTools = [
  {
    id: '1',
    name: 'ChatGPT',
    description: 'AI-powered chatbot for conversation and assistance',
    imageUrl: '/images/chatgpt.jpg',
    rating: 4.8,
    category: 'Chatbot',
    pricing: 'Freemium',
    url: 'https://chat.openai.com'
  },
  {
    id: '2',
    name: 'Midjourney',
    description: 'AI image generation tool for creating artwork',
    imageUrl: '/images/midjourney.jpg',
    rating: 4.7,
    category: 'Image Generation',
    pricing: 'Paid',
    url: 'https://midjourney.com'
  },
  {
    id: '3',
    name: 'GitHub Copilot',
    description: 'AI pair programmer that helps write better code',
    imageUrl: '/images/github-copilot.jpg',
    rating: 4.5,
    category: 'Code Assistant',
    pricing: 'Paid',
    url: 'https://github.com/features/copilot'
  }
]

describe('ToolsList Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.runOnlyPendingTimers()
  })

  it('renders all tools initially', () => {
    render(<ToolsList tools={mockTools} />)
    
    // Check if all tools are rendered
    expect(screen.getByText('ChatGPT')).toBeInTheDocument()
    expect(screen.getByText('Midjourney')).toBeInTheDocument()
    expect(screen.getByText('GitHub Copilot')).toBeInTheDocument()
  })

  it('filters tools when searching', () => {
    render(<ToolsList tools={mockTools} />)
    
    const searchInput = screen.getByPlaceholderText('Search AI tools by name, description, or category...')
    
    // Search for "ChatGPT"
    fireEvent.change(searchInput, { target: { value: 'ChatGPT' } })
    
    // Fast-forward timers to trigger debounce
    act(() => {
      jest.advanceTimersByTime(300)
    })
    
    // Check if only ChatGPT is rendered
    expect(screen.getByText('ChatGPT')).toBeInTheDocument()
    expect(screen.queryByText('Midjourney')).not.toBeInTheDocument()
    expect(screen.queryByText('GitHub Copilot')).not.toBeInTheDocument()
  })

  it('filters tools by category', () => {
    render(<ToolsList tools={mockTools} />)
    
    // Click on the Image Generation category button
    fireEvent.click(screen.getByText('Image Generation'))
    
    // Check if only Midjourney is rendered
    expect(screen.queryByText('ChatGPT')).not.toBeInTheDocument()
    expect(screen.getByText('Midjourney')).toBeInTheDocument()
    expect(screen.queryByText('GitHub Copilot')).not.toBeInTheDocument()
  })

  it('toggles category filter when clicking the same category twice', () => {
    render(<ToolsList tools={mockTools} />)
    
    // Click on the Chatbot category button
    fireEvent.click(screen.getByText('Chatbot'))
    
    // Check if only ChatGPT is rendered
    expect(screen.getByText('ChatGPT')).toBeInTheDocument()
    expect(screen.queryByText('Midjourney')).not.toBeInTheDocument()
    
    // Click on the Chatbot category button again
    fireEvent.click(screen.getByText('Chatbot'))
    
    // Check if all tools are rendered again
    expect(screen.getByText('ChatGPT')).toBeInTheDocument()
    expect(screen.getByText('Midjourney')).toBeInTheDocument()
    expect(screen.getByText('GitHub Copilot')).toBeInTheDocument()
  })

  it('sorts tools by rating by default', () => {
    render(<ToolsList tools={mockTools} />)
    
    const toolNames = screen.getAllByRole('heading', { level: 3 }).map(h => h.textContent)
    expect(toolNames).toEqual(['ChatGPT', 'Midjourney', 'GitHub Copilot'])
  })

  it('sorts tools by name when clicking the sort button', () => {
    render(<ToolsList tools={mockTools} />)
    
    // Click the sort button to sort by name
    fireEvent.click(screen.getByText('Sort by: Rating'))
    
    const toolNames = screen.getAllByRole('heading', { level: 3 }).map(h => h.textContent)
    expect(toolNames).toEqual(['ChatGPT', 'GitHub Copilot', 'Midjourney'])
  })

  it('shows "No tools found" message when no results match', () => {
    render(<ToolsList tools={mockTools} />)
    
    const searchInput = screen.getByPlaceholderText('Search AI tools by name, description, or category...')
    
    // Search for something that doesn't exist
    fireEvent.change(searchInput, { target: { value: 'NonExistentTool' } })
    
    // Fast-forward timers to trigger debounce
    act(() => {
      jest.advanceTimersByTime(300)
    })
    
    // Check if "No tools found" message is displayed
    expect(screen.getByText('No tools found')).toBeInTheDocument()
    expect(screen.getByText('Try adjusting your search criteria')).toBeInTheDocument()
  })

  it('shows all tools when search is cleared', () => {
    render(<ToolsList tools={mockTools} />)
    
    const searchInput = screen.getByPlaceholderText('Search AI tools by name, description, or category...')
    
    // First search for something
    fireEvent.change(searchInput, { target: { value: 'ChatGPT' } })
    
    // Fast-forward timers to trigger debounce
    act(() => {
      jest.advanceTimersByTime(300)
    })
    
    // Then clear the search
    fireEvent.change(searchInput, { target: { value: '' } })
    
    // Fast-forward timers to trigger debounce
    act(() => {
      jest.advanceTimersByTime(300)
    })
    
    // Check if all tools are rendered again
    expect(screen.getByText('ChatGPT')).toBeInTheDocument()
    expect(screen.getByText('Midjourney')).toBeInTheDocument()
    expect(screen.getByText('GitHub Copilot')).toBeInTheDocument()
  })

  it('combines search and category filters', () => {
    render(<ToolsList tools={mockTools} />)
    
    // Click on the Image Generation category
    fireEvent.click(screen.getByText('Image Generation'))
    
    // Search for "art"
    const searchInput = screen.getByPlaceholderText('Search AI tools by name, description, or category...')
    fireEvent.change(searchInput, { target: { value: 'art' } })
    
    // Fast-forward timers to trigger debounce
    act(() => {
      jest.advanceTimersByTime(300)
    })
    
    // Check if only Midjourney is rendered
    expect(screen.queryByText('ChatGPT')).not.toBeInTheDocument()
    expect(screen.getByText('Midjourney')).toBeInTheDocument()
    expect(screen.queryByText('GitHub Copilot')).not.toBeInTheDocument()
  })
}) 