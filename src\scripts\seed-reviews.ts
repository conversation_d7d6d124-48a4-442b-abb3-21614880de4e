import dbConnect from '../lib/db';
import Review from '../models/Review';
import Tool from '../models/Tool';
import User from '../models/User';

// Define initial reviews
const reviews = [
  {
    toolName: 'ChatGPT',
    userEmail: '<EMAIL>',
    rating: 5,
    title: 'Excellent AI Assistant',
    comment: 'ChatGPT has revolutionized how I work. It helps me with writing, coding, and research tasks. Highly recommended!'
  },
  {
    toolName: 'Midjourney',
    userEmail: '<EMAIL>',
    rating: 4,
    title: 'Great for Creative Projects',
    comment: 'Midjourney produces stunning images from text descriptions. The quality is impressive, though it can be expensive for heavy usage.'
  },
  {
    toolName: 'DALL-E',
    userEmail: '<EMAIL>',
    rating: 4,
    title: 'Versatile Image Generator',
    comment: 'DALL-E is great for creating unique images. The interface is user-friendly and the results are consistently good.'
  },
  {
    toolName: 'Claude',
    userEmail: '<EMAIL>',
    rating: 5,
    title: 'Thoughtful and Helpful',
    comment: '<PERSON> provides detailed and thoughtful responses. It\'s particularly good at analysis and explaining complex topics.'
  }
];

async function seedReviews() {
  try {
    // Connect to database
    await dbConnect();
    console.log('Connected to database');

    // Create new reviews
    for (const reviewData of reviews) {
      // Find tool by name
      const tool = await Tool.findOne({ name: reviewData.toolName });
      
      if (!tool) {
        console.log(`Tool not found: ${reviewData.toolName}`);
        continue;
      }
      
      // Find user by email
      const user = await User.findOne({ email: reviewData.userEmail });
      
      if (!user) {
        console.log(`User not found: ${reviewData.userEmail}`);
        continue;
      }
      
      // Check if review already exists
      const existingReview = await Review.findOne({
        tool: tool._id,
        user: user._id
      });
      
      if (!existingReview) {
        await Review.create({
          tool: tool._id,
          user: user._id,
          rating: reviewData.rating,
          title: reviewData.title,
          comment: reviewData.comment
        });
        
        console.log(`Created review for ${reviewData.toolName} by ${reviewData.userEmail}`);
        
        // Update tool rating
        const allReviews = await Review.find({ tool: tool._id });
        const totalRating = allReviews.reduce((sum, review) => sum + review.rating, 0);
        const averageRating = totalRating / allReviews.length;
        
        await Tool.findByIdAndUpdate(tool._id, {
          rating: averageRating,
          ratingCount: allReviews.length
        });
      } else {
        console.log(`Review already exists for ${reviewData.toolName} by ${reviewData.userEmail}`);
      }
    }

    console.log('Reviews seeded successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding reviews:', error);
    process.exit(1);
  }
}

seedReviews(); 