# Anti-Scraping Implementation Guide

## Overview
This document describes the comprehensive anti-scraping measures implemented to protect the AI Tools Directory from unauthorized data extraction.

## Implemented Protection Layers

### 1. Server-Side Middleware Protection (`middleware.ts`)
- **Bot Detection**: Blocks requests from known bot user agents
- **Rate Limiting**: Limits requests per IP (30 requests/minute)
- **Suspicious Pattern Detection**: Identifies scraping patterns in URLs
- **Security Headers**: Adds protective HTTP headers

### 2. Enhanced API Protection (`src/lib/antiScraping.ts`)
- **Behavioral Analysis**: Scores requests based on suspicious indicators
- **Pattern Recognition**: Detects sequential scraping patterns
- **Request Tracking**: Monitors user behavior across time
- **Dynamic Token Generation**: Creates client tokens for legitimate users

### 3. Client-Side Protection (`src/components/ui/AntiScrapingWrapper.tsx`)
- **Headless Browser Detection**: Identifies automated browsers
- **Automation Tool Detection**: Blocks Selenium, Puppeteer, etc.
- **Human Behavior Verification**: Requires mouse/keyboard interaction
- **Token Management**: Handles client token lifecycle

### 4. Content Obfuscation (`src/components/ui/ObfuscatedContent.tsx`)
- **Dynamic Loading**: Delays content rendering
- **Placeholder Content**: Shows skeleton loaders initially
- **Random Delays**: Adds unpredictability to loading times

### 5. Honeypot Traps
- **Invisible Links**: Hidden links that only bots can access
- **Fake API Endpoints**: `/api/admin/secret` for catching scrapers
- **CSS Traps**: Invisible elements with scraper-bait content

### 6. Infrastructure Protection
- **robots.txt**: Explicit disallow rules for scrapers
- **Meta Tags**: Anti-scraping directives
- **API Limits**: Maximum 50 results per request

## Configuration

### Environment Variables
```bash
NEXT_PUBLIC_APP_URL=http://localhost:3000
ANTI_SCRAPING_ENABLED=true
RATE_LIMIT_REQUESTS=20
RATE_LIMIT_WINDOW_MS=60000
SUSPICIOUS_SCORE_THRESHOLD=50
```

### API Rate Limits
- **Tools API**: 20 requests/minute per IP
- **Categories API**: 20 requests/minute per IP
- **Maximum Results**: 50 items per request

## Detection Mechanisms

### Bot User Agents Blocked
- scrapy, selenium, puppeteer, playwright
- curl, wget, requests, axios
- chrome-headless, phantomjs
- python-requests, node-fetch

### Suspicious Patterns
- High limit parameters (`?limit=1000`)
- Sequential page access
- Missing referer headers
- JSON-only accept headers
- No JavaScript execution indicators

### Behavioral Scoring
- Missing user agent: +30 points
- No referer for API calls: +20 points
- JSON-only accept: +15 points
- Too many endpoints: +25 points
- Sequential patterns: +35 points
- Threshold for blocking: 50 points

## Client Token System

### Token Generation
- Unique tokens created per session
- 1-hour expiration time
- Cryptographic signing for security

### Token Validation
- Required for all API requests
- Validated on each request
- Automatic renewal on client side

## Monitoring and Logging

### Blocked Requests
All blocked requests are logged with:
- IP address
- User agent
- Blocking reason
- Timestamp
- Request details

### Honeypot Monitoring
Honeypot triggers are logged with full request details for analysis.

## Legitimate User Experience

### Minimal Impact
- Loading delays: 0.5-1.5 seconds
- No CAPTCHA requirements
- Transparent token management
- Normal browsing unaffected

### Fallback Handling
- Graceful degradation for JS-disabled browsers
- Error pages for blocked users
- Clear instructions for false positives

## Bypassing Protection (For Development)

### Development Mode
Set `ANTI_SCRAPING_ENABLED=false` in environment variables.

### Testing APIs
Use browser developer tools or include proper headers:
```javascript
fetch('/api/tools', {
  headers: {
    'X-Requested-With': 'XMLHttpRequest',
    'X-Client-Token': 'your-token-here'
  }
})
```

## Maintenance

### Regular Updates
1. Review blocked user agents monthly
2. Update suspicious patterns based on logs
3. Adjust rate limits based on traffic
4. Monitor false positive rates

### Performance Monitoring
- Track response times with protection enabled
- Monitor memory usage of rate limiting maps
- Clean up old tracking data regularly

## Future Enhancements

### Planned Improvements
1. **Redis Integration**: Replace in-memory storage
2. **Machine Learning**: Behavior pattern recognition
3. **CAPTCHA Integration**: For edge cases
4. **Geolocation Blocking**: Country-based restrictions
5. **Device Fingerprinting**: Enhanced bot detection

### Advanced Features
- WebRTC-based detection
- Canvas fingerprinting
- Audio context analysis
- Browser automation detection

## Troubleshooting

### Common Issues

#### False Positives
- Check user agent whitelist
- Reduce suspicious score threshold
- Review rate limit settings

#### Performance Issues
- Implement Redis for production
- Optimize request tracking cleanup
- Use CDN for static content

#### Bypass Attempts
- Monitor honeypot logs
- Update detection patterns
- Implement additional layers

## Compliance and Legal

### GDPR Considerations
- IP addresses are processed for security
- Data retention limited to security needs
- Users can request IP data deletion

### Terms of Service
Update ToS to include anti-scraping clauses and authorized use policies.

## Support

For questions or issues related to anti-scraping measures:
1. Check server logs for blocking reasons
2. Review this documentation
3. Contact development team
4. Report false positives for investigation

---

**Last Updated**: July 2025  
**Version**: 1.0  
**Status**: Production Ready
