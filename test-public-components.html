<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Public Components</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .tool-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
            transition: box-shadow 0.3s ease;
        }
        .tool-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .tool-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        .tool-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }
        .tool-image img.loaded {
            opacity: 1;
        }
        .fallback-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 20px;
            color: #374151;
        }
        .tool-content {
            padding: 16px;
        }
        .tool-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #111827;
        }
        .tool-description {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        .tool-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        .tool-category {
            background: #dbeafe;
            color: #1e40af;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        .tool-pricing {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        .pricing-free { background: #dcfce7; color: #166534; }
        .pricing-freemium { background: #dbeafe; color: #1e40af; }
        .pricing-paid { background: #fed7aa; color: #c2410c; }
        .tool-rating {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #d97706;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .preview-banner {
            background: #dbeafe;
            border: 1px solid #93c5fd;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 20px;
            color: #1e40af;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI Tools Directory - Public Preview Test</h1>
        
        <div class="preview-banner">
            <strong>Preview Mode:</strong> These are sample tools to showcase our directory. 
            <a href="#" style="color: #1e40af; text-decoration: underline;">Subscribe to access 1000+ real AI tools</a>
        </div>
        
        <h2>Featured Tools</h2>
        <div class="grid" id="tools-grid">
            <!-- Tools will be populated by JavaScript -->
        </div>
    </div>

    <script>
        // Mock tools data (same as in the React component)
        const mockTools = [
            {
                name: 'ChatGPT',
                description: 'Advanced AI chatbot for conversations, writing, and problem-solving',
                imageUrl: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=300&h=200&fit=crop&crop=center',
                rating: 4.8,
                category: 'AI Chatbots',
                pricing: 'Freemium'
            },
            {
                name: 'Midjourney',
                description: 'AI-powered image generation tool for creating stunning artwork',
                imageUrl: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=300&h=200&fit=crop&crop=center',
                rating: 4.7,
                category: 'Image Generation',
                pricing: 'Paid'
            },
            {
                name: 'GitHub Copilot',
                description: 'AI pair programmer that helps you write code faster',
                imageUrl: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=300&h=200&fit=crop&crop=center',
                rating: 4.6,
                category: 'Code Generation',
                pricing: 'Paid'
            },
            {
                name: 'Grammarly',
                description: 'AI writing assistant for grammar, spelling, and style',
                imageUrl: 'https://images.unsplash.com/photo-1455390582262-044cdead277a?w=300&h=200&fit=crop&crop=center',
                rating: 4.5,
                category: 'Writing',
                pricing: 'Freemium'
            },
            {
                name: 'Notion AI',
                description: 'AI-powered workspace for notes, docs, and project management',
                imageUrl: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=300&h=200&fit=crop&crop=center',
                rating: 4.4,
                category: 'Productivity',
                pricing: 'Freemium'
            },
            {
                name: 'Canva AI',
                description: 'AI-enhanced design platform for creating graphics and presentations',
                imageUrl: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=300&h=200&fit=crop&crop=center',
                rating: 4.3,
                category: 'Design',
                pricing: 'Freemium'
            }
        ];

        function getPricingClass(pricing) {
            switch(pricing) {
                case 'Free': return 'pricing-free';
                case 'Freemium': return 'pricing-freemium';
                case 'Paid': return 'pricing-paid';
                default: return 'pricing-freemium';
            }
        }

        function createToolCard(tool) {
            return `
                <div class="tool-card">
                    <div class="tool-image">
                        <img src="${tool.imageUrl}" alt="${tool.name}" onload="this.classList.add('loaded')" onerror="handleImageError(this, '${tool.name}')">
                    </div>
                    <div class="tool-content">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                            <h3 class="tool-title">${tool.name}</h3>
                            <div class="tool-rating">
                                ⭐ ${tool.rating}
                            </div>
                        </div>
                        <p class="tool-description">${tool.description}</p>
                        <div class="tool-meta">
                            <span class="tool-category">${tool.category}</span>
                            <span class="tool-pricing ${getPricingClass(tool.pricing)}">${tool.pricing}</span>
                        </div>
                        <button style="width: 100%; padding: 8px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer;">
                            View Details
                        </button>
                    </div>
                </div>
            `;
        }

        function handleImageError(img, toolName) {
            img.style.display = 'none';
            const parent = img.parentElement;
            if (!parent.querySelector('.fallback-content')) {
                const fallback = document.createElement('div');
                fallback.className = 'fallback-content';
                fallback.innerHTML = `
                    <div style="width: 48px; height: 48px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 8px;">
                        ⚡
                    </div>
                    <div style="font-weight: 600;">${toolName}</div>
                `;
                parent.appendChild(fallback);
            }
        }

        // Populate the tools grid
        const toolsGrid = document.getElementById('tools-grid');
        toolsGrid.innerHTML = mockTools.map(tool => createToolCard(tool)).join('');
    </script>
</body>
</html>
