import { SmartDuplicateHandler } from '../services/smartDuplicateHandler';
import DatabaseStatsManager from '../services/databaseStatsManager';

// Test the smart duplicate handler with sample data
const testTools = [
  {
    name: 'ChatGPT Pro',
    description: 'Advanced conversational AI with enhanced capabilities, longer context, and priority access',
    url: 'https://chat.openai.com',
    category: 'language-models',
    source: 'manual-test',
    features: ['Advanced reasoning', 'Priority access', 'Longer conversations'],
    company: 'OpenAI',
    pricing: 'Paid'
  },
  {
    name: 'Claude 3',
    description: 'Anthropics latest AI assistant with improved reasoning, analysis, and creative capabilities',
    url: 'https://claude.ai',
    category: 'language-models', 
    source: 'manual-test',
    features: ['Constitutional AI', 'Long context', 'Safety focused'],
    company: 'Anthropic',
    pricing: 'Freemium'
  },
  {
    name: 'GitHub Copilot Chat',
    description: 'AI-powered coding assistant with chat interface for explaining code and getting programming help',
    url: 'https://github.com/features/copilot',
    category: 'code-generation',
    source: 'manual-test', 
    features: ['Code explanation', 'Inline chat', 'IDE integration'],
    company: 'GitHub',
    pricing: 'Paid'
  }
];

async function testEnhancedSystem() {
  console.log('🧪 TESTING ENHANCED DATA COLLECTION SYSTEM');
  console.log('=' .repeat(50));

  try {
    // Test 1: Smart Duplicate Handler
    console.log('\n🔍 Test 1: Smart Duplicate Detection');
    console.log('-'.repeat(30));
    
    const duplicateHandler = new SmartDuplicateHandler();
    const processResults = await duplicateHandler.batchProcessTools(testTools);
    
    console.log(`✅ Batch processing results:`);
    console.log(`   Created: ${processResults.created}`);
    console.log(`   Updated: ${processResults.updated}`);
    console.log(`   Skipped: ${processResults.skipped}`);

    // Test 2: Database Stats and Maintenance
    console.log('\n📊 Test 2: Database Statistics');
    console.log('-'.repeat(30));
    
    const statsManager = new DatabaseStatsManager();
    const stats = await statsManager.getFullStats();
    
    console.log(`📈 Current Database Status:`);
    console.log(`   Total Tools: ${stats.totalTools}`);
    console.log(`   Active Tools: ${stats.activeTools}`);
    console.log(`   Categories: ${stats.totalCategories}`);
    console.log(`   Categories with Tools: ${stats.categoriesWithTools}`);
    console.log(`   Recently Added: ${stats.recentlyAdded}`);

    // Test 3: Category Updates
    console.log('\n🔄 Test 3: Category Count Updates');
    console.log('-'.repeat(30));
    
    await statsManager.updateCategoryCounts();
    console.log('✅ Category counts updated');

    // Test 4: Cleanup
    console.log('\n🧹 Test 4: Database Cleanup');
    console.log('-'.repeat(30));
    
    const cleanupResult = await statsManager.cleanupDatabase();
    console.log(`✅ Cleanup completed:`);
    console.log(`   Tools removed: ${cleanupResult.removedTools}`);
    console.log(`   Categories updated: ${cleanupResult.updatedCategories}`);

    // Test 5: Generate Report
    console.log('\n📋 Test 5: Detailed Report');
    console.log('-'.repeat(30));
    
    const report = await statsManager.generateReport();
    console.log(report);

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testEnhancedSystem();
